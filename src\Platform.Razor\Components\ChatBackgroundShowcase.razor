@* Chat Background Showcase Component - Nothing Phone Aesthetic *@

<div class="p-6 space-y-8">
    <div class="text-center">
        <h2 class="text-responsive-xl font-bold text-primary mb-2">Chat Background Options</h2>
        <p class="text-responsive-sm text-secondary">SVG-based backgrounds for crisp, scalable chat interfaces</p>
    </div>

    <!-- Background Variants Grid -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        
        <!-- Dynamic Background -->
        <div class="space-y-3">
            <h3 class="text-responsive-base font-semibold text-primary">Dynamic Background</h3>
            <div class="chat-bg-dynamic h-48 rounded-lg border border-border relative overflow-hidden">
                <div class="absolute inset-4 space-y-2">
                    <div class="chat-bubble-sent ml-auto">
                        <p class="text-sm">Perfect for active conversations</p>
                    </div>
                    <div class="chat-bubble-received mr-auto">
                        <p class="text-sm">Organic shapes and flowing lines</p>
                    </div>
                </div>
            </div>
            <p class="text-xs text-secondary">Best for: Group chats, active conversations</p>
        </div>

        <!-- Abstract Background -->
        <div class="space-y-3">
            <h3 class="text-responsive-base font-semibold text-primary">Abstract Background</h3>
            <div class="chat-bg-abstract h-48 rounded-lg border border-border relative overflow-hidden">
                <div class="absolute inset-4 space-y-2">
                    <div class="chat-bubble-sent ml-auto">
                        <p class="text-sm">Geometric patterns</p>
                    </div>
                    <div class="chat-bubble-received mr-auto">
                        <p class="text-sm">Nothing Phone minimalism</p>
                    </div>
                </div>
            </div>
            <p class="text-xs text-secondary">Best for: Professional chats, business contexts</p>
        </div>

        <!-- Minimal Background -->
        <div class="space-y-3">
            <h3 class="text-responsive-base font-semibold text-primary">Minimal Background</h3>
            <div class="chat-bg-minimal h-48 rounded-lg border border-border relative overflow-hidden">
                <div class="absolute inset-4 space-y-2">
                    <div class="chat-bubble-sent ml-auto">
                        <p class="text-sm">Ultra-subtle design</p>
                    </div>
                    <div class="chat-bubble-received mr-auto">
                        <p class="text-sm">Maximum focus on content</p>
                    </div>
                </div>
            </div>
            <p class="text-xs text-secondary">Best for: Private chats, focused reading</p>
        </div>
    </div>

    <!-- Technical Benefits -->
    <div class="bg-surface border border-border rounded-lg p-6">
        <h3 class="text-responsive-base font-semibold text-primary mb-4">SVG Background Benefits</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
                <h4 class="text-sm font-medium text-primary">Visual Quality</h4>
                <ul class="text-xs text-secondary space-y-1">
                    <li>• Crisp at any resolution</li>
                    <li>• No pixelation or blurriness</li>
                    <li>• Perfect for high-DPI displays</li>
                    <li>• Consistent across devices</li>
                </ul>
            </div>
            <div class="space-y-2">
                <h4 class="text-sm font-medium text-primary">Performance</h4>
                <ul class="text-xs text-secondary space-y-1">
                    <li>• Smaller file sizes than PNG</li>
                    <li>• Faster loading times</li>
                    <li>• Better caching efficiency</li>
                    <li>• Reduced bandwidth usage</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Usage Examples -->
    <div class="bg-surface border border-border rounded-lg p-6">
        <h3 class="text-responsive-base font-semibold text-primary mb-4">CSS Usage</h3>
        <div class="space-y-4">
            <div>
                <h4 class="text-sm font-medium text-primary mb-2">Basic Implementation</h4>
                <pre class="bg-muted p-3 rounded text-xs text-secondary overflow-x-auto"><code>&lt;div class="chat-bg-dynamic"&gt;
  &lt;!-- Chat messages --&gt;
&lt;/div&gt;</code></pre>
            </div>
            <div>
                <h4 class="text-sm font-medium text-primary mb-2">Context-Specific Variants</h4>
                <pre class="bg-muted p-3 rounded text-xs text-secondary overflow-x-auto"><code>/* Conversation types */
.chat-bg-conversation  /* Dynamic background */
.chat-bg-group        /* Abstract background */
.chat-bg-private      /* Minimal background */</code></pre>
            </div>
        </div>
    </div>
</div>

@code {
    // This component is for documentation and testing purposes
    // It showcases the different SVG chat backgrounds available
}
