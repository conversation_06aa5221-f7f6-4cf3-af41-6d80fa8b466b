# Authentication System Razor Components

A comprehensive authentication system built with Blazor and styled with Tailwind CSS. This system provides secure user authentication with modern UI/UX patterns, comprehensive validation, and full hybrid app functionality.

## Components Overview

### 1. SignInForm Component
**Location**: `Platform.Razor/Features/Authentication/SignIn/SignInForm.razor`

A modern sign-in component with email/password authentication and social login options.

#### Features
- **Email/Username Authentication**: Support for both email and username login
- **Password Visibility Toggle**: Show/hide password functionality
- **Remember Me**: Persistent login option with secure token storage
- **Social Login**: Google OAuth integration (extensible for other providers)
- **Form Validation**: Real-time client-side and server-side validation
- **Error Handling**: Comprehensive error states with user-friendly messages
- **Loading States**: Visual feedback during authentication process
- **Responsive Design**: Mobile-first approach with touch-optimized inputs

#### Usage
```razor
@page "/signin"
<SignInForm />
```

### 2. SignUpForm Component
**Location**: `Platform.Razor/Features/Authentication/SignUp/SignUpForm.razor`

A comprehensive registration component with advanced validation and user experience features.

#### Features
- **Profile Picture Upload**: Image upload with preview and validation
- **Real-time Password Strength**: Visual password strength indicator
- **Email Availability Check**: Debounced email availability validation
- **Password Confirmation**: Real-time password matching validation
- **Terms & Privacy**: Required acceptance of terms and privacy policy
- **Form Validation**: Comprehensive client-side and server-side validation
- **Responsive Layout**: Mobile-optimized form with proper field grouping
- **Error Handling**: Detailed error messages and recovery options

#### Usage
```razor
@page "/signup"
<SignUpForm />
```

### 3. ForgotPasswordForm Component
**Location**: `Platform.Razor/Features/Authentication/ForgotPassword/ForgotPasswordForm.razor`

A password reset request component with email verification flow.

#### Features
- **Email Validation**: Real-time email format validation
- **Success States**: Clear confirmation of email sent
- **Resend Functionality**: Cooldown timer for resend attempts
- **Error Handling**: Comprehensive error states and recovery
- **User Guidance**: Clear instructions and troubleshooting tips
- **Responsive Design**: Mobile-friendly layout

#### Usage
```razor
@page "/forgot-password"
<ForgotPasswordForm />
```

### 4. ResetPasswordForm Component
**Location**: `Platform.Razor/Features/Authentication/ResetPassword/ResetPasswordForm.razor`

A secure password reset component with token validation and strength requirements.

#### Features
- **Token Validation**: Secure reset token verification
- **Password Strength**: Real-time strength indicator and requirements
- **Password Confirmation**: Real-time matching validation
- **Security Features**: Password visibility toggles and secure handling
- **Success Flow**: Clear confirmation and navigation to sign-in
- **Error Handling**: Invalid token handling and error recovery

#### Usage
```razor
@page "/reset-password/{Token}"
<ResetPasswordForm />
```

## Architecture

### Component Inheritance
Authentication components follow the established base class patterns:

- **SignInForm**: Inherits from `FormBase<SigninFormBusinessObject, SigninFormViewModel, string, ISigninFormDataService>`
- **SignUpForm**: Inherits from `FormBase<SignupFormBusinessObject, SignupFormViewModel, string, ISignupFormDataService>`

### Enhanced ViewModels
The ViewModels have been enhanced with modern authentication features:

#### SigninFormViewModel
- Email/username support with backward compatibility
- Password visibility toggle
- Remember me functionality
- Form validation with user-friendly error messages
- Device string generation for security tracking

#### SignupFormViewModel
- Comprehensive user information fields (first name, last name, email)
- Real-time password strength calculation
- Password confirmation with matching validation
- Terms and privacy policy acceptance
- Profile picture URL support
- Email availability checking

### Data Flow
1. **ViewModels**: Enhanced in `Platform.Client.Services.Features.Account`
2. **Business Objects**: Located in `DeepMessage.ServiceContracts.Features.Account`
3. **Data Services**: Both server-side and client-side implementations
4. **Token Management**: Secure JWT token storage and management

## File Structure
```
Platform.Razor/Features/Authentication/
├── SignIn/
│   ├── SignInForm.razor                  # Sign-in component
│   └── SignInForm.razor.cs               # Component logic
├── SignUp/
│   ├── SignUpForm.razor                  # Sign-up component
│   └── SignUpForm.razor.cs               # Component logic
├── ForgotPassword/
│   └── ForgotPasswordForm.razor          # Password reset request
├── ResetPassword/
│   └── ResetPasswordForm.razor           # Password reset form
├── Components/                           # Shared authentication components
└── README.md                             # This documentation
```

## Features in Detail

### Security Features
- **Password Strength Validation**: Real-time strength calculation with visual indicators
- **Secure Token Storage**: JWT tokens stored securely in browser storage
- **Device Tracking**: Device fingerprinting for security monitoring
- **Input Sanitization**: XSS prevention and input validation
- **CSRF Protection**: Built-in CSRF protection for form submissions
- **Rate Limiting**: Protection against brute force attacks

### User Experience Features
- **Real-time Validation**: Immediate feedback on form inputs
- **Password Visibility**: Toggle password visibility for better UX
- **Remember Me**: Persistent login with secure token management
- **Email Availability**: Real-time email availability checking
- **Profile Pictures**: Image upload with preview and validation
- **Loading States**: Visual feedback during all operations
- **Error Recovery**: Clear error messages with recovery options

### Responsive Design
- **Mobile-first**: Optimized for mobile devices with touch-friendly inputs
- **Adaptive Layout**: Responsive grid layouts that adapt to screen size
- **Touch Targets**: Minimum 44px touch targets for accessibility
- **Virtual Keyboard**: Proper input types for mobile keyboards
- **Orientation Support**: Works in both portrait and landscape modes

## Styling

### Tailwind CSS Classes
The components use comprehensive Tailwind CSS classes for:
- **Layout**: Flexbox and grid layouts for responsive design
- **Colors**: Consistent color scheme with dark mode support
- **Typography**: Responsive text sizing and weights
- **Forms**: Modern form styling with focus states
- **Buttons**: Interactive button states with loading indicators
- **Animations**: Smooth transitions and loading animations

### Form Styling
- **Input Fields**: Consistent styling with icons and validation states
- **Validation Messages**: Color-coded validation feedback
- **Password Strength**: Visual strength indicators with color coding
- **File Upload**: Custom file upload styling with preview
- **Checkboxes**: Custom checkbox styling for terms acceptance

## Integration

### Authentication Flow
1. **Sign In**: Email/password or social login → JWT token → redirect to dashboard
2. **Sign Up**: Registration → email verification → account activation
3. **Password Reset**: Email request → token validation → password update
4. **Token Management**: Automatic token refresh and secure storage

### Navigation Routes
- **Sign In**: `/signin`, `/login`
- **Sign Up**: `/signup`, `/register`
- **Forgot Password**: `/forgot-password`
- **Reset Password**: `/reset-password/{token}`
- **Email Verification**: `/verify-email/{token}`

### Service Integration
- **Authentication Services**: Integration with existing auth services
- **Email Services**: Password reset and verification emails
- **File Upload**: Profile picture upload to cloud storage
- **Social Providers**: OAuth integration with Google, Facebook, Apple

## Validation

### Client-side Validation
- **Data Annotations**: Comprehensive validation attributes
- **Real-time Feedback**: Immediate validation on input change
- **Password Strength**: Real-time strength calculation
- **Email Format**: Email format validation with regex
- **File Validation**: Image type and size validation

### Server-side Validation
- **Business Logic**: Server-side validation in data services
- **Security Checks**: Additional security validation
- **Database Constraints**: Unique email and username validation
- **Rate Limiting**: Server-side rate limiting for security

## Security Considerations

### Password Security
- **Strength Requirements**: Minimum 8 characters with complexity rules
- **Hashing**: Secure password hashing on server-side
- **Salt**: Unique salt for each password hash
- **History**: Password history to prevent reuse

### Token Security
- **JWT Tokens**: Secure JWT token implementation
- **Refresh Tokens**: Automatic token refresh mechanism
- **Expiration**: Configurable token expiration times
- **Revocation**: Token revocation for security incidents

### Data Protection
- **HTTPS**: All authentication over HTTPS
- **Input Sanitization**: XSS and injection prevention
- **CSRF Protection**: Cross-site request forgery protection
- **Rate Limiting**: Protection against brute force attacks

## Accessibility

### WCAG Compliance
- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Focus Management**: Clear focus indicators and logical tab order
- **Color Contrast**: WCAG AA compliant color combinations
- **Error Announcements**: Screen reader announcements for errors

### Form Accessibility
- **Label Association**: Proper label-input associations
- **Error Identification**: Clear error identification and description
- **Required Fields**: Clear indication of required fields
- **Help Text**: Descriptive help text for complex fields

## Browser Support
- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile Browsers**: iOS Safari, Chrome Mobile, Samsung Internet
- **JavaScript Required**: Components require JavaScript for full functionality
- **Progressive Enhancement**: Basic functionality without JavaScript

## Performance

### Optimization Features
- **Lazy Loading**: Components load only when needed
- **Debounced Validation**: Prevents excessive API calls
- **Image Optimization**: Automatic image resizing and compression
- **Bundle Splitting**: Separate bundles for authentication components
- **Caching**: Appropriate caching strategies for static assets

### Loading States
- **Form Submission**: Loading indicators during form submission
- **Email Checking**: Loading states for email availability
- **File Upload**: Progress indicators for file uploads
- **Token Validation**: Loading states for token verification

## Customization

### Styling Customization
1. **Tailwind Configuration**: Modify Tailwind config for global changes
2. **CSS Variables**: Use CSS custom properties for theme customization
3. **Component Overrides**: Override specific component styles

### Functionality Customization
1. **Validation Rules**: Customize validation rules and messages
2. **Social Providers**: Add additional OAuth providers
3. **Email Templates**: Customize email templates for notifications
4. **Redirect URLs**: Configure post-authentication redirect URLs

## Testing

### Unit Testing
- **Component Testing**: Test component rendering and interactions
- **Validation Testing**: Test form validation logic
- **Service Testing**: Test authentication service integration
- **Error Handling**: Test error scenarios and recovery

### Integration Testing
- **Authentication Flow**: End-to-end authentication testing
- **Social Login**: Test OAuth provider integration
- **Email Flow**: Test password reset and verification emails
- **Security Testing**: Test security features and vulnerabilities

## Deployment

### Environment Configuration
- **API Endpoints**: Configure authentication API endpoints
- **OAuth Settings**: Configure social login provider settings
- **Email Settings**: Configure email service settings
- **Security Settings**: Configure security parameters

### Production Considerations
- **HTTPS**: Ensure HTTPS in production
- **Rate Limiting**: Configure appropriate rate limits
- **Monitoring**: Set up authentication monitoring and alerts
- **Backup**: Implement user data backup strategies

## Troubleshooting

### Common Issues
1. **Login Failures**: Check credentials and account status
2. **Email Not Received**: Check spam folder and email configuration
3. **Token Expired**: Handle token expiration gracefully
4. **Social Login Issues**: Verify OAuth provider configuration
5. **Validation Errors**: Check validation rules and messages

### Debug Tips
1. **Browser DevTools**: Use for CSS and JavaScript debugging
2. **Network Tab**: Monitor API calls and responses
3. **Console Logs**: Check for JavaScript errors and warnings
4. **Authentication State**: Monitor authentication state changes
