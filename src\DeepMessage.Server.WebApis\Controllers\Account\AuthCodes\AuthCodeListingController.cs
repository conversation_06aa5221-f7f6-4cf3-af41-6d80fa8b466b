﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.AuthCodes;
namespace DeepMessage.Server.WebApis.Controller.AuthCodes;
[ApiController, Route("api/[controller]/[action]")]
public class AuthCodeListingController : ControllerBase, IAuthCodeListingDataService
{

	private readonly IAuthCodeListingDataService dataService;

	public AuthCodeListingController(IAuthCodeListingDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<PagedDataList<AuthCodeListingBusinessObject>> GetPaginatedItems([FromQuery] AuthCodeFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
