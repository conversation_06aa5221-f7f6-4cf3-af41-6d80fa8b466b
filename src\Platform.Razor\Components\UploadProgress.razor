@* Upload Progress Component for file uploads *@
<div class="upload-progress-container @(IsVisible ? "visible" : "hidden")">
    @if (ShowAsRing)
    {
        <!-- Circular Progress Ring -->
        <div class="progress-ring-container">
            <svg class="progress-ring" width="@Size" height="@Size">
                <circle class="progress-ring-background"
                        cx="@(Size/2)" cy="@(Size/2)" r="@Radius"
                        stroke-width="@StrokeWidth" />
                <circle class="progress-ring-progress"
                        cx="@(Size/2)" cy="@(Size/2)" r="@Radius"
                        stroke-width="@StrokeWidth"
                        stroke-dasharray="@Circumference"
                        stroke-dashoffset="@StrokeDashOffset"
                        transform="rotate(-90 @(Size/2) @(Size/2))" />
            </svg>
            @if (ShowPercentage)
            {
                <div class="progress-text">@Progress%</div>
            }
        </div>
    }
    else
    {
        <!-- Linear Progress Bar -->
        <div class="progress-bar-container">
            @if (ShowLabel && !string.IsNullOrEmpty(Label))
            {
                <div class="progress-label">@Label</div>
            }
            <div class="progress-bar">
                <div class="progress-bar-fill" style="width: @Progress%"></div>
            </div>
            @if (ShowPercentage)
            {
                <div class="progress-percentage">@Progress%</div>
            }
        </div>
    }

    @if (ShowFileInfo && !string.IsNullOrEmpty(FileName))
    {
        <div class="file-info">
            <span class="file-name">@FileName</span>
            @if (FileSizeBytes.HasValue)
            {
                <span class="file-size">(@FormatFileSize(FileSizeBytes.Value))</span>
            }
        </div>
    }

    @if (!string.IsNullOrEmpty(ErrorMessage))
    {
        <div class="error-message">
            <svg class="error-icon" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            @ErrorMessage
        </div>
    }

    @if (ShowRetryButton && !string.IsNullOrEmpty(ErrorMessage))
    {
        <button class="retry-button" @onclick="OnRetryClick">
            <svg class="retry-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Retry
        </button>
    }
</div>

<style>
    .upload-progress-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        transition: opacity 0.3s ease;
    }

    .upload-progress-container.visible {
        opacity: 1;
    }

    .upload-progress-container.hidden {
        opacity: 0;
        pointer-events: none;
    }

    /* Circular Progress Ring */
    .progress-ring-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .progress-ring {
        transform: rotate(-90deg);
    }

    .progress-ring-background {
        fill: none;
        stroke: rgba(0, 0, 0, 0.1);
    }

    .progress-ring-progress {
        fill: none;
        stroke: #3b82f6; /* blue-500 */
        stroke-linecap: round;
        transition: stroke-dashoffset 0.3s ease;
    }

    .progress-text {
        position: absolute;
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151; /* gray-700 */
    }

    /* Linear Progress Bar */
    .progress-bar-container {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        width: 100%;
        min-width: 12rem;
    }

    .progress-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151; /* gray-700 */
    }

    .progress-bar {
        width: 100%;
        height: 0.5rem;
        background-color: #e5e7eb; /* gray-200 */
        border-radius: 0.25rem;
        overflow: hidden;
    }

    .progress-bar-fill {
        height: 100%;
        background-color: #3b82f6; /* blue-500 */
        border-radius: 0.25rem;
        transition: width 0.3s ease;
        background-image: linear-gradient(
            45deg,
            rgba(255, 255, 255, 0.2) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0.2) 75%,
            transparent 75%,
            transparent
        );
        background-size: 1rem 1rem;
    }



    .progress-percentage {
        font-size: 0.75rem;
        font-weight: 600;
        color: #6b7280; /* gray-500 */
        text-align: center;
    }

    /* File Info */
    .file-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
        text-align: center;
    }

    .file-name {
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151; /* gray-700 */
        max-width: 12rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .file-size {
        font-size: 0.75rem;
        color: #6b7280; /* gray-500 */
    }

    /* Error Message */
    .error-message {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background-color: #fef2f2; /* red-50 */
        border: 1px solid #fecaca; /* red-200 */
        border-radius: 0.375rem;
        color: #dc2626; /* red-600 */
        font-size: 0.875rem;
    }

    .error-icon {
        width: 1rem;
        height: 1rem;
        flex-shrink: 0;
    }

    /* Retry Button */
    .retry-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background-color: #3b82f6; /* blue-500 */
        color: white;
        border: none;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .retry-button:hover {
        background-color: #2563eb; /* blue-600 */
    }

    .retry-icon {
        width: 1rem;
        height: 1rem;
    }

    /* Dark mode adjustments - simplified for Razor compatibility */
</style>

@code {
    [Parameter] public bool IsVisible { get; set; } = true;
    [Parameter] public int Progress { get; set; } = 0;
    [Parameter] public bool ShowAsRing { get; set; } = false;
    [Parameter] public bool ShowPercentage { get; set; } = true;
    [Parameter] public bool ShowLabel { get; set; } = true;
    [Parameter] public bool ShowFileInfo { get; set; } = false;
    [Parameter] public bool ShowRetryButton { get; set; } = false;
    [Parameter] public string? Label { get; set; }
    [Parameter] public string? FileName { get; set; }
    [Parameter] public long? FileSizeBytes { get; set; }
    [Parameter] public string? ErrorMessage { get; set; }
    [Parameter] public EventCallback OnRetry { get; set; }

    // Ring-specific parameters
    [Parameter] public int Size { get; set; } = 48;
    [Parameter] public int StrokeWidth { get; set; } = 4;

    private int Radius => (Size - StrokeWidth) / 2;
    private double Circumference => 2 * Math.PI * Radius;
    private double StrokeDashOffset => Circumference - (Progress / 100.0 * Circumference);

    private async Task OnRetryClick()
    {
        await OnRetry.InvokeAsync();
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.#} {sizes[order]}";
    }
}
