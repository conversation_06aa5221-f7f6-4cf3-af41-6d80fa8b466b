﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Friends;
using System.Security.Claims;
using DeepMessage.Cient.Common.Data;
using DeepMessage.Client.Common.Data;
using DeepMessage.MauiApp.Helpers;
using Platform.Framework.Core;
namespace Platform.Client.Services.Features.Friends;
public class FriendsClientSideListingDataService : IFriendsListingDataService
{

	private readonly BaseHttpClient _httpClient;

	public FriendsClientSideListingDataService (BaseHttpClient httpClient)
	{
		_httpClient = httpClient;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<PagedDataList<FriendsListingBusinessObject>> GetPaginatedItems(FriendsFilterBusinessObject filterBusinessObject)
	{
		return await _httpClient.GetFromJsonAsync<PagedDataList<FriendsListingBusinessObject>>($"api/FriendsListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());
	}
}

public class FriendsOfflineListingDataService : ClientSideListingDataService<FriendsListingBusinessObject, FriendsFilterBusinessObject>, IFriendsListingDataService
{ 
    private readonly AppDbContext _context;
    private readonly ILocalStorageService localStorage;

    public FriendsOfflineListingDataService( AppDbContext context, ILocalStorageService localStorage)
    { 
        _context = context;
        this.localStorage = localStorage;
    } 

    public override IQueryable<FriendsListingBusinessObject> GetQuery(FriendsFilterBusinessObject filterBusinessObject)
    {
        var userId = Task.Run(() => (localStorage.GetValue(ClaimTypes.NameIdentifier))).Result;

        var query = (from f in _context.Friendships
                     where f.UserId == userId
                     select new FriendsListingBusinessObject
                     {
                         Id = f.Id,
                         FriendId = f.FriendId,
                         AvatarData = f.AvatarData,
                         Name = f.Name,
                         TagLine = f.TagLine,  
                         Pub1 = f.Pub1  
                     });

        // Apply search filter if SearchKey is provided
        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            var searchTerm = filterBusinessObject.SearchKey.ToLower();
            query = query.Where(friend =>
                friend.Name.ToLower().Contains(searchTerm)
            );
        }

        // Order by name for consistent results
        return query.OrderBy(friend => friend.Name);
    }

}
