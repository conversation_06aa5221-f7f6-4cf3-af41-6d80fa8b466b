@using Microsoft.Extensions.Logging
@using Microsoft.AspNetCore.Components.Web
@using Platform.Razor.Components.ErrorBoundary
@inject ILogger<ComponentErrorBoundary> Logger

<!-- Component-Level Error Boundary - Inline Error Display -->
<ErrorBoundary @ref="errorBoundary">
    <ChildContent>
        @ChildContent
    </ChildContent>
    <ErrorContent Context="exception">
        <div class="@ContainerClasses">
            <div class="bg-secondary-50 border border-secondary-200 rounded-lg p-4">

                <!-- Error Icon and Title -->
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-secondary-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-3 flex-1">
                        <h3 class="text-sm font-semibold text-secondary-800 mb-2">
                            @GetErrorTitle()
                        </h3>

                        <!-- Error Message -->
                        <div class="text-sm text-secondary-700 mb-3">
                            @GetErrorMessage(exception)
                        </div>

                        <!-- Development Details -->
                        @if (!IsProduction && ShowDetails)
                        {
                            <details class="mb-3">
                                <summary class="cursor-pointer text-xs text-secondary-600 hover:underline transition-theme">
                                    Technical Details
                                </summary>
                                <div class="mt-2 p-2 bg-secondary-100 rounded text-xs font-mono">
                                    <p class="font-semibold">@exception.GetType().Name</p>
                                    <p class="mt-1 text-secondary-600">@exception.Message</p>
                                </div>
                            </details>
                        }

                        <!-- Action Buttons -->
                        <div class="flex flex-wrap gap-2">
                            <button @onclick="RetryComponent"
                                    class="btn-secondary btn-xs">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Retry
                            </button>

                            @if (ShowDismiss)
                            {
                                <button @onclick="DismissError" 
                                        class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    Dismiss
                                </button>
                            }

                            @if (OnReportError.HasDelegate)
                            {
                                <button @onclick="() => OnReportError.InvokeAsync(exception)" 
                                        class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-900/50 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Report
                                </button>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ErrorContent>
</ErrorBoundary>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public string ComponentName { get; set; } = "Component";
    [Parameter] public string ContainerClasses { get; set; } = "p-4";
    [Parameter] public bool ShowDetails { get; set; } = true;
    [Parameter] public bool ShowDismiss { get; set; } = false;
    [Parameter] public EventCallback OnRetry { get; set; }
    [Parameter] public EventCallback OnDismiss { get; set; }
    [Parameter] public EventCallback<Exception> OnReportError { get; set; }
    [Parameter] public ErrorBoundaryErrorType ErrorType { get; set; } = ErrorBoundaryErrorType.General;

    private ErrorBoundary? errorBoundary;
    private bool IsProduction => Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") != "Development";

    protected override void OnParametersSet()
    {
        // Reset error boundary when parameters change
        errorBoundary?.Recover();
    }

    private string GetErrorTitle()
    {
        return ErrorType switch
        {
            ErrorBoundaryErrorType.Network => "Connection Error",
            ErrorBoundaryErrorType.Authentication => "Authentication Error",
            ErrorBoundaryErrorType.Encryption => "Security Error",
            ErrorBoundaryErrorType.Media => "Media Error",
            ErrorBoundaryErrorType.Chat => "Chat Error",
            ErrorBoundaryErrorType.Dialog => "Dialog Error",
            _ => $"{ComponentName} Error"
        };
    }

    private string GetErrorMessage(Exception exception)
    {
        if (IsProduction)
        {
            return ErrorType switch
            {
                ErrorBoundaryErrorType.Network => "Unable to connect to the server. Please check your internet connection and try again.",
                ErrorBoundaryErrorType.Authentication => "There was a problem with your authentication. Please sign in again.",
                ErrorBoundaryErrorType.Encryption => "There was a security error. Please refresh the page and try again.",
                ErrorBoundaryErrorType.Media => "Unable to process media file. Please try a different file or try again later.",
                ErrorBoundaryErrorType.Chat => "Unable to load messages. Please try again or refresh the page.",
                ErrorBoundaryErrorType.Dialog => "Unable to open dialog. Please try again.",
                _ => "An unexpected error occurred. Please try again."
            };
        }
        else
        {
            return exception.Message;
        }
    }

    private async Task RetryComponent()
    {
        try
        {
            Logger.LogInformation("Retrying component: {ComponentName}, Error Type: {ErrorType}", ComponentName, ErrorType);
            
            errorBoundary?.Recover();
            
            if (OnRetry.HasDelegate)
            {
                await OnRetry.InvokeAsync();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during component retry: {ComponentName}", ComponentName);
        }
    }

    private async Task DismissError()
    {
        try
        {
            Logger.LogInformation("Dismissing error for component: {ComponentName}", ComponentName);
            
            if (OnDismiss.HasDelegate)
            {
                await OnDismiss.InvokeAsync();
            }
            else
            {
                errorBoundary?.Recover();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during error dismissal: {ComponentName}", ComponentName);
        }
    }

    public void HandleException(Exception exception)
    {
        Logger.LogError(exception, 
            "Component error in {ComponentName}. Error Type: {ErrorType}. Exception: {ExceptionType} - {Message}", 
            ComponentName, ErrorType, exception.GetType().Name, exception.Message);
    }
}
