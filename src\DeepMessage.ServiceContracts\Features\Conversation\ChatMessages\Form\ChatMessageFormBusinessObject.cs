﻿using DeepMessage.Framework.Core;
using System.ComponentModel.DataAnnotations;
namespace DeepMessage.ServiceContracts.Features.Conversation;
public class ChatMessageFormBusinessObject
{
    public string? Id { get; set; }

    [Required]
    public string? ConversationId { get; set; }

    public string? Content { get; set; } // Made optional for media-only messages

    public byte ContentType { get; set; }

    /// <summary>
    /// Media attachments for this message (images, documents, etc.)
    /// </summary>
    public List<MediaAttachmentDto>? Attachments { get; set; }
}

/// <summary>
/// Data transfer object for media attachments
/// </summary>
public class MediaAttachmentDto
{
    /// <summary>
    /// Type of attachment: "Image", "Document", "Audio", "Video"
    /// </summary>
    public string AttachmentType { get; set; } = "Image";

    /// <summary>
    /// Original filename or caption
    /// </summary>
    public string FileName { get; set; } = null!;

    /// <summary>
    /// Binary file data (will be encrypted before storage)
    /// </summary>
    public byte[] FileData { get; set; } = null!;

    /// <summary>
    /// Optional caption text for the media
    /// </summary>
    public string? Caption { get; set; }

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long FileSizeBytes { get; set; }

    /// <summary>
    /// MIME type for validation and display
    /// </summary>
    public string? MimeType { get; set; }

    /// <summary>
    /// Thumbnail data for images (smaller compressed version)
    /// </summary>
    public byte[]? ThumbnailData { get; set; }
}

public class ChatMessageIdBusinessObject
{
    public ChatMessageIdBusinessObject(string id, string conversationId, string senderId, string receiverId)
    {
        Id = id;
        ConversationId = conversationId;
        SenderId = senderId;
        ReceiverId = receiverId;
    }

    public string Id { get; set; }

    public string ConversationId { get; set; }

    public string SenderId { get; set; }

    public string ReceiverId { get; set; }



}
