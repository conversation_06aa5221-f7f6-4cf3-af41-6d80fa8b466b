﻿using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Profile;
using Platform.Client.Services.Features.Profile;
namespace Platform.Client.Common.Features.Profile;
public class AvatarFormViewBase : FormBaseMaui<AvatarFormBusinessObject, AvatarFormViewModel, string, IAvatarFormDataService>
{
    public AvatarFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }
}

public partial class AvatarFormView : AvatarFormViewBase
{
    public AvatarFormView(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
        InitializeComponent();
        BindingContext = this;
    }
}
