﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="ModelFury.Briefly.MobileApp.MainPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

    <ScrollView>
        <VerticalStackLayout Padding="30,0" Spacing="25">
            <Image
                Aspect="AspectFit"
                HeightRequest="185"
                SemanticProperties.Description="dot net bot in a hovercraft number nine"
                Source="dotnet_bot.png" />

            <Label
                SemanticProperties.HeadingLevel="Level1"
                Style="{StaticResource Headline}"
                Text="Hello, World!" />

            <Label
                SemanticProperties.Description="Welcome to dot net Multi platform App U I"
                SemanticProperties.HeadingLevel="Level2"
                Style="{StaticResource SubHeadline}"
                Text="Welcome to &#10;.NET Multi-platform App UI" />

            <Button
                x:Name="CounterBtn"
                Clicked="OnCounterClicked"
                HorizontalOptions="Fill"
                SemanticProperties.Hint="Counts the number of times you click"
                Text="Click me" />
        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
