﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
using System.Security.Claims;
using DeepMessage.Client.Common.Data;
using DeepMessage.MauiApp.Services;
using Microsoft.EntityFrameworkCore;
using Platform.Framework.Core;
using Platform.Client.Services.Services;
namespace Platform.Client.Services.Features.Conversation;
public class ChatMessageClientSideFormDataService : IChatMessageFormDataService
{

    private readonly BaseHttpClient _httpClient;
    private readonly AppDbContext context;
    private readonly ILocalStorageService localStorageService;
    private readonly ChatSyncUpService chatSyncUpService;
    private readonly IClientEncryptionService encryptionService;

    public ChatMessageClientSideFormDataService(BaseHttpClient httpClient, AppDbContext context,
        ILocalStorageService localStorageService, ChatSyncUpService chatThreadsSyncService,
        IClientEncryptionService encryptionService)
    {
        _httpClient = httpClient;
        this.context = context;
        this.localStorageService = localStorageService;
        this.chatSyncUpService = chatThreadsSyncService;
        this.encryptionService = encryptionService;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(ChatMessageFormBusinessObject formBusinessObject)
    {

        var userId = await localStorageService.GetValue(ClaimTypes.NameIdentifier);
        ArgumentException.ThrowIfNullOrEmpty(formBusinessObject.ConversationId);
        ArgumentException.ThrowIfNullOrEmpty(userId);
        ArgumentException.ThrowIfNullOrEmpty(formBusinessObject.Content);

        var trx = await context.Database.BeginTransactionAsync();
        try
        {
            // Create message with NO plaintext content for E2E encryption
            var message = new Message()
            {
                Id = Guid.CreateVersion7().ToString(),
                ConversationId = formBusinessObject.ConversationId,
                CreatedAt = DateTime.UtcNow,
                DeliveryStatus = DeepMessage.ServiceContracts.Enums.MessageDeliveryStatus.QueuedToUpSync,
                SenderId = userId
            };

            context.Messages.Add(message);
            await context.SaveChangesAsync();

            // Get conversation participants with their public keys
            var participantsWithKeys = await (from cp in context.ConversationParticipants
                                              from f in context.Friendships.Where(x => x.FriendId == cp.UserId)
                                              where cp.ConversationId == message.ConversationId
                                              && f.UserId == userId
                                              select new ParticipantInfo
                                              {
                                                  UserId = cp.UserId,
                                                  Pub1 = f.Pub1, // Friend's public key
                                                  IsSender = cp.UserId == userId
                                              }).ToListAsync();


            participantsWithKeys.Add(new ParticipantInfo()
            {
                UserId = userId,
                Pub1 = await localStorageService.GetValue("pub1o_"),
                IsSender = true
            });


            // Create encrypted MessageRecipient records for each participant
            foreach (var participant in participantsWithKeys)
            {
                var encryptedContent = encryptionService.EncryptWithRSAPublicKey(formBusinessObject.Content, participant.Pub1);

                var messageRecipient = new MessageRecipient()
                {
                    Id = Guid.CreateVersion7().ToString().ToLower(),
                    MessageId = message.Id,
                    RecipientId = participant.UserId,
                    EncryptedContent = encryptedContent,
                    IsRead = participant.IsSender,
                };

                context.MessageRecipients.Add(messageRecipient);
            }

            await context.SaveChangesAsync();
            chatSyncUpService.Sync(new ChatSyncItem() { Id = message.Id, SyncType = SyncType.ChatMessage });
            await trx.CommitAsync();
            return message.Id;
        }
        catch (Exception ex)
        {
            await trx.RollbackAsync();
            throw new InvalidOperationException("Failed to Send chat message", ex);
        }
        finally
        {
            await trx.DisposeAsync();
        }
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<ChatMessageFormBusinessObject> GetItemByIdAsync(string id)
    {
        return await _httpClient.GetFromJsonAsync<ChatMessageFormBusinessObject>($"api/ChatMessageForm/GetItemById?id=" + id);

    }

    public class ParticipantInfo
    {
        public string UserId { get; set; }
        public string Pub1 { get; set; }
        public bool IsSender { get; set; }
    }
}
