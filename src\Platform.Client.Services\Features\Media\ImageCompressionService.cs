using SkiaSharp;
using Microsoft.Extensions.Logging;
using Platform.Framework.Core;
using MobileApp.MauiShared.Utils;

namespace Platform.Client.Services.Features.Media;

/// <summary>
/// Image compression service using SkiaSharp for optimal performance and quality
/// </summary>
public class ImageCompressionService : IImageCompressionService
{
    private readonly ILogger<ImageCompressionService> _logger;

    public ImageCompressionService(ILogger<ImageCompressionService> logger)
    {
        _logger = logger;
    }

    public async Task<byte[]> CompressImageAsync(byte[] imageData, int maxWidth = 1920, int maxHeight = 1920, int quality = 85)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var originalImage = SKImage.FromEncodedData(imageData);
                if (originalImage == null)
                {
                    _logger.LogWarning("Failed to decode image data for compression");
                    return imageData; // Return original if can't decode
                }

                using var bitmap = SKBitmap.FromImage(originalImage);
                
                // Check if compression is needed
                if (bitmap.Width <= maxWidth && bitmap.Height <= maxHeight)
                {
                    // Image is already within limits, just re-encode with quality setting
                    using var reEncodedImage = SKImage.FromBitmap(bitmap);
                    using var data = reEncodedImage.Encode(SKEncodedImageFormat.Jpeg, quality);
                    
                    var reEncodedData = data.ToArray();
                    
                    // Only return re-encoded if it's smaller
                    if (reEncodedData.Length < imageData.Length)
                    {
                        _logger.LogDebug("Re-encoded image from {OriginalSize} to {NewSize} bytes", 
                            imageData.Length, reEncodedData.Length);
                        return reEncodedData;
                    }
                    
                    return imageData;
                }

                // Resize image using existing ImageResizer utility
                var resizedBitmap = ImageResizer.ResizeImageWithAspectRatio(bitmap, maxWidth, maxHeight);
                
                using var resizedImage = SKImage.FromBitmap(resizedBitmap);
                using var compressedData = resizedImage.Encode(SKEncodedImageFormat.Jpeg, quality);
                
                var result = compressedData.ToArray();
                
                _logger.LogDebug("Compressed image from {OriginalSize} bytes ({OriginalWidth}x{OriginalHeight}) to {NewSize} bytes ({NewWidth}x{NewHeight})",
                    imageData.Length, bitmap.Width, bitmap.Height, 
                    result.Length, resizedBitmap.Width, resizedBitmap.Height);
                
                resizedBitmap.Dispose();
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error compressing image, returning original");
                return imageData;
            }
        });
    }

    public async Task<byte[]> GenerateThumbnailAsync(byte[] imageData, int maxWidth = 300, int maxHeight = 300)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var originalImage = SKImage.FromEncodedData(imageData);
                if (originalImage == null)
                {
                    _logger.LogWarning("Failed to decode image data for thumbnail generation");
                    return imageData;
                }

                using var bitmap = SKBitmap.FromImage(originalImage);
                
                // Calculate thumbnail dimensions maintaining aspect ratio
                var (newWidth, newHeight) = CalculateThumbnailDimensions(
                    bitmap.Width, bitmap.Height, maxWidth, maxHeight);
                
                // Create thumbnail
                using var thumbnailBitmap = bitmap.Resize(new SKImageInfo(newWidth, newHeight), SKSamplingOptions.Default);
                using var thumbnailImage = SKImage.FromBitmap(thumbnailBitmap);
                
                // Encode with higher compression for thumbnails
                using var data = thumbnailImage.Encode(SKEncodedImageFormat.Jpeg, 75);
                
                var result = data.ToArray();
                
                _logger.LogDebug("Generated thumbnail: {OriginalSize} bytes ({OriginalWidth}x{OriginalHeight}) -> {ThumbnailSize} bytes ({ThumbnailWidth}x{ThumbnailHeight})",
                    imageData.Length, bitmap.Width, bitmap.Height,
                    result.Length, newWidth, newHeight);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating thumbnail, returning compressed original");
                // Fallback: return a heavily compressed version of the original
                return CompressImageAsync(imageData, maxWidth, maxHeight, 60).Result;
            }
        });
    }

    public async Task<(int width, int height)> GetImageDimensionsAsync(byte[] imageData)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var image = SKImage.FromEncodedData(imageData);
                if (image == null)
                {
                    _logger.LogWarning("Failed to decode image data for dimension reading");
                    return (0, 0);
                }

                return (image.Width, image.Height);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading image dimensions");
                return (0, 0);
            }
        });
    }

    public bool IsValidImage(byte[] imageData)
    {
        try
        {
            using var image = SKImage.FromEncodedData(imageData);
            return image != null;
        }
        catch
        {
            return false;
        }
    }

    private static (int width, int height) CalculateThumbnailDimensions(
        int originalWidth, int originalHeight, int maxWidth, int maxHeight)
    {
        var aspectRatio = (double)originalWidth / originalHeight;
        
        int newWidth, newHeight;
        
        if (aspectRatio > 1) // Landscape
        {
            newWidth = Math.Min(originalWidth, maxWidth);
            newHeight = (int)(newWidth / aspectRatio);
            
            // Ensure height doesn't exceed max
            if (newHeight > maxHeight)
            {
                newHeight = maxHeight;
                newWidth = (int)(newHeight * aspectRatio);
            }
        }
        else // Portrait or square
        {
            newHeight = Math.Min(originalHeight, maxHeight);
            newWidth = (int)(newHeight * aspectRatio);
            
            // Ensure width doesn't exceed max
            if (newWidth > maxWidth)
            {
                newWidth = maxWidth;
                newHeight = (int)(newWidth / aspectRatio);
            }
        }
        
        // Ensure minimum dimensions
        newWidth = Math.Max(1, newWidth);
        newHeight = Math.Max(1, newHeight);
        
        return (newWidth, newHeight);
    }
}

/// <summary>
/// Extension methods for image compression service
/// </summary>
public static class ImageCompressionExtensions
{
    /// <summary>
    /// Compresses image if it exceeds the specified size threshold
    /// </summary>
    public static async Task<byte[]> CompressIfNeededAsync(
        this IImageCompressionService service, 
        byte[] imageData, 
        long maxSizeBytes = 2_000_000) // 2MB default
    {
        if (imageData.Length <= maxSizeBytes)
            return imageData;

        // Progressively reduce quality until size is acceptable
        var quality = 85;
        var result = imageData;
        
        while (result.Length > maxSizeBytes && quality > 30)
        {
            result = await service.CompressImageAsync(imageData, 1920, 1920, quality);
            quality -= 10;
        }
        
        return result;
    }

    /// <summary>
    /// Gets a data URL for displaying images in web components
    /// </summary>
    public static string ToDataUrl(this byte[] imageData, string mimeType = "image/jpeg")
    {
        var base64 = Convert.ToBase64String(imageData);
        return $"data:{mimeType};base64,{base64}";
    }
}
