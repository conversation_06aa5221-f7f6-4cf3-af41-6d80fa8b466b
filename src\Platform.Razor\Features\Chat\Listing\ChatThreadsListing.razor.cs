using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.Components;
using Platform.Client.Services.Features.Conversation;
using Platform.Client.Services.Services;
using System.Security.Cryptography;
using System.Text;
using System.Timers;

namespace Platform.Razor.Features.Chat.Listing
{
    public partial class ChatThreadsListing : IDisposable
    {
        private System.Timers.Timer? _searchTimer;

        private const int SearchDelayMs = 500; // Debounce search for 500ms
        [Inject] ISecureKeyManager secureKeyManager { get; set; } = null!;
        RSA rsaKey = null!;
        protected override async Task OnInitializedAsync()
        {
            //var scope = ScopeFactory.CreateScope();
            //var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            //var chatThreadsListingDataService = scope.ServiceProvider.GetRequiredKeyedService<IChatThreadsListingDataService>("client");
            //var items = await chatThreadsListingDataService.GetPaginatedItems(new ChatThreadsFilterBusinessObject() { UsePagination = false });
            //foreach (var item in items.Items)
            //{
            //    var chatThread = await context.Conversations.FirstOrDefaultAsync(x => x.Id == item.Id);
            //    if (chatThread == null)
            //    {
            //        chatThread = new Conversation()
            //        {
            //            Id = item.Id,
            //            CreatedAt = DateTime.UtcNow,
            //            Title = "None",
            //            IsDeleted = false,
            //            Type =  DeepMessage.Framework.Enums.ConversationType.Direct,
            //            SyncStatus = 0
            //        };
            //        context.Conversations.Add(chatThread);
            //        await context.SaveChangesAsync();
            //    }
            //}

            // Initialize the search timer for debounced search
            _searchTimer = new System.Timers.Timer(SearchDelayMs);
            _searchTimer.Elapsed += OnSearchTimerElapsed;
            _searchTimer.AutoReset = false;
            rsaKey = secureKeyManager.GetRSAPrivateKeyAsync();
            PubSub.Hub.Default.Subscribe<string>((m) =>
            {
                if (m == "NewMessageReceived")
                {
                    _ = LoadItems(false);
                }
            });

            await base.OnInitializedAsync();
        }

        protected override List<ChatThreadsListingViewModel> ConvertToListViewModel(List<ChatThreadsListingBusinessObject> list)
        {
            return list.Select(x => new ChatThreadsListingViewModel
            {
                Id = x.Id,
                Avatar = x.Avatar,
                Name = x.Name,
                LastMessage = Encoding.UTF8.GetString(rsaKey.Decrypt(Convert.FromBase64String(x.LastMessage!), RSAEncryptionPadding.OaepSHA256)),
                LastMessageTime = x.LastMessageTime

            }).ToList();
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await Task.Delay(100);
                await InvokeAsync(StateHasChanged);
            }
            await base.OnAfterRenderAsync(firstRender);
        }

        /// <summary>
        /// Shows the Add Friend form dialog
        /// </summary>
        private void ShowAddFriendForm()
        {
            try
            {
                // Navigate to friends page where they can add friends
                Navigation.NavigateTo("/friends");
            }
            catch (Exception ex)
            {
                Error = "Unable to navigate to friends page. Please try again.";
                StateHasChanged();
            }
        }

        /// <summary>
        /// Handles search input with debouncing
        /// </summary>
        private void OnSearchKeyUp()
        {
            // Reset and restart the timer for debounced search
            _searchTimer?.Stop();
            _searchTimer?.Start();
        }

        /// <summary>
        /// Timer elapsed event for debounced search
        /// </summary>
        private async void OnSearchTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            await InvokeAsync(async () =>
            {
                // Trigger search when timer elapses
                await LoadItems();
                StateHasChanged();
            });
        }

        /// <summary>
        /// Clears the search text and refreshes the list
        /// </summary>
        private async Task ClearSearch()
        {
            FilterViewModel.SearchText = string.Empty;
            await LoadItems();
        }

        /// <summary>
        /// Opens a chat conversation
        /// </summary>
        private void OpenChat(ChatThreadsListingViewModel thread)
        {
            Navigation.NavigateTo($"/chat/{thread.Id}?ChatTitle={thread.Name}&ChatIcon={thread.Avatar}");
        }


        /// <summary>
        /// Gets the initials from a name for avatar display
        /// </summary>
        private string GetInitials(string? name)
        {
            if (string.IsNullOrEmpty(name))
                return "?";

            var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0)
                return "?";

            if (parts.Length == 1)
                return parts[0].Substring(0, Math.Min(2, parts[0].Length)).ToUpper();

            return $"{parts[0][0]}{parts[^1][0]}".ToUpper();
        }

        /// <summary>
        /// Formats the timestamp for display
        /// </summary>
        private string GetFormattedTime(DateTime timestamp)
        {
            var now = DateTime.UtcNow;
            var timeSpan = now - timestamp;

            if (timeSpan.TotalMinutes < 1)
                return "Just now";

            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes}m ago";

            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours}h ago";

            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays}d ago";

            if (timestamp.Year == now.Year)
                return timestamp.ToString("MMM dd");

            return timestamp.ToString("MMM dd, yyyy");
        }

        /// <summary>
        /// Determines if a thread has unread messages (placeholder logic)
        /// </summary>
        private bool HasUnreadMessages(ChatThreadsListingViewModel thread)
        {
            // TODO: Implement actual unread message logic
            // This could be based on last read timestamp vs last message timestamp
            return false;
        }

        /// <summary>
        /// Refreshes the chat threads list
        /// </summary>
        private async Task RefreshItems()
        {
            Error = string.Empty;
            await LoadItems();
        }

        void IDisposable.Dispose()
        {
            _searchTimer.Stop();
            _searchTimer.Dispose();
        }
    }
}
