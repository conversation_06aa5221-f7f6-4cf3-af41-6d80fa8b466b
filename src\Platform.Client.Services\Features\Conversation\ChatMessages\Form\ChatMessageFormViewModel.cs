﻿using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
using System.ComponentModel.DataAnnotations;
namespace Platform.Client.Services.Features.Conversation;
public class ChatMessageFormViewModel : ObservableBase, IValidateable
{
    [Required]
    public string? ConversationId { get; set; }

    private string? _content;

    public string? Content
    {
        get { return _content; }
        set
        {
            SetField(ref _content, value);
        }
    }

    public byte ContentType { get; set; }

    /// <summary>
    /// Media attachments for this message
    /// </summary>
    public List<MediaAttachmentDto>? Attachments { get; set; }

    public void Validate()
    {
        // Allow messages with either content OR attachments
        if (string.IsNullOrEmpty(Content) && (Attachments == null || !Attachments.Any()))
        {
            throw new ValidationException("Message content or attachments are required.");
        }
        if (string.IsNullOrEmpty(ConversationId))
        {
            throw new ValidationException("ConversationId is required.");
        }
    }
}
