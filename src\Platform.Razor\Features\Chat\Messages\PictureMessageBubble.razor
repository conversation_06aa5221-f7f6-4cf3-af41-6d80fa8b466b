@using DeepMessage.ServiceContracts.Features.Conversation
@using DeepMessage.ServiceContracts.Enums
@using Platform.Client.Services.Features.Conversation

@* Picture Message Bubble Component for WhatsApp-style image messages *@
<div class="picture-message-container @(Message.IsIncoming ? "incoming" : "outgoing")">
    @if (Message.Attachments?.Any(a => a.AttachmentType == "Image") == true)
    {
        <div class="picture-message-bubble @(Message.IsIncoming ? "incoming-bubble" : "outgoing-bubble")">
            @foreach (var attachment in Message.Attachments.Where(a => a.AttachmentType == "Image"))
            {
                <div class="image-container" @onclick="() => ShowFullImage(attachment)">
                    @if (!string.IsNullOrEmpty(attachment.ThumbnailBase64))
                    {
                        <img src="data:image/jpeg;base64,@attachment.ThumbnailBase64" 
                             alt="@attachment.FileName"
                             class="message-image @(attachment.IsUploading ? "uploading" : "")"
                             loading="lazy" />
                    }
                    else if (!string.IsNullOrEmpty(attachment.FileUrl))
                    {
                        <img src="@attachment.FileUrl" 
                             alt="@attachment.FileName"
                             class="message-image @(attachment.IsUploading ? "uploading" : "")"
                             loading="lazy" />
                    }
                    else
                    {
                        <!-- Placeholder for loading image -->
                        <div class="image-placeholder">
                            <div class="loading-spinner"></div>
                        </div>
                    }

                    @if (attachment.IsUploading)
                    {
                        <div class="upload-overlay">
                            <div class="upload-progress">
                                <div class="progress-ring">
                                    <div class="progress-fill" style="--progress: @attachment.UploadProgress%"></div>
                                </div>
                                <span class="progress-text">@attachment.UploadProgress%</span>
                            </div>
                        </div>
                    }

                    @if (attachment.FileSizeBytes.HasValue)
                    {
                        <div class="image-info">
                            <span class="file-size">@FormatFileSize(attachment.FileSizeBytes.Value)</span>
                        </div>
                    }
                </div>
            }

            @if (!string.IsNullOrEmpty(Message.Content))
            {
                <div class="image-caption">
                    @Message.Content
                </div>
            }

            <div class="message-footer">
                <span class="message-timestamp">
                    @GetFormattedTime(Message.Timestamp)
                </span>
                @if (!Message.IsIncoming)
                {
                    <span class="delivery-status">
                        @GetDeliveryIcon(Message.DeliveryStatus)
                    </span>
                }
            </div>
        </div>
    }
</div>

<style>
    .picture-message-container {
        display: flex;
        margin-bottom: 0.5rem;
        max-width: 100%;
    }

    .picture-message-container.incoming {
        justify-content: flex-start;
    }

    .picture-message-container.outgoing {
        justify-content: flex-end;
    }

    .picture-message-bubble {
        position: relative;
        max-width: 20rem; /* 320px */
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .incoming-bubble {
        background-color: #f4f4f5; /* zinc-100 */
        border-bottom-left-radius: 0.25rem;
    }

    .outgoing-bubble {
        background-color: #d4d4d8; /* zinc-300 */
        border-bottom-right-radius: 0.25rem;
    }

    .image-container {
        position: relative;
        cursor: pointer;
        transition: opacity 0.2s ease;
    }

    .image-container:hover {
        opacity: 0.95;
    }

    .message-image {
        width: 100%;
        height: auto;
        display: block;
        border-radius: 0.75rem 0.75rem 0 0;
        max-height: 24rem; /* 384px */
        object-fit: cover;
    }

    .message-image.uploading {
        opacity: 0.7;
    }

    .image-placeholder {
        width: 100%;
        height: 12rem; /* 192px */
        background-color: #e4e4e7; /* zinc-200 */
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.75rem 0.75rem 0 0;
    }

    .loading-spinner {
        width: 2rem;
        height: 2rem;
        border: 2px solid #d4d4d8;
        border-top-color: #71717a;
        border-radius: 50%;
    }



    .upload-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.75rem 0.75rem 0 0;
    }

    .upload-progress {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    .progress-ring {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background: conic-gradient(
            #ffffff var(--progress, 0%),
            rgba(255, 255, 255, 0.3) var(--progress, 0%)
        );
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    .progress-ring::before {
        content: '';
        position: absolute;
        width: 2rem;
        height: 2rem;
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 50%;
    }

    .progress-text {
        color: white;
        font-size: 0.75rem;
        font-weight: 600;
        position: absolute;
        z-index: 1;
    }

    .image-info {
        position: absolute;
        bottom: 0.5rem;
        left: 0.5rem;
        background-color: rgba(0, 0, 0, 0.6);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
    }

    .image-caption {
        padding: 0.75rem;
        font-size: 0.875rem;
        line-height: 1.25rem;
        color: #374151; /* gray-700 */
        word-wrap: break-word;
    }

    .message-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 0.25rem;
        padding: 0.5rem 0.75rem;
        padding-top: 0;
    }

    .message-timestamp {
        font-size: 0.75rem;
        color: #6b7280; /* gray-500 */
    }

    .delivery-status {
        font-size: 0.75rem;
        color: #6b7280; /* gray-500 */
    }

    /* Dark mode and responsive adjustments - simplified for Razor compatibility */
</style>

@code {
    [Parameter] public ChatMessagesListingViewModel Message { get; set; } = null!;
    [Parameter] public EventCallback<MessageAttachmentInfo> OnImageClick { get; set; }

    private async Task ShowFullImage(MessageAttachmentInfo attachment)
    {
        await OnImageClick.InvokeAsync(attachment);
    }

    private string GetFormattedTime(DateTime? timestamp)
    {
        if (!timestamp.HasValue)
            return "";

        var time = timestamp.Value;
        var now = DateTime.Now;

        if (time.Date == now.Date)
        {
            // Today - show time only
            return time.ToString("HH:mm");
        }
        else if (time.Date == now.Date.AddDays(-1))
        {
            // Yesterday
            return "Yesterday " + time.ToString("HH:mm");
        }
        else if (time.Date > now.Date.AddDays(-7))
        {
            // This week - show day and time
            return time.ToString("ddd HH:mm");
        }
        else
        {
            // Older - show date and time
            return time.ToString("MMM dd HH:mm");
        }
    }

    private string GetDeliveryIcon(DeliveryStatus status)
    {
        return status switch
        {
            DeliveryStatus.SentToMessageServer or
            DeliveryStatus.SentToEndUserViaSignalR or
            DeliveryStatus.SentToEndUserViaPushNotification => "✓",
            DeliveryStatus.DeliveredToEndUser => "✓✓",
            DeliveryStatus.ReadByEndUser => "✓✓", // Could be blue in CSS
            _ => ""
        };
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.#} {sizes[order]}";
    }
}
