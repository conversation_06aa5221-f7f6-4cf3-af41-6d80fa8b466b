﻿using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Account;
using System.ComponentModel.DataAnnotations;

namespace Platform.Client.Services.Features.Account;

public class ProfileFormViewModel :ObservableBase
{
    [Required]
    public string? Id { get; set; }

    [Required]
    [StringLength(450)]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Stores the generated avatar image data as base64 string
    /// </summary>
    public string? AvatarData { get; set; }

    /// <summary>
    /// Description used to generate the avatar
    /// </summary>
    [StringLength(450)]
    public string? AvatarDescription { get; set; }
}
