﻿using DeepMessage.ServiceContracts.Enums;
namespace Platform.Client.Services.Features.AuthCodes;
public class AuthCodeListingViewModel
{
    public string Id { get; set; } = null!;

    public string? AuthCode { get; set; }

    public DateTime CreatedAt { get; set; }


    public DateTime ExpiresAt { get; set; }

    public AuthCodeStatus AuthCodeStatus { get; set; }

    public string? ConsumedBy { get; set; }

    public DateTime? ConsumedAt { get; set; }
}
