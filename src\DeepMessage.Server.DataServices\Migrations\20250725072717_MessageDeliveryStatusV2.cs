﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DeepMessage.Server.DataServices.Migrations
{
    /// <inheritdoc />
    public partial class MessageDeliveryStatusV2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DeliveryStatusNotified",
                table: "MessageRecipients");

            migrationBuilder.RenameColumn(
                name: "DeliveryStatus",
                table: "MessageRecipients",
                newName: "ReadAcknowledgementStatus");

            migrationBuilder.AddColumn<byte>(
                name: "DeliveryAcknowledgementStatus",
                table: "MessageRecipients",
                type: "tinyint",
                nullable: false,
                defaultValue: (byte)0);

            migrationBuilder.AddColumn<byte>(
                name: "MessageDeliveryStatus",
                table: "MessageRecipients",
                type: "tinyint",
                nullable: false,
                defaultValue: (byte)0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DeliveryAcknowledgementStatus",
                table: "MessageRecipients");

            migrationBuilder.DropColumn(
                name: "MessageDeliveryStatus",
                table: "MessageRecipients");

            migrationBuilder.RenameColumn(
                name: "ReadAcknowledgementStatus",
                table: "MessageRecipients",
                newName: "DeliveryStatus");

            migrationBuilder.AddColumn<bool>(
                name: "DeliveryStatusNotified",
                table: "MessageRecipients",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
