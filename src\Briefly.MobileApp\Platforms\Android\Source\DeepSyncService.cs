﻿
using Android.App;
using Android.Content;
using Android.OS;
using AndroidX.Core.App;
using AndroidX.Lifecycle;
using CommunityToolkit.Mvvm.Messaging;
using DeepMessage.Client.Common.Data;
using DeepMessage.MauiApp.Services;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using global::Android.Content.PM;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MobileApp.MauiShared;
using ModelFury.Briefly.MobileApp;
using Platform.Framework.Core;
using System.Text.Json;
using System.Threading.Channels;
using Message = DeepMessage.Client.Common.Data.Message;
using Notification = Android.App.Notification;

namespace DeepMessage.MauiApp.Platforms.Android;
[Service(Exported = true, ForegroundServiceType = ForegroundService.TypeDataSync)]
public class DeepSyncService : Service
{
    private HubConnection? _hubConnection;
    private CancellationTokenSource _cts = new CancellationTokenSource();

    // These dependencies should be resolved via DI.
    private IServiceScopeFactory? _scopeFactory;
    private ILogger<DeepSyncService> _logger = null!;
    Task? _monitoringTask;
    Task? _monitoringProducerTask;
    //Task? _syncDownTask;
    public override void OnCreate()
    {
        base.OnCreate();
        _scopeFactory = IPlatformApplication.Current?.Services.GetService<IServiceScopeFactory>();
        _logger = IPlatformApplication.Current?.Services.GetService<ILogger<DeepSyncService>>()!;

        // Initialize your channels, etc.
    }

    public override StartCommandResult OnStartCommand(Intent? intent, StartCommandFlags flags, int startId)
    {
        // Create a notification channel if needed (Android O and above)
        if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
        {
            var channel = new NotificationChannel("messenger_channel", "Messenger Service", NotificationImportance.Default);
            var notificationManager = (NotificationManager?)GetSystemService(NotificationService);
            if (notificationManager != null)
            {
                notificationManager.CreateNotificationChannel(channel);
            }
        }

        // Build the notification.
        var notification = new Notification.Builder(this, "messenger_channel")
            .SetContentTitle("Messenger Service")
            .SetContentText("Syncing messages...")
            .SetSmallIcon(global::Android.Resource.Drawable.ButtonStar) // replace with your app icon.
            .Build();

        // Start as a foreground service.
        if (global::Android.OS.Build.VERSION.SdkInt >= global::Android.OS.BuildVersionCodes.UpsideDownCake)
            StartForeground(1, notification, ForegroundService.TypeDataSync);
        else
            StartForeground(1, notification);

        //_syncDownTask = Task.Factory.StartNew(StartDownSyncTask, TaskCreationOptions.RunCont, inuationsAsynchronously);
        if (_monitoringTask == null || _monitoringTask.Status != TaskStatus.Running)
        {
            _monitoringTask = Task.Factory.StartNew(StartMonitor, TaskCreationOptions.RunContinuationsAsynchronously);
            _monitoringProducerTask = Task.Factory.StartNew(() =>
            {
                while (!_cts.IsCancellationRequested)
                {
                    try
                    {
                        _monitoringChannel.Writer.TryWrite(string.Empty);
                        _logger.LogDebug("Monitoring task running...");
                        Thread.Sleep(15000);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Error in monitoring task: {Error}", ex.Message);
                    }
                }
            });
        }
        //_downSyncChannel.Writer.WriteAsync(new ChatSyncItem() { Id = string.Empty, SyncType = 1 }, _cts.Token);
        return StartCommandResult.Sticky;
    }



    private async Task StartMonitor()
    {
        while (!_cts.IsCancellationRequested)
        {
            var hint = await _monitoringChannel.Reader.ReadAsync();
            await StartSignalRConnection(_cts.Token);
            await Task.Delay(5000);
        }
    }

    Channel<string> _monitoringChannel = Channel.CreateBounded<string>(10);

    private bool IsAlive(object? obj)
    {
        try
        {
            if (obj != null && obj.ToString() != null)
            {
                _logger.LogDebug("IsAlive: true");
                return true;
            }
            _logger.LogDebug("IsAlive: false");
            return false;
        }
        catch (Exception ex)
        {

            _logger.LogError("IsAlive error: {Error}", ex.Message);
            return false;
        }
    }

    private async Task StartSignalRConnection(CancellationToken token)
    {
        try
        {
            _logger.LogDebug("SignalR Starting connection...");
            using var scope = _scopeFactory!.CreateScope();
            var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();

            var httpHandler = new HttpClientHandler
            {
                // WARNING: Do not use this in production.
                ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
            };

            if (_hubConnection != null && IsAlive(_hubConnection) && _hubConnection.State == HubConnectionState.Disconnected)
            {
                _logger.LogInformation("Stopping existing SignalR connection...");
                try
                {
                    await _hubConnection.StopAsync();
                    await _hubConnection.DisposeAsync();
                    _hubConnection = null;
                }
                catch { }

            }


            if (_hubConnection == null || !IsAlive(_hubConnection))
            {

                _logger.LogDebug("SignalR Building connection...");
                _hubConnection = new HubConnectionBuilder()
                    .WithUrl(MauiProgram.ChatHubUrl, options =>
                    {
                        options.HttpMessageHandlerFactory = _ => httpHandler;
                        options.AccessTokenProvider = async () => await localStorageService.GetValue("auth_token");
                    })
                    .WithAutomaticReconnect(new[]
                    {
                    TimeSpan.Zero,
                    TimeSpan.FromSeconds(2),
                    TimeSpan.FromSeconds(10),
                    TimeSpan.FromSeconds(30)
                    })
                    .Build();

                _hubConnection.On<string, string>("OnNewFeed", async (userId, messageJson) =>
                {
                    await ProcessIncomingMessage(userId, messageJson);
                });

                _hubConnection.On("Logout", async () =>
                {
                    _logger.LogInformation("SignalR Logout event received.");
                    var scope = _scopeFactory.CreateScope();
                    var storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                    await storageService.RemoveValue("auth_token");
                    if (IsAppInForeground())
                    {
                        await localStorageService.SetValue("true", "message");
                        var intent = new Intent("com.companyname.deepmessage.UPDATE");
                        intent.PutExtra("message", "Logout");
                        SendBroadcast(intent);
                    }
                });


                _hubConnection.On<string, string>("OnFeedUpdate", async (userId, messageJson) =>
                {
                    _logger.LogInformation("SignalR Received OnFeedUpdate: {0}", messageJson);
                    await UpdateMessage(userId, messageJson);
                });

                _hubConnection.Reconnecting += error =>
                {
                    _logger?.LogWarning("SignalR reconnecting: {Error}", error?.Message);
                    return Task.CompletedTask;
                };

                _hubConnection.Reconnected += connectionId =>
                {
                    _logger?.LogInformation("SignalR reconnected. ConnectionId: {ConnectionId}", connectionId);
                    return Task.CompletedTask;
                };

                _hubConnection.Closed += async error =>
                {
                    _logger?.LogError("SignalR closed: {Error}", error?.Message);
                    await Task.Delay(TimeSpan.FromSeconds(5), token);
                    await _monitoringChannel.Writer.WriteAsync(string.Empty, token);
                };

                await _hubConnection.StartAsync(token);
                _logger.LogInformation("SignalR connection started...");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex.ToString());
            await _monitoringChannel.Writer.WriteAsync(string.Empty);
        }
    }

    // Process incoming SignalR messages.
    private async Task ProcessIncomingMessage(string userId, string messageJson)
    {
        try
        {
            _logger.LogInformation("SignalR Received broadcast: {0}", messageJson);
            var formBusinessObject = JsonSerializer.Deserialize<ChatMessagesSyncFormBusinessObject>(messageJson);
            if (!string.IsNullOrEmpty(userId) && formBusinessObject != null)
            {
                // Create a new scope for the DbContext to keep it short-lived.
                using var scope = _scopeFactory.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                var message = await dbContext.Messages.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id);
                if (message == null)
                {
                    message = new Message();
                    message.Id = formBusinessObject.Id;
                    message.ConversationId = formBusinessObject.ConversationId;
                    message.CreatedAt = formBusinessObject.CreatedAt;
                    message.SenderId = formBusinessObject.SenderId;
                    dbContext.Messages.Add(message);
                }

                message.DeletedAt = formBusinessObject.DeletedAt;
                message.DisappearAfter = formBusinessObject.DisappearAfter;
                message.DisappearAt = formBusinessObject.DisappearAt;
                message.IsDeleted = formBusinessObject.IsDeleted;
                message.IsEdited = formBusinessObject.IsEdited;
                message.IsEphemeral = formBusinessObject.IsEphemeral;
                message.EditedAt = formBusinessObject.EditedAt; 
                var effectedRows = await dbContext.SaveChangesAsync();

                if (_hubConnection != null && _hubConnection.State == HubConnectionState.Connected)
                    await _hubConnection.SendAsync("AcknowledgeMessage", message.Id, message.SenderId, MessageDeliveryStatus.DeliveredToEndUser);

                _logger.LogDebug("Added message: {0}, {1}", message.Id, message.Conversation);

                // ✅ FIXED: Thread-safe app state checking and UI updates
                var isInForeground = IsAppInForeground();
                if (isInForeground)
                {
                    // ✅ FIXED: Proper async handling for UI updates
                    await MainThread.InvokeOnMainThreadAsync(async () =>
                    {
                        try
                        {
                            await localStorageService.SetValue("true", "message");
                            var intent = new Intent("com.companyname.deepmessage.UPDATE");
                            intent.PutExtra("message", "New data available");
                            intent.PutExtra("messageId", message.Id);
                            intent.PutExtra("timestamp", DateTime.UtcNow.ToString("O"));
                            SendBroadcast(intent);

                            _logger.LogDebug("Updated UI for message {MessageId}", message.Id);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Failed to update UI for message {MessageId}", message.Id);
                        }
                    });
                }
                else
                {
                    // ✅ FIXED: Enhanced background notification with unique ID
                    var notificationId = Math.Abs(message.Id.GetHashCode());
                    ShowMessageNotification("New message");
                    _logger.LogDebug("Showed background notification for message {MessageId}", message.Id);
                }


            }
        }
        catch (Exception ex)
        {
            _logger?.LogError("Error processing broadcast: {Error}", ex.Message);
        }
    }

    // Down-sync task: e.g. process messages to retrieve full details.
    //private async Task StartDownSyncTask()
    //{
    //    while (!_cts.IsCancellationRequested)
    //    {
    //        try
    //        {
    //            var syncItem = await _downSyncChannel.Reader.ReadAsync(_cts.Token);
    //            using var scope = _scopeFactory!.CreateScope();
    //            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
    //            var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
    //            if (syncItem.SyncType == 0)
    //            {

    //                var conversations = await (from c in context.Conversations
    //                                           where c.SyncStatus == 100
    //                                           select c).ToArrayAsync();

    //                if (!conversations.Any())
    //                    continue;

    //                var chatThreadSyncService = scope.ServiceProvider.GetRequiredService<IChatThreadSyncFormDataService>();
    //                foreach (var conversation in conversations)
    //                {
    //                    var formBusinessObject = await chatThreadSyncService.GetItemByIdAsync(conversation.Id);

    //                    conversation.CreatedAt = formBusinessObject.CreatedAt;
    //                    conversation.IsDeleted = formBusinessObject.IsDeleted;
    //                    conversation.Type = formBusinessObject.Type;
    //                    conversation.Title = formBusinessObject.Title;
    //                    await context.SaveChangesAsync();

    //                    ArgumentNullException.ThrowIfNull(formBusinessObject.ChatParticipents);

    //                    foreach (var item in formBusinessObject.ChatParticipents)
    //                    {
    //                        var participent = await context.ConversationParticipants.FirstOrDefaultAsync(x => x.Id == item.Id);
    //                        if (participent == null)
    //                        {
    //                            participent = new ConversationParticipant()
    //                            {
    //                                Id = item.Id,
    //                                ConversationId = item.ConversationId,
    //                                IsAdmin = item.IsAdmin,
    //                                JoinedAt = item.JoinedAt,
    //                                UserId = item.UserId,
    //                            };
    //                            context.ConversationParticipants.Add(participent);
    //                            await context.SaveChangesAsync();
    //                        }
    //                    }

    //                    conversation.SyncStatus = 101;
    //                    await context.SaveChangesAsync();

    //                }
    //            }
    //            if (syncItem.SyncType == 1)
    //            {
    //                var messages = await (from m in context.Messages
    //                                      where m.SyncStatus == 100
    //                                      select m).ToListAsync();

    //                if (!messages.Any())
    //                    continue;

    //                var chatMessagSyncService = scope.ServiceProvider.GetRequiredService<IChatMessagesSyncFormDataService>();

    //                foreach (var message in messages)
    //                {
    //                    var formBusinessObject = await chatMessagSyncService.GetItemByIdAsync(message.Id);
    //                    message.Id = formBusinessObject.Id;
    //                    message.ConversationId = formBusinessObject.ConversationId;
    //                    message.CreatedAt = formBusinessObject.CreatedAt;
    //                    message.DeletedAt = formBusinessObject.DeletedAt;
    //                    message.DisappearAfter = formBusinessObject.DisappearAfter;
    //                    message.DisappearAt = formBusinessObject.DisappearAt;
    //                    message.IsDeleted = formBusinessObject.IsDeleted;
    //                    message.IsEdited = formBusinessObject.IsEdited;
    //                    message.IsEphemeral = formBusinessObject.IsEphemeral;
    //                    message.EditedAt = formBusinessObject.EditedAt;
    //                    message.PlainContent = formBusinessObject.PlainContent;
    //                    message.SenderId = formBusinessObject.SenderId;
    //                    await context.SaveChangesAsync();

    //                    //todo: optimize for only required data
    //                    foreach (var participant in formBusinessObject.MessageRecipients)
    //                    {
    //                        var messageRecipient = await context.MessageRecipients.FirstOrDefaultAsync(x => x.MessageId == message.Id && x.RecipientId == participant.Id);
    //                        if (messageRecipient == null)
    //                        {
    //                            messageRecipient = new MessageRecipient()
    //                            {
    //                                Id = participant.Id,
    //                                MessageId = participant.MessageId,
    //                                RecipientId = participant.RecipientId,
    //                                EncryptedContent = message.PlainContent!,
    //                            };
    //                        }
    //                        context.MessageRecipients.Add(messageRecipient);
    //                        await context.SaveChangesAsync();
    //                    }
    //                    message.SyncStatus = 101;
    //                    await context.SaveChangesAsync();
    //                }

    //                if (messages.Count > 0)
    //                {
    //                    if (IsAppInForeground())
    //                    {
    //                        await localStorageService.SetValue("true", "message");
    //                        var intent = new Intent("com.companyname.deepmessage.UPDATE");
    //                        intent.PutExtra("message", "New data available");
    //                        SendBroadcast(intent);
    //                    }
    //                    else
    //                    {
    //                        // App is in background, show a system notification.
    //                        ShowMessageNotification(messages.Count.ToString());
    //                    }
    //                }

    //            }
    //            // Process down-sync for chat messages.
    //            // (For example, download full message body and update local DB.)
    //            // You can call an injected sync service here.

    //            // After processing, you might also trigger UI updates, etc.
    //        }
    //        catch (System.OperationCanceledException) { break; }
    //        catch (Exception ex)
    //        {
    //            _logger?.LogWarning("Down-sync error: {Error}", ex.Message);
    //        }
    //    }
    //}
    private bool IsAppInForeground()
    {
        var lifecycle = ProcessLifecycleOwner.Get()?.Lifecycle;
        return lifecycle?.CurrentState?.IsAtLeast(Lifecycle.State.Started!) ?? false;
    }

    private void ShowMessageNotification(string messageContent)
    {
        var notificationManager = (NotificationManager?)GetSystemService(NotificationService);

        // Create a notification channel if necessary (for API 26+)
        if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
        {
            var channel = new NotificationChannel("message_channel", "Messages", NotificationImportance.High);
            notificationManager.CreateNotificationChannel(channel);
        }

        // Build the notification using NotificationCompat for backward compatibility.
        var builder = new NotificationCompat.Builder(this, "message_channel")
                            .SetContentTitle("New Message")
                            .SetContentText(messageContent)
                            .SetSmallIcon(global::Android.Resource.Drawable.ButtonStar) // Replace with your icon.
                            .SetAutoCancel(true);

        // Create a pending intent so that clicking the notification opens your main activity.
        Intent intent = new Intent(this, typeof(MainActivity));
        intent.SetFlags(ActivityFlags.ClearTop | ActivityFlags.NewTask);
        PendingIntent? pendingIntent = PendingIntent.GetActivity(this, 0, intent, PendingIntentFlags.UpdateCurrent | PendingIntentFlags.Immutable);
        builder.SetContentIntent(pendingIntent);

        // Display the notification.
        notificationManager.Notify(1001, builder.Build());
    }

    public override void OnDestroy()
    {
        _cts.Cancel();
        if (_hubConnection != null)
        {
            _hubConnection.StopAsync();
            _hubConnection.DisposeAsync();
            _hubConnection = null;
        }
        base.OnDestroy();
    }

    public override IBinder OnBind(Intent? intent) => null!;
    private async Task UpdateMessage(string userId, string messageJson)
    {
        try
        {
            var formBusinessObject = JsonSerializer.Deserialize<ChatMessageUpdate>(messageJson);
            if (!string.IsNullOrEmpty(userId) && formBusinessObject != null)
            {
                using var scope = _scopeFactory.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var message = await dbContext.Messages.FirstOrDefaultAsync(x => x.Id == formBusinessObject.MessageId);
                if (message == null)
                {
                    throw new InvalidOperationException();
                }
                message.DeliveryStatus = formBusinessObject.DeliveryStatus;
                message.DeliveryStatusTime = formBusinessObject.DeliveryStatusTime;
                await dbContext.SaveChangesAsync();
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError("Error updating message: {Error}", ex.Message);
        }
    }

}

// Example DTOs and business objects.
public class ChatSyncItem
{
    public string Id { get; set; } = string.Empty;
    /// <summary>
    /// 0 = Chat Thread, 1 = Chat Message
    /// </summary>
    public int SyncType { get; set; }
}

public class ChatMessageIdBusinessObject_
{
    public string Id { get; set; } = string.Empty;
    public string ConversationId { get; set; } = string.Empty;
    public string SenderId { get; set; } = string.Empty;
}


