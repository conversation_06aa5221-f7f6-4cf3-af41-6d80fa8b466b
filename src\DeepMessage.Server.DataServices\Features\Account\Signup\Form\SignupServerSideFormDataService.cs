﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using Microsoft.Data.SqlClient;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
namespace DeepMessage.Server.DataServices.Features.Account;
public class SignupServerSideFormDataService : ISignupFormDataService
{
    private readonly JWTSettings _jwtSettings;
    private readonly AppDbContext _context;
    private readonly UserManager<ApplicationUser> userManager;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<SignupServerSideFormDataService> _logger;

    public SignupServerSideFormDataService(AppDbContext context,
        UserManager<ApplicationUser> userManager,
        IOptions<JWTSettings> jWTSettings,
        IHttpContextAccessor httpContextAccessor,
        ILogger<SignupServerSideFormDataService> logger)
    {
        _context = context;
        this.userManager = userManager;
        this._jwtSettings = jWTSettings.Value;
        this._httpContextAccessor = httpContextAccessor;
        this._logger = logger;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(SignupFormBusinessObject formBusinessObject)
    {
        var clientIp = GetClientIpAddress();
        var operationId = Guid.NewGuid().ToString();

        _logger.LogInformation("Starting signup process. OperationId: {OperationId}, ClientIP: {ClientIP}, Nickname: {Nickname}",
            operationId, clientIp, formBusinessObject.NickName);

        try
        {
            // Enhanced input validation with security checks
            await ValidateSignupInputSecurely(formBusinessObject, clientIp);

            // Check if user already exists
            var existingUser = await userManager.FindByNameAsync(formBusinessObject.NickName!);
            if (existingUser != null)
            {
                _logger.LogWarning("Signup attempt with existing nickname. OperationId: {OperationId}, ClientIP: {ClientIP}, Nickname: {Nickname}",
                    operationId, clientIp, formBusinessObject.NickName);
                throw new Exception("A user already exists with the provided nickname");
            }

            // Use database transaction to ensure atomicity and prevent race conditions
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Secure referral code consumption with row-level locking and atomic update
                var refCode = await ConsumeReferralCodeSecurely(formBusinessObject.ReferralCode,
                    clientIp, formBusinessObject.NickName, operationId);

                var friendInfo = await userManager.FindByIdAsync(refCode.CreatedBy);
                ArgumentNullException.ThrowIfNull(friendInfo);

                // Create new user
                var user = new ApplicationUser
                {
                    Id = Guid.NewGuid().ToString().ToLower(),
                    UserName = formBusinessObject.NickName,
                    DisplayName = formBusinessObject.DisplayName,
                    AvatarData = formBusinessObject.AvatarData,
                    LockoutEnabled = true,
                    Pub1 = formBusinessObject.Pub1,
                    Pub2 = formBusinessObject.Pub2
                };

                var result = await userManager.CreateAsync(user, formBusinessObject.PassKey!);

                if (!result.Succeeded)
                {
                    // If user creation fails, rollback the transaction
                    await transaction.RollbackAsync();
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogError("User creation failed. OperationId: {OperationId}, ClientIP: {ClientIP}, Errors: {Errors}",
                        operationId, clientIp, errors);
                    throw new Exception($"User creation failed: {errors}");
                }

                await userManager.UpdateAsync(user);

                var friendship = new Friendship
                {
                    Id = CombineGuidsXor(user.Id, friendInfo.Id),
                    UserId = user.Id,
                    FriendId = friendInfo.Id,
                    AvatarData = friendInfo.AvatarData,
                    TagLine = "No Tagline added",
                    Name = friendInfo.DisplayName ?? "No Name"
                };
                _context.Friendships.Add(friendship);
                await _context.SaveChangesAsync();

                //reciprocal friendship creation

                var reciprocalFriendship = new Friendship
                {
                    Id = CombineGuidsXor(friendInfo.Id, user.Id),
                    UserId = friendInfo.Id,
                    FriendId = user.Id,
                    AvatarData = user.AvatarData,
                    TagLine = "No Tag line added",
                    Name = user.DisplayName ?? "No Name"
                };
                _context.Friendships.Add(reciprocalFriendship);
                await _context.SaveChangesAsync();

                // Commit the transaction
                await transaction.CommitAsync();

                _logger.LogInformation("Signup completed successfully. OperationId: {OperationId}, ClientIP: {ClientIP}, UserId: {UserId}, ReferralCode: {ReferralCode}",
                    operationId, clientIp, user.Id, HashSensitiveData(formBusinessObject.ReferralCode));

                var authClaims = new AuthorizationClaimsModel(CreateJwtToken(user),
                    RefreshToken(user),
                    user.Id,
                    user.UserName,
                    user.Pub1!,
                    user.Pub2!,
                    user.DisplayName,
                    user.AvatarData,
                    user.AvatarDescription);
                return JsonSerializer.Serialize(authClaims);
            }
            catch
            {
                // Rollback transaction on any error
                await transaction.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Signup failed. OperationId: {OperationId}, ClientIP: {ClientIP}, Nickname: {Nickname}",
                operationId, clientIp, formBusinessObject.NickName);
            throw;
        }
    }

    public string CombineGuidsXor(string guid1, string guid2)
    {
        string combined = string.Empty;
        for (int i = 0; i < guid1.Length; i++)
        {
            combined += (i % 2 == 0) ? guid1[i] : guid2[i];
        }

        return combined;
    }

    /// <summary>
    /// Validates signup input with enhanced security checks
    /// </summary>
    private async Task ValidateSignupInputSecurely(SignupFormBusinessObject formBusinessObject, string clientIp)
    {
        // Input validation
        if (string.IsNullOrWhiteSpace(formBusinessObject.NickName))
        {
            throw new ArgumentException("Nickname is required");
        }

        if (string.IsNullOrWhiteSpace(formBusinessObject.ReferralCode))
        {
            throw new ArgumentException("Referral code is required");
        }

        // Security validations
        if (formBusinessObject.NickName.Length > 50)
        {
            throw new ArgumentException("Nickname is too long");
        }

        if (formBusinessObject.ReferralCode.Length > 20)
        {
            throw new ArgumentException("Referral code format is invalid");
        }

        // Check for suspicious patterns
        if (ContainsSuspiciousPatterns(formBusinessObject.NickName))
        {
            _logger.LogWarning("Suspicious nickname pattern detected. ClientIP: {ClientIP}, Nickname: {Nickname}",
                clientIp, formBusinessObject.NickName);
            throw new ArgumentException("Invalid nickname format");
        }

        // Additional async validations can be added here
        await Task.CompletedTask;
    }


    /// <summary>
    /// Checks for suspicious patterns in user input
    /// </summary>
    private bool ContainsSuspiciousPatterns(string input)
    {
        if (string.IsNullOrEmpty(input)) return false;

        // Check for common injection patterns
        var suspiciousPatterns = new[]
        {
            "script", "javascript", "vbscript", "onload", "onerror",
            "select", "insert", "update", "delete", "drop", "union",
            "<", ">", "\"", "'", ";", "--", "/*", "*/"
        };

        return suspiciousPatterns.Any(pattern =>
            input.ToLowerInvariant().Contains(pattern.ToLowerInvariant()));
    }

    /// <summary>
    /// Hashes sensitive data for logging purposes
    /// </summary>
    private string HashSensitiveData(string data)
    {
        if (string.IsNullOrEmpty(data)) return "null";

        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(data));
        return Convert.ToBase64String(hash)[..8] + "..."; // Show only first 8 chars of hash
    }

    /// <summary>
    /// Securely consumes a referral code with protection against race conditions, 
    /// replay attacks, and concurrent access issues.
    /// </summary>
    /// <param name="referralCode">The referral code to consume</param>
    /// <param name="clientIp">Client IP address for audit trail</param>
    /// <param name="consumedBy">Username consuming the code</param>
    /// <param name="operationId">Unique operation identifier for tracking</param>
    /// <returns>The consumed AuthCode entity</returns>
    private async Task<AuthCode> ConsumeReferralCodeSecurely(string referralCode, string clientIp, string consumedBy, string operationId)
    {
        _logger.LogInformation("Starting referral code consumption. OperationId: {OperationId}, ClientIP: {ClientIP}, ConsumedBy: {ConsumedBy}, ReferralCodeHash: {ReferralCodeHash}",
            operationId, clientIp, consumedBy, HashSensitiveData(referralCode));

        // Normalize the referral code (trim whitespace, convert to uppercase for consistency)
        var normalizedCode = referralCode?.Trim()?.ToUpperInvariant();

        if (string.IsNullOrEmpty(normalizedCode))
        {
            _logger.LogWarning("Invalid referral code format. OperationId: {OperationId}, ClientIP: {ClientIP}", operationId, clientIp);
            throw new ArgumentException("Invalid referral code format");
        }

        // Additional security: Check for code format validity (assuming 6-digit numeric codes)
        if (!IsValidReferralCodeFormat(normalizedCode))
        {
            _logger.LogWarning("Referral code format validation failed. OperationId: {OperationId}, ClientIP: {ClientIP}", operationId, clientIp);
            throw new ArgumentException("Invalid referral code format");
        }

        //todo: Check for potential brute force attempts via failed attempts

        try
        {
            // Use raw SQL with row-level locking to prevent race conditions
            // This ensures atomic read-and-update operation with enhanced security
            var sql = @"
                UPDATE AuthCodes WITH (UPDLOCK, SERIALIZABLE)
                SET AuthCodeStatus = @consumedStatus,
                    ConsumedBy = @consumedBy,
                    ConsumedAt = @consumedAt,
                    ConsumedByIp = @consumedByIp
                OUTPUT INSERTED.*
                WHERE Code = @code 
                    AND AuthCodeStatus = 0 
                    AND ExpiresAt > @currentTime
                    AND (ConsumedBy IS NULL OR ConsumedBy = '')
                    AND CreatedAt <= @maxCreatedTime"; // Prevent using codes created in the future

            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@code", normalizedCode),
                new Microsoft.Data.SqlClient.SqlParameter("@consumedStatus", (byte)AuthCodeStatus.Consumed),
                new Microsoft.Data.SqlClient.SqlParameter("@consumedBy", consumedBy),
                new Microsoft.Data.SqlClient.SqlParameter("@consumedAt", DateTime.UtcNow),
                new Microsoft.Data.SqlClient.SqlParameter("@consumedByIp", clientIp ?? "Unknown"),
                new Microsoft.Data.SqlClient.SqlParameter("@currentTime", DateTime.UtcNow),
                new Microsoft.Data.SqlClient.SqlParameter("@maxCreatedTime", DateTime.UtcNow.AddMinutes(1)) // Allow 1 minute clock skew
            };

            // Execute the atomic update operation
            var updatedCodes = await _context.AuthCodes
                .FromSqlRaw(sql, parameters)
                .ToListAsync();

            if (!updatedCodes.Any())
            {
                // Enhanced error handling with detailed logging
                await HandleReferralCodeConsumptionFailure(normalizedCode, clientIp, consumedBy, operationId);
            }

            // Additional security check: verify the code was consumed by the expected user
            var consumedCode = updatedCodes.First();
            if (consumedCode.ConsumedBy != consumedBy)
            {
                _logger.LogError("Security violation: Referral code consumption mismatch. OperationId: {OperationId}, Expected: {Expected}, Actual: {Actual}",
                    operationId, consumedBy, consumedCode.ConsumedBy);
                throw new Exception("Security violation: Referral code consumption mismatch");
            }

            // Verify the consumed code meets all security requirements
            if (!IsConsumedCodeValid(consumedCode, clientIp, consumedBy))
            {
                _logger.LogError("Consumed referral code failed security validation. OperationId: {OperationId}, CodeId: {CodeId}",
                    operationId, consumedCode.Id);
                throw new Exception("Security violation: Invalid code consumption");
            }

            _logger.LogInformation("Referral code consumed successfully. OperationId: {OperationId}, CodeId: {CodeId}, ConsumedBy: {ConsumedBy}",
                operationId, consumedCode.Id, consumedBy);

            return consumedCode;
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            _logger.LogError(ex, "Error during referral code consumption. OperationId: {OperationId}, ClientIP: {ClientIP}", operationId, clientIp);
            throw;
        }
    }

    /// <summary>
    /// Validates referral code format
    /// </summary>
    private bool IsValidReferralCodeFormat(string code)
    {
        if (string.IsNullOrEmpty(code)) return false;

        // Assuming 6-digit numeric codes (adjust based on your format)
        return code.Length == 6 && code.All(char.IsDigit);
    }


    /// <summary>
    /// Handles referral code consumption failure with detailed error analysis
    /// </summary>
    private async Task HandleReferralCodeConsumptionFailure(string normalizedCode, string clientIp, string consumedBy, string operationId)
    {
        // Check if code exists but is already consumed or expired for better error messaging
        var existingCode = await _context.AuthCodes
            .Where(x => x.Code == normalizedCode)
            .FirstOrDefaultAsync();

        if (existingCode == null)
        {
            _logger.LogWarning("Referral code not found. OperationId: {OperationId}, ClientIP: {ClientIP}, CodeHash: {CodeHash}",
                operationId, clientIp, HashSensitiveData(normalizedCode));
            throw new Exception("Invalid referral code");
        }
        else if (existingCode.AuthCodeStatus == AuthCodeStatus.Consumed)
        {
            _logger.LogWarning("Attempt to use already consumed referral code. OperationId: {OperationId}, ClientIP: {ClientIP}, CodeId: {CodeId}, OriginalConsumer: {OriginalConsumer}",
                operationId, clientIp, existingCode.Id, existingCode.ConsumedBy);
            throw new Exception("Referral code has already been used");
        }
        else if (existingCode.ExpiresAt <= DateTime.UtcNow)
        {
            _logger.LogWarning("Attempt to use expired referral code. OperationId: {OperationId}, ClientIP: {ClientIP}, CodeId: {CodeId}, ExpiredAt: {ExpiredAt}",
                operationId, clientIp, existingCode.Id, existingCode.ExpiresAt);
            throw new Exception("Referral code has expired");
        }
        else if (existingCode.AuthCodeStatus == AuthCodeStatus.Blocked)
        {
            _logger.LogWarning("Attempt to use blocked referral code. OperationId: {OperationId}, ClientIP: {ClientIP}, CodeId: {CodeId}",
                operationId, clientIp, existingCode.Id);
            throw new Exception("Referral code is blocked");
        }
        else
        {
            _logger.LogWarning("Referral code not available for unknown reason. OperationId: {OperationId}, ClientIP: {ClientIP}, CodeId: {CodeId}, Status: {Status}",
                operationId, clientIp, existingCode.Id, existingCode.AuthCodeStatus);
            throw new Exception("Referral code is not available for use");
        }
    }

    /// <summary>
    /// Validates the consumed code meets all security requirements
    /// </summary>
    private bool IsConsumedCodeValid(AuthCode consumedCode, string clientIp, string consumedBy)
    {
        // Verify basic properties
        if (consumedCode.AuthCodeStatus != AuthCodeStatus.Consumed) return false;
        if (consumedCode.ConsumedBy != consumedBy) return false;
        if (consumedCode.ConsumedByIp != clientIp && consumedCode.ConsumedByIp != "Unknown") return false;
        if (consumedCode.ConsumedAt == null) return false;

        // Verify consumption time is recent (within last minute)
        if (consumedCode.ConsumedAt.Value < DateTime.UtcNow.AddMinutes(-1)) return false;
        if (consumedCode.ConsumedAt.Value > DateTime.UtcNow.AddMinutes(1)) return false;

        return true;
    }

    /// <summary>
    /// Gets the client IP address from the current HTTP context
    /// </summary>
    /// <returns>Client IP address or "Unknown" if not available</returns>
    private string GetClientIpAddress()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
                return "Unknown";

            // Check for forwarded IP addresses (common in load balancer scenarios)
            var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                // Take the first IP if multiple are present
                var firstIp = forwardedFor.Split(',')[0].Trim();
                if (!string.IsNullOrEmpty(firstIp))
                    return firstIp;
            }

            // Check for real IP header (some proxies use this)
            var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
                return realIp;

            // Fall back to connection remote IP
            var remoteIp = httpContext.Connection.RemoteIpAddress?.ToString();
            if (!string.IsNullOrEmpty(remoteIp))
                return remoteIp;

            return "Unknown";
        }
        catch
        {
            // If anything fails, return Unknown rather than throwing
            return "Unknown";
        }
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public Task<SignupFormBusinessObject> GetItemByIdAsync(string id)
    {
        throw new NotImplementedException();
    }

    private string CreateJwtToken(ApplicationUser user)
    {
        var claims = new Claim[]
        {
                    new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                    new Claim(JwtRegisteredClaimNames.Exp, DateTime.UtcNow.AddMinutes(30).ToString()),
                    new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                    new Claim(ClaimTypes.Name, user.UserName?? string.Empty),
                    new Claim(ClaimTypes.UserData, user.AvatarData?? string.Empty)

        };


        return GenerateAccessToken(claims, 10000);

    }

    private string RefreshToken(ApplicationUser user)
    {
        var claims = new Claim[]
        {
                        new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                        new Claim(JwtRegisteredClaimNames.UniqueName, user.UserName),
                        new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                        //new Claim(JwtRegisteredClaimNames.Iat, DateTime.Now.ToUnixTimeSeconds().ToString()),
                        new Claim(JwtRegisteredClaimNames.Exp, DateTime.UtcNow.AddMinutes(100).ToString()),
                        new Claim(ClaimTypes.Role, "REFRESHTOKEN")

        };

        return GenerateAccessToken(claims, 20000);
    }

    private string GenerateAccessToken(Claim[] claims, int minutes)
    {
        var signingKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Key));
        var signingCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);
        var jwt = new JwtSecurityToken(
            signingCredentials: signingCredentials,
            claims: claims,
            notBefore: DateTime.UtcNow,
            expires: DateTime.UtcNow.AddMinutes(minutes),
            audience: _jwtSettings.Audience,
            issuer: _jwtSettings.Issuer
            );

        var token = new JwtSecurityTokenHandler().WriteToken(jwt);
        return token;
    }
}

public class JWTSettings
{
    public string Key { get; set; }
    public string PublicKey { get; set; }
    public int LifeTime { get; set; }
    public string Audience { get; set; }
    public string Issuer { get; set; }
}
