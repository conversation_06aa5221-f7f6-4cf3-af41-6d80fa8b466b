﻿using DeepMessage.Framework.Core;
using Microsoft.EntityFrameworkCore;

namespace DeepMessage.Cient.Common.Data;

public abstract class ClientSideListingDataService<TListingModel, TFilterBusinessObject> : IListingDataService<TListingModel, TFilterBusinessObject>
   where TFilterBusinessObject : BaseFilterBusinessObject
{
    public async Task<PagedDataList<TListingModel>> GetPaginatedItems(TFilterBusinessObject filterBusinessObject)
    {

        return await MaterializeQueryAsync(filterBusinessObject, GetQuery(filterBusinessObject), GetTotalRows());
    }

    public async Task<PagedDataList<TListingModel>> MaterializeQueryAsync(TFilterBusinessObject filterBusinessObject, IQueryable<TListingModel> query, int totalRows)

    {
        var resultBusinessObject = new PagedDataList<TListingModel>();
        if (query != null)
        {

            if (filterBusinessObject.UsePagination)
            {
                resultBusinessObject.TotalRows = totalRows;

                if (resultBusinessObject.TotalRows == -1)
                    resultBusinessObject.TotalRows = query.Count();

                resultBusinessObject.TotalPages = Convert.ToInt32(Math.Ceiling(resultBusinessObject.TotalRows / (double)filterBusinessObject.RowsPerPage));

                query = query.Skip((filterBusinessObject.CurrentIndex - 1) * filterBusinessObject.RowsPerPage).Take(filterBusinessObject.RowsPerPage); 
            }

            if (query is IAsyncEnumerable<TListingModel>)
            {
                resultBusinessObject.Items = await query.ToListAsync();
            }
            else
            {
                resultBusinessObject.Items =  query.ToList();
            }
        }
         
        return OnItemsLoaded(resultBusinessObject);
    }
     
    public int GetTotalRows()
    {
        return -1;
    }
     

    public abstract IQueryable<TListingModel> GetQuery(TFilterBusinessObject filterBusinessObject);

    protected virtual PagedDataList<TListingModel> OnItemsLoaded(PagedDataList<TListingModel> items)
    {
        return items;
    }
}
