using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using ModelFury.Briefly.MobileApp.Features.Account;
using Platform.Client.Services.Services;
using Platform.Framework.Core; 
using System.Security.Claims;
using System.Text.Json;

namespace Platform.Razor.Features.Authentication.SignUp
{
    public partial class SignUpForm
    {
        [Parameter, SupplyParameterFromQuery]
        public string? Code { get; set; }


        [Inject]
        IClientEncryptionService _encryptionService { get; set; } = default!;

        [Inject]
        ISecureKeyManager _secureKeyManager { get; set; } = default!;

        protected override async Task<SignupFormViewModel> CreateSelectedItem()
        {
            return new SignupFormViewModel
            {
                NickName = string.Empty,
                PassKey = string.Empty,
                DeviceString = await GetDeviceString(),
                ReferralCode = Code,
                ShowPassword = false
            };
        }

        /// <summary>
        /// Toggles password visibility
        /// </summary>
        private void TogglePasswordVisibility()
        {
            if (SelectedItem != null)
            {
                SelectedItem.ShowPassword = !SelectedItem.ShowPassword;
            }
        }

        /// <summary>
        /// Gets device information string
        /// </summary>
        private async Task<string> GetDeviceString()
        {
            try
            {
                var userAgent = await JsRuntime.InvokeAsync<string>("eval", "navigator.userAgent");
                var platform = await JsRuntime.InvokeAsync<string>("eval", "navigator.platform");
                return $"Web-{platform}-{userAgent.Split(' ').LastOrDefault()}";
            }
            catch
            {
                return "Web-Unknown-Browser";
            }
        }

        protected override SignupFormBusinessObject ConvertViewModelToBusinessModel(SignupFormViewModel formViewModel)
        {
            // Step 1: Generate RSA 2048-bit key pair on client-side
            var keyPair = _encryptionService.GenerateRSAKeyPairAsync();

            // Step 2: Derive AES-256 key from PassKey using username as deterministic salt
            var aesKey = _secureKeyManager.DeriveTemporaryAESKeyAsync(formViewModel.NickName!, formViewModel.PassKey!);

            // Step 3: Encrypt RSA private key with derived AES key
            var encryptedPrivateKey = _encryptionService.EncryptRSAPrivateKeyAsync(keyPair.PrivateKeyPem, aesKey);

            //await _localStorageService.SetValue(formBusinessObject.NickName!, ClaimTypes.Name);
            //await _localStorageService.SetValue(encryptedPrivateKey, "pub2e_");
            //await _localStorageService.SetValue(keyPair.PublicKeyPem, "pub1o_");


            var passKey = formViewModel.PassKey?.Trim();
            if (!string.IsNullOrEmpty(formViewModel.NickName))
            {
                passKey = $"{formViewModel.NickName}{_encryptionService.GenerateBitSignature(SelectedItem.PassKey!)}!";
            }
            ;

            var signupBusinessObject = new SignupFormBusinessObject()
            {
                NickName = formViewModel.NickName.Trim(),
                DeviceString = formViewModel.DeviceString,
                PassKey = passKey,
                ReferralCode = formViewModel.ReferralCode,
                Pub1 = keyPair.PublicKeyPem,
                Pub2 = encryptedPrivateKey
            };

            // Clear sensitive data from local variables
            Array.Clear(aesKey, 0, aesKey.Length);

            return signupBusinessObject;
        }

        /// <summary>
        /// Handles successful registration
        /// </summary>
        public override async Task OnAfterSaveAsync(string authResult)
        {
            try
            {

                //catch (Exception ex)
                //{
                //    // Clear any potentially stored sensitive data on error
                //    _secureKeyManager.ClearKeys();
                //    throw new Exception($"Signup failed: {ex.Message}");
                //}

                // Parse the authentication result
                var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(authResult);

                if (authClaims != null)
                {
                    // Store authentication tokens
                    await StoreAuthenticationTokens(authClaims);

                    //Initialize secure key manager with the keys in memory
                    await _secureKeyManager.DeriveAndStoreKeysAsync(SelectedItem.NickName!, SelectedItem.PassKey!);

                    //if (AuthStateProvider is IAuthenticationStateNotifier notifier)
                    //{
                    //    await notifier.NotifyAuthenticationStateChanged();
                    //}

                    Navigation.NavigateTo("/chat", replace: true);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing registration result");
                Error = "Registration succeeded but there was an error processing the result. Please try signing in.";
            }
        }

        /// <summary>
        /// Stores authentication tokens
        /// </summary>
        private async Task StoreAuthenticationTokens(AuthorizationClaimsModel authClaims)
        {
            try
            {
                var scope = ScopeFactory.CreateScope();
                var localStorage = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                await localStorage.SetValue(authClaims.Token, "auth_token");
                await localStorage.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
                await localStorage.SetValue(authClaims.Username, ClaimTypes.Name);
                await localStorage.SetValue(authClaims.Pub1, "pub1o_");
                await localStorage.SetValue(authClaims.Pub2, "pub2e_");

                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var user = await context.ApplicationUsers.FindAsync(authClaims.UserId);
                if (user == null)
                {
                    user = new ApplicationUser()
                    {
                        Id = authClaims.UserId,
                        NickName = authClaims.Username,
                        Hash = "dummy",
                    };
                    context.ApplicationUsers.Add(user);
                    await context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error storing authentication tokens");
            }
        }

    }
}
