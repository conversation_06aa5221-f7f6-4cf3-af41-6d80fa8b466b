﻿using KPlatform.Framework.Core;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using System.Reflection;
using System.Reflection.Metadata;
using System.Runtime.CompilerServices;

namespace Platform.Framework.Core
{
    public class FrameworkBaseComponent : ComponentBase
    {

        [Parameter]
        public string? ModalSize { get; set; }

        [Parameter]
        public bool DisplayDialogCross { get; set; } = true;

        [Parameter]
        public ModalDialogConfig? DialogConfig { get; set; }

        [Inject]
        public NavigationManager Navigation { get; set; } = null!;

        [Inject]
        public ILocalStorageService StorageService { get; set; } = null!;

        [Inject]
        public DialogService DialogService { get; set; } = null!;

        [Inject]
        public KtNotificationService NotificationService { get; set; } = null!;

        [Inject]
        public AlertService AlertService { get; set; } = null!;

        [Inject]
        protected IServiceScopeFactory ScopeFactory { get; set; } = null!;

        [Inject]
        public IJSRuntime JsRuntime { get; set; } = null!;

        [Inject]
        public AuthenticationStateProvider AuthStateProvider { get; set; } = null!;
   
        [Inject]
        public IAuthenticatedUser AuthenticatedUser { get; set; } = null!;
         
        protected override async Task OnInitializedAsync()
        { 

            if (AuthenticatedUser == null)
                throw new ArgumentNullException($"Authenticated user");

            ArgumentNullException.ThrowIfNull(NotificationService);
            ArgumentNullException.ThrowIfNull(AuthStateProvider);

            var state = await AuthStateProvider.GetAuthenticationStateAsync();
            var user = state?.User;

            if (user != null && user.Identity != null && user.Identity.IsAuthenticated)
            {
                AuthenticatedUser.UserId = user.GetUserId() ?? string.Empty;
                AuthenticatedUser.Username = user.GetUserName() ?? string.Empty;
                AuthenticatedUser.ProfileName = user.GetProfileName() ?? string.Empty;
                AuthenticatedUser.ImageUrl = user.GetImage() ?? string.Empty;

            }

            PubSub.Hub.Default.Subscribe<Tuple<string, string, dynamic>>((x) =>
            {
                if (x.Item1 == OperationId && x.Item2 == "Dialog Closed")
                {
                    DialogService_OnClose(x.Item3);
                }
            }); 
            await base.OnInitializedAsync();
        } 

        public string OperationId { get; set; } = Guid.NewGuid().ToString().ToLower();

        protected KeyValuePair<string, object?> P(object? value, [CallerArgumentExpression("value")] string? variableName = null)
        {
            if (variableName == null) throw new ArgumentNullException(nameof(variableName));
            return new KeyValuePair<string, object?>(variableName.Split('.').Last(), value);
        }
         

        [Inject]
        private ILogger<FrameworkBaseComponent>? Logger { get; set; }

        
        

        public static Type? GetTypeByFullName(string typeFullName)
        {
            // Loop through all assemblies in the current AppDomain
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies().Where(x => x.FullName.StartsWith("K")))
            {
                // Look for the type in each assembly
                var type = assembly.GetType(typeFullName);
                if (type != null)
                {
                    return type; // Return the type if found
                }
            }

            return null; // Return null if the type was not found
        } 

       

        public enum Size
        { 
            Sm,
            Md,
            Xl,
            Xl2,
            Xl3,
            Xl4,
            Xl5,
            Xl6,
            Xl7,
            Xl8,
            FullScreen,
        }

        public enum Position_
        {
            None = 0,
            Right,
            Center
        }

        protected void ShowDialog<T>(string title, object? id, Size dialogSize = Size.Xl,
            Position_ position = Position_.Right, bool showCrossIcon = true,
            params List<KeyValuePair<string, object?>> parameters) where T : ComponentBase
        {
            ShowDialog(typeof(T), title, id, dialogSize, position, showCrossIcon, parameters);
        }

    
        protected void ShowDialog(Type dialogType, string title, object? id, Size dialogSize = Size.Xl,
           Position_ position = Position_.Right, bool showCrossIcon = true,
           params List<KeyValuePair<string, object?>> parameters)
        {

            var config = new ModalDialogConfig()
            {
                Component = dialogType,
                Title = title,
                ShowCrossIcon = DisplayDialogCross,
            };

            var parameters_ = parameters.ToDictionary();
            parameters_.Add("DialogConfig", config);
            parameters_.Add("Id", id);
            parameters_.Add("OperationId", OperationId);

            if (DialogService == null)
                throw new Exception("Dialog service is not initialized");

            switch (position)
            {
                case Position_.Center:
                    config.PositionClasses = "justify-center h-auto";
                    config.DialogContainerClasses = "grid place-items-center";
                    break;
                case Position_.Right:
                    config.PositionClasses = "justify-end min-h-[calc(100%-0.5rem)] h-[calc(100%-0.5rem)]";
                    config.DialogContainerClasses = "flex justify-end";
                    break;
            }
            // Mobile-first responsive dialog sizing with Nothing Phone aesthetic
            switch (dialogSize)
            {
                case Size.Sm:
                    // Small dialogs: Mobile-optimized for forms like captcha, login
                    config.SizeClasses = "w-11/12 max-w-sm sm:w-full sm:max-w-md";
                    break;
                case Size.Md:
                    // Medium dialogs: Balanced for most content
                    config.SizeClasses = "w-11/12 max-w-md sm:w-full sm:max-w-lg md:max-w-xl";
                    break;
                case Size.Xl:
                    // Large dialogs: Content-heavy forms and listings
                    config.SizeClasses = "w-11/12 max-w-lg sm:w-full sm:max-w-xl md:max-w-2xl";
                    break;
                case Size.Xl2:
                    // Extra large: Complex forms with multiple sections
                    config.SizeClasses = "w-11/12 max-w-xl sm:w-full sm:max-w-2xl md:max-w-3xl";
                    break;
                case Size.Xl3:
                    // Very large: Data tables and complex layouts
                    config.SizeClasses = "w-11/12 max-w-2xl sm:w-full sm:max-w-3xl md:max-w-4xl";
                    break;
                case Size.Xl4:
                    // Huge: Full-featured applications
                    config.SizeClasses = "w-11/12 max-w-3xl sm:w-full sm:max-w-4xl md:max-w-5xl";
                    break;
                case Size.Xl5:
                    // Maximum: Dashboard-style layouts
                    config.SizeClasses = "w-11/12 max-w-4xl sm:w-full sm:max-w-5xl md:max-w-6xl";
                    break;
                case Size.Xl6:
                    // Ultra-wide: Advanced data visualization
                    config.SizeClasses = "w-11/12 max-w-5xl sm:w-full sm:max-w-6xl md:max-w-7xl";
                    break;
                case Size.Xl7:
                    // Full-screen alternative: Complex applications
                    config.SizeClasses = "w-11/12 max-w-6xl sm:w-full sm:max-w-7xl md:max-w-screen-xl";
                    break;
                case Size.Xl8:
                    // Maximum screen utilization: Admin interfaces
                    config.SizeClasses = "w-11/12 max-w-7xl sm:w-full sm:max-w-screen-xl md:max-w-screen-2xl";
                    break;

            }

            config.Parameters = parameters_!;
            DialogService.ShowDialogAsync(config);
            StateHasChanged();

        }
         
          
        public virtual void DialogService_OnClose(dynamic obj)
        {
            Console.WriteLine("Dialog closed");
        }

        protected void CloseDialog()
        {
            if (DialogConfig != null)
            {
                //await JsRuntime.InvokeVoidAsync("closeDialog", DialogConfig.Id);
                //await Task.Delay(50);
                DialogService.Dialogs.Remove(DialogConfig);
            }
        }

    }
}
