﻿using KPlatform.Framework.Core;
using Platform.Framework.Core;
using System.Security.Claims;
 
    public class MauiAuthenticatedUser : IAuthenticatedUser
    {

        public MauiAuthenticatedUser(ILocalStorageService storageService)
        {

            storageService.GetValue("browsersession").ContinueWith(x =>
            {
                if (x.Result == null)
                {
                    BrowserId = Guid.NewGuid().ToString();
                    storageService.SetValue(BrowserId, "browsersession");
                }
                else
                {
                    BrowserId = x.Result;
                }
            });

            storageService.GetValue(ClaimTypes.NameIdentifier).ContinueWith(x =>
            {
                UserId = x.Result;
            });
             
            storageService.GetValue(ClaimTypes.GivenName).ContinueWith(x =>
            {
                ProfileName = x.Result;
            });
 
            storageService.GetValue(ClaimTypes.UserData).ContinueWith(x =>
            {
                ImageUrl = x.Result;
            });
            
            storageService.GetValue(ClaimTypes.Name).ContinueWith(x =>
            {
                Username = x.Result;
            });
        }

        public string UserId { get; set; }
        public string ProfileName { get; set; }
      
        public string ImageUrl { get; set; }
         
        public string BrowserId { get; set; }
        public string? Username { get; set; }
    }

