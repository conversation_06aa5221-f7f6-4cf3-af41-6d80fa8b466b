﻿namespace DeepMessage.Framework.Enums
{
    public enum SystemClaimType
    {
        Obsolete = 0,

        SystemDefault = 100,

    }

    public class SystemClaimAttribute : Attribute
    {
        public SystemClaimAttribute(SystemClaimType claimType)
        {
            ClaimType = claimType;
        }

        public SystemClaimType ClaimType { get; }
    }

    public enum ConversationType
    {
        Direct = 0,
        Group = 1
    }
}
