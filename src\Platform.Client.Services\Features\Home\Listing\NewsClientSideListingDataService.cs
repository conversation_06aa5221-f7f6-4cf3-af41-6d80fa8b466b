﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core; 
using DeepMessage.MauiApp.Helpers;
using DeepMessage.ServiceContracts.Features.Home;
namespace Platform.Client.Services.Features.Home;
public class NewsClientSideListingDataService : INewsListingDataService
{

	private readonly BaseHttpClient _httpClient;

	public NewsClientSideListingDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<PagedDataList<NewsListingBusinessObject>> GetPaginatedItems(NewsFilterBusinessObject filterBusinessObject)
	{
		return await _httpClient.GetFromJsonAsync<PagedDataList<NewsListingBusinessObject>>($"api/NewsListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());
	}
}
