using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Features.Account;
using DeepMessage.ServiceContracts.Features.Configurations;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ModelFury.Briefly.MobileApp.Features.Account;
using Platform.Client.Services.Services;
using Platform.Framework.Core;
using System.Security.Claims;
using System.Text.Json;

namespace Platform.Razor.Features.Authentication.SignIn
{
    public partial class SignInForm
    {
        private IClientEncryptionService _encryptionService = default!;
        private ISecureKeyManager _secureKeyManager = default!;

        // UI State Properties
        private bool usernameFieldFocused;
        public bool UsernameFieldFocused
        {
            get => usernameFieldFocused;
            set
            {
                usernameFieldFocused = value;
                StateHasChanged();
            }
        }

        private bool passwordFieldFocused;
        public bool PasswordFieldFocused
        {
            get => passwordFieldFocused;
            set
            {
                passwordFieldFocused = value;
                StateHasChanged();
            }
        }

        [Inject] private IClientEncryptionService ClientEncryptionService { get; set; } = default!;
        [Inject] private ISecureKeyManager SecureKeyManager { get; set; } = default!;

        protected override async Task OnInitializedAsync()
        {
            _encryptionService = ClientEncryptionService;
            _secureKeyManager = SecureKeyManager;
            await base.OnInitializedAsync();
        }

        protected override async Task<SignInFormViewModel> CreateSelectedItem()
        {
            var defaultPass = string.Empty;
#if DEBUG
            defaultPass = "Cancel55%%";
#endif

            return await Task.FromResult(new SignInFormViewModel
            {
                PassKey = defaultPass,
                DeviceString = $"web-{Environment.MachineName}-{Environment.OSVersion.Platform}",
                ShowPassword = false
            });
        }

        protected override SignInFormBusinessObject ConvertViewModelToBusinessModel(SignInFormViewModel formViewModel)
        {
            var passKey = formViewModel.PassKey?.Trim();
            if (!string.IsNullOrEmpty(formViewModel.NickName))
            {
                passKey = $"{formViewModel.NickName}{_encryptionService.GenerateBitSignature(formViewModel.PassKey!)}!";
            }

            return new SignInFormBusinessObject()
            {
                NickName = formViewModel.NickName?.Trim(),
                DeviceString = formViewModel.DeviceString,
                PassKey = passKey
            };
        }

        private void TogglePasswordVisibility()
        {
            if (SelectedItem != null)
            {
                SelectedItem.ShowPassword = !SelectedItem.ShowPassword;
                StateHasChanged();
            }
        }

        private async Task GoToSignup()
        {
            Navigation.NavigateTo("/signup");
        }

        public override async Task OnAfterSaveAsync(string authResult)
        {
            try
            {
                var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(authResult);

                if (authClaims != null)
                {
                    await StoreAuthenticationTokens(authClaims);
                    await _secureKeyManager.DeriveAndStoreKeysAsync(SelectedItem.NickName!, SelectedItem.PassKey!);

                    // Navigate to chat threads
                    Navigation.NavigateTo("/chat", replace: true);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing authentication result");
                Error = "Authentication failed. Please try again.";
                StateHasChanged();
            }
        }

        private async Task StoreAuthenticationTokens(AuthorizationClaimsModel authClaims)
        {
            try
            {
                await StorageService.SetValue(authClaims.Token, "auth_token");
                await StorageService.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
                await StorageService.SetValue(authClaims.Username, ClaimTypes.Name);
                await StorageService.SetValue(authClaims.Pub1, "pub1o_");
                await StorageService.SetValue(authClaims.Pub2, "pub2e_");

                // Sync user profile data including avatar
                using var scope = ScopeFactory.CreateScope();
                var profileSyncService = scope.ServiceProvider.GetRequiredService<Platform.Client.Services.Features.Account.IProfileSyncService>();
                await profileSyncService.SyncUserProfileAsync(authClaims.UserId, authClaims.Username);

                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var user = await context.ApplicationUsers.FindAsync(authClaims.UserId);
                if (user == null)
                {
                    user = new ApplicationUser()
                    {
                        Id = authClaims.UserId,
                        NickName = authClaims.Username,
                        Hash = "dummy",
                    };
                    context.ApplicationUsers.Add(user);
                }

                user.Pub1 = authClaims.Pub1;
                user.Pub2 = authClaims.Pub2;
                user.DisplayName = authClaims.DisplayName;
                user.AvatarData = authClaims.AvatarData;
                user.AvatarDescription = authClaims.AvatarDescription;
                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error storing authentication tokens");
                // Don't throw as authentication was successful
            }
        }
    }
}