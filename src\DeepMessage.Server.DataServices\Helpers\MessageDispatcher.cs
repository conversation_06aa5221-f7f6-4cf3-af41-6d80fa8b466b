﻿using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using FirebaseAdmin.Messaging;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Threading.Channels;

namespace DeepMessage.Server.DataServices.Helpers;

public class MessageDispatcher
{
    private Channel<ChatMessagesSyncFormBusinessObject> ChatMessages { get; set; }

    private Channel<ChatMessageUpdate> ChatMessageUpdates { get; set; }

    private readonly DeepChatHub chatHub;

    private ILogger<MessageDispatcher> _logger;

    public MessageDispatcher(IServiceScopeFactory scopeFactory, DeepChatHub chatHub, ILogger<MessageDispatcher> logger)
    {
        ChatMessages = Channel.CreateUnboundedPrioritized(
            new UnboundedPrioritizedChannelOptions<ChatMessagesSyncFormBusinessObject>()
            {
                Comparer = Comparer<ChatMessagesSyncFormBusinessObject>.Create((x, y) => x.Id.CompareTo(y.Id))
            });

        ChatMessageUpdates = Channel.CreateUnbounded<ChatMessageUpdate>();

        // Message dispatcher for sending messages to SignalR clients and handling push notifications
        Task.Factory.StartNew(async () =>
        {
            var fiveMinutesAgo = DateTime.UtcNow.AddMinutes(-5);
            while (true)
            {
                try
                {
                    var message = await ChatMessages.Reader.ReadAsync();
                    try
                    {
                        var scope = scopeFactory.CreateScope();
                        var newsService = scope.ServiceProvider.GetRequiredService<RssNewsHelper>();
                        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                        var memoryCache = scope.ServiceProvider.GetRequiredService<IMemoryCache>();

                        // ✅ SECURE: Query MessageRecipients for encrypted content delivery
                        var messageRecipients = context.MessageRecipients.Where(x => x.MessageId == message.Id
                                && x.MessageDeliveryStatus < MessageDeliveryStatus.DeliveredToEndUser
                                && x.RecipientId != message.SenderId).ToList();

                        foreach (var recipient in messageRecipients)
                        {
                            var userName = await memoryCache.GetOrCreateAsync($"username_{recipient.RecipientId}", async entry =>
                            {
                                var user = await context.Users.FirstOrDefaultAsync(x => x.Id == recipient.RecipientId);
                                return user.UserName;
                            }); // just for logging/debugging

                            var utcNow = DateTime.UtcNow; 
                            var result = await chatHub.SendMessageAsync(recipient.RecipientId, message.Id, JsonSerializer.Serialize(message));
                            if (result) // Fixed: was hardcoded to false
                            {
                                recipient.MessageDeliveryStatus = MessageDeliveryStatus.SentToEndUserViaSignalR;
                                await context.SaveChangesAsync();

                                await context.Messages.Where(x => x.Id == message.Id && x.DeliveryStatus < MessageDeliveryStatus.SentToEndUserViaSignalR)
                                 .ExecuteUpdateAsync(x => x
                                 .SetProperty(p => p.DeliveryStatus, MessageDeliveryStatus.SentToEndUserViaSignalR)
                                 .SetProperty(p => p.DeliveryStatusTime, utcNow));

                                DispatchMessageUpdate(new ChatMessageUpdate()
                                {
                                    MessageId = message.Id,
                                    TargetUserId = message.SenderId,
                                    SenderId = recipient.RecipientId,
                                    DeliveryStatusTime = utcNow,
                                    DeliveryStatus = MessageDeliveryStatus.SentToEndUserViaSignalR,
                                });

                                logger.LogInformation("Message {MessageId} delivered via SignalR to user {UserId}", message.Id, recipient.RecipientId);
                            }
                            else if (message.EnableFallBackChannel)
                            {
                                var deviceToken = await memoryCache.GetOrCreateAsync($"devicetoken_{recipient.RecipientId}", async entry =>
                               {
                                   var temp = await context.UserDevices.Where(x => x.UserId == recipient.RecipientId)
                                   .Select(x => x.DeviceToken).FirstOrDefaultAsync();
                                   return temp;
                               });

                                // 2) Prepare the message with actual chat content
                                var fcmMessage = new FirebaseAdmin.Messaging.Message()
                                {
                                    Token = deviceToken,
                                    Notification = new Notification()
                                    {
                                        Title = "Trump signs a new tariff bill",
                                        Body = "News headline",
                                    },
                                    Data = new Dictionary<string, string>
                                    {
                                        ["messageId"] = message.Id,
                                        ["conversationId"] = message.ConversationId,
                                        ["senderId"] = message.SenderId,
                                        ["type"] = "chat_message"
                                    },
                                    Android = new AndroidConfig
                                    {
                                        Priority = Priority.High,
                                        TimeToLive = TimeSpan.FromHours(24),
                                        CollapseKey = $"chat_{message.ConversationId}",
                                        Notification = new AndroidNotification
                                        {
                                            ChannelId = "chat_messages",
                                            Sound = "default"
                                        }
                                    },
                                    Apns = new ApnsConfig
                                    {
                                        Aps = new Aps
                                        {
                                            Alert = new ApsAlert
                                            {
                                                Title = "Trump signs a new tariff bill",
                                                Body = "News headline",
                                            },
                                            Badge = 1,
                                            Sound = "default"
                                        }
                                    }
                                };

                                // 3) Send push notification with error handling
                                try
                                {
                                    if (!string.IsNullOrEmpty(deviceToken))
                                    {
                                        string response = await FirebaseMessaging.DefaultInstance.SendAsync(fcmMessage);
                                        logger.LogInformation("Push notification sent successfully. MessageId: {MessageId}, Response: {Response}", message.Id, response);

                                        recipient.MessageDeliveryStatus = MessageDeliveryStatus.SentToEndUserViaPushNotification;
                                        await context.SaveChangesAsync();
                                    }
                                    else
                                    {
                                        logger.LogWarning("No device token found for user {UserId}, message {MessageId}", recipient.RecipientId, message.Id);
                                        recipient.MessageDeliveryStatus = MessageDeliveryStatus.DeliveryFailed;
                                        await context.SaveChangesAsync();
                                    }
                                }
                                catch (Exception fcmEx)
                                {
                                    logger.LogError(fcmEx, "Failed to send push notification for message {MessageId} to user {UserId}", message.Id, recipient.RecipientId);
                                    recipient.MessageDeliveryStatus = MessageDeliveryStatus.DeliveryFailed;
                                    await context.SaveChangesAsync();
                                }

                                await context.Messages.Where(x => x.Id == message.Id && x.DeliveryStatus < MessageDeliveryStatus.SentToEndUserViaPushNotification)
                               .ExecuteUpdateAsync(x => x
                               .SetProperty(p => p.DeliveryStatus, MessageDeliveryStatus.SentToEndUserViaPushNotification)
                               .SetProperty(p => p.DeliveryStatusTime, utcNow));

                                DispatchMessageUpdate(new ChatMessageUpdate()
                                {
                                    MessageId = message.Id,
                                    TargetUserId = recipient.Id,
                                    SenderId = message.SenderId,
                                    DeliveryStatusTime = utcNow,
                                    DeliveryStatus = MessageDeliveryStatus.SentToEndUserViaPushNotification,
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        DispatchMessage(message);
                        logger.LogWarning(ex.Message);
                    }
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex.Message);
                }
            }
        });

        // Message update dispatcher for sending updates to SignalR clients
        Task.Factory.StartNew(async () =>
        {
            while (true)
            {
                try
                {
                    var message = await ChatMessageUpdates.Reader.ReadAsync();
                    var result = await chatHub.SendMessageUpdateAsync(message.TargetUserId, message.MessageId, message.DeliveryStatus);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                }
            }
        });

        // Periodic task to check for pending messages and dispatch them
        Task.Factory.StartNew(async () =>
         {
             while (true)
             {
                 await Task.Delay(10000);
                 var fiveMinutesAgo = DateTime.UtcNow.AddMinutes(-1);
                 var scope = scopeFactory.CreateScope();
                 var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                 var pendingMessages = (from m in context.Messages
                                        from recipients in context.MessageRecipients.Where(x => x.MessageId == m.Id)
                                        from user in context.Users.Where(x => x.Id == recipients.RecipientId)
                                        where
                                        //m.Id == "0195ffba-4ae1-7a10-8dc2-84f438f42825" &&
                                        recipients.MessageDeliveryStatus == MessageDeliveryStatus.Pending ||
                                        (recipients.MessageDeliveryStatus == MessageDeliveryStatus.QueuedToUpSync && recipients.DeliveryStatusTime < fiveMinutesAgo) ||
                                         (recipients.MessageDeliveryStatus == MessageDeliveryStatus.SentToEndUserViaSignalR && recipients.DeliveryStatusTime < fiveMinutesAgo)
                                        orderby m.Id
                                        select new ChatMessagesSyncFormBusinessObject
                                        {
                                            Id = m.Id,
                                            MessageRecepientId = recipients.Id,
                                            MessageRecepientUserName = user.UserName,
                                            ConversationId = m.ConversationId,
                                            SenderId = m.SenderId,
                                            CreatedAt = m.CreatedAt,
                                            DeletedAt = m.DeletedAt,
                                            DisappearAfter = m.DisappearAfter,
                                            DisappearAt = m.DisappearAt,
                                            EditedAt = m.EditedAt,
                                            IsDeleted = m.IsDeleted,
                                            IsEdited = m.IsEdited,
                                            IsEphemeral = m.IsEphemeral,
                                        }).ToList();
                 pendingMessages = pendingMessages.DistinctBy(x => x.Id).ToList();
                 foreach (var item in pendingMessages)
                 {
                     DispatchMessage(item);
                 }

                 var recipientIds = pendingMessages.Select(x => x.MessageRecepientId).ToList();
                 await context.MessageRecipients
                       .Where(x => recipientIds.Contains(x.Id))
                       .ExecuteUpdateAsync(x => x.SetProperty(p => p.MessageDeliveryStatus, MessageDeliveryStatus.QueuedToUpSync)
                       .SetProperty(p => p.DeliveryStatusTime, DateTime.UtcNow));
             }
         });

        // Periodic task to check pending updates and dispatch them
        Task.Factory.StartNew(async () =>
        {
            while (true)
            {
                try
                {
                    var scope = scopeFactory.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                    var pendingUpdates = (from r in context.MessageRecipients
                                          join m in context.Messages on r.MessageId equals m.Id
                                          where r.DeliveryAcknowledgementStatus == AcknowledgementStatus.SentToMessageServer
                                          select new ChatMessageUpdate
                                          {
                                              MessageId = m.Id,
                                              TargetUserId = m.SenderId,
                                              SenderId = r.RecipientId,
                                              DeliveryStatus = r.MessageDeliveryStatus,
                                              DeliveryStatusTime = r.DeliveryStatusTime ?? DateTime.UtcNow,
                                              MessageRecipientId = r.Id // Add this to track which MessageRecipient was processed
                                          }).ToList();

                    foreach (var item in pendingUpdates)
                    {
                        DispatchMessageUpdate(item);
                    }

                    // ✅ FIXED: Update the correct MessageRecipient records that were just processed
                    var processedRecipientIds = pendingUpdates.Select(u => u.MessageRecipientId).ToList();
                    await context.MessageRecipients
                        .Where(x => processedRecipientIds.Contains(x.Id))
                        .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryAcknowledgementStatus, AcknowledgementStatus.SentToEndUserViaSignalR));
                }
                finally
                {
                    await Task.Delay(10000);
                }
            }
        });

        this.chatHub = chatHub;
        _logger = logger;
    }

    public bool DispatchMessage(ChatMessagesSyncFormBusinessObject chatMessage)
    {
        return ChatMessages.Writer.TryWrite(chatMessage);
    }
    public bool DispatchMessageUpdate(ChatMessageUpdate chatMessageUpdate)
    {
        return ChatMessageUpdates.Writer.TryWrite(chatMessageUpdate);
    }
}