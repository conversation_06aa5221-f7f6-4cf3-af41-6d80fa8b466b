﻿using DeepMessage.Framework.Core;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Text.Json;

namespace Platform.Framework.Core
{
    public abstract class FormBase<TFormModel, TFormViewModel, TKey, TService> : FrameworkBaseComponent, INotifyPropertyChanged
        where TFormModel : class, new()
        where TFormViewModel : class, INotifyPropertyChanged, new()
        where TService : IFormDataService<TFormModel, TKey>

    {
        [SupplyParameterFromQuery(Name = "id")]
        [Parameter]
        public virtual TKey? Id { get; set; }

        [Parameter]
        public string? Position { get; set; }

        [Parameter]
        public bool KeepAlive { get; set; } = false;


        [Inject]
        protected ILogger<TService> Logger { get; set; } = default!;

        public string? Error { get; set; }

        private TFormViewModel _selectedItem = new TFormViewModel();

        public TFormViewModel SelectedItem
        {
            get
            {
                return _selectedItem;
            }
            set
            {
                _selectedItem = value;
                NotifyPropertyChanged();
            }
        }

        public int OnDialogClosed { get; set; }

        public bool IsWorking { get; set; } = true;

        public bool IsSaving { get; set; }

        public virtual string? ServiceKey { get; }

        public FormBase()
        {
            PropertyChanged += FormBase_PropertyChanged;
        }

        private void FormBase_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            try
            {

                if (e.PropertyName == nameof(SelectedItem) && SelectedItem != null)
                {
                    OnSelectedItemCreated(SelectedItem);
                }
            }
            catch (Exception ex)
            {
                Error = ex.Message;
            }
        }

        protected virtual void OnSelectedItemCreated(TFormViewModel model)
        {

        }

        protected virtual void OnArgsSet(object args)
        {

        }

        /// <summary>
        /// Called after service returns viewmodel from database
        /// </summary>
        /// <typeparam name="TFormViewMoel"></typeparam>
        /// <param name="formModel"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        protected virtual Task<TFormViewModel> ConvertBusinessModelToViewModel(TFormModel formModel)
        {
            if (formModel == null)
                throw new Exception("formModel is null while cloning");

            return Task.FromResult(formModel.Clone<TFormViewModel>());
        }


        protected override async Task OnInitializedAsync()
        {
            try
            {
                if (Id == null || Id.ToString() == "0" || string.IsNullOrEmpty(Id.ToString()) || Id.ToString() == "00000000-0000-0000-0000-000000000000")
                {
                    SelectedItem = await CreateSelectedItem();
                }
                else
                {
                    using var scope = ScopeFactory!.CreateScope();
                    var crudService = string.IsNullOrEmpty(ServiceKey) ? scope.ServiceProvider.GetRequiredService<TService>()
                                    : scope.ServiceProvider.GetKeyedService<TService>(ServiceKey);

                    var formModel = await crudService.GetItemByIdAsync(Id);
                    if (formModel == null)
                        throw new InvalidDataException("Selected Item is null after service.GetItemByIdAsync");

                    SelectedItem = await ConvertBusinessModelToViewModel(formModel);

                    if (SelectedItem == null)
                        throw new InvalidDataException("Selected Item is null after cloning SelectedItem");
                }
                SelectedItem.PropertyChanged += SelectedItem_PropertyChanged;

                using var scp = ScopeFactory!.CreateScope();
                await LoadSelectLists(scp);
                PubSub.Hub.Default.Publish(new Tuple<string>("CloseRefreshScreenIndicator"));

            }
            catch (UnauthorizedAccessException)
            {
                Navigation?.NavigateTo($"/home/<USER>");
            }
            catch (Exception ex)
            {
                Error = ex.Message;
                Logger.LogInformation(ex?.Message);
            }
            IsWorking = false;
            await base.OnInitializedAsync();
        }

        public virtual void SelectedItem_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {

        }


        protected virtual Task LoadSelectLists(IServiceScope scope)
        {
            return Task.CompletedTask;
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                try
                {
                    await InitialzeJsScrips(JsRuntime);
                }
                catch (Exception ex)
                {
                    Error = ex.Message + ex.InnerException?.Message;
                }
            }
        }

        protected virtual async Task InitialzeJsScrips(IJSRuntime jsRuntime)
        {
            await Task.CompletedTask;
        }


        /// <summary>
        /// Created SelectedItem with default values.
        /// Override this method if want to create and enforce specific rules on ComposeViewModel,
        /// </summary> 
        protected virtual Task<TFormViewModel> CreateSelectedItem()
        {
            return Task.FromResult(new TFormViewModel());
        }


        public virtual Task BeforeSaveAsync()
        {

            return Task.CompletedTask;
        }

        public virtual Task OnAfterSaveAsync(TKey key)
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// Called just before saving data, convert dates to UTC
        /// </summary>
        /// <param name="formViewModel"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        protected virtual TFormModel ConvertViewModelToBusinessModel(TFormViewModel formViewModel)
        {
            if (formViewModel == null)
                throw new Exception();

            return formViewModel.Clone<TFormModel>();
        }

        protected async Task HandleFormSubmit()
        {
            IsWorking = true;
            IsSaving = true;
            try
            {

                Error = null;

                if (ScopeFactory == null)
                    throw new ArgumentNullException(nameof(ScopeFactory));

                await BeforeSaveAsync();

                using var scope = ScopeFactory.CreateScope();
                var crudService = string.IsNullOrEmpty(ServiceKey) ? scope.ServiceProvider.GetRequiredService<TService>()
                                  : scope.ServiceProvider.GetKeyedService<TService>(ServiceKey);

                if (SelectedItem == null)
                    throw new InvalidOperationException("SelectedItem ViewModel is null before converting to business model");

                var businessModel = ConvertViewModelToBusinessModel(SelectedItem)
                    ?? throw new InvalidDataException("Business model is null before calling SaveAsync");

                TKey id = await crudService.SaveAsync(businessModel);

                if (id == null)
                    throw new InvalidDataException("Id is null after calling SaveAsync");

                if (!KeepAlive)
                {
                    ArgumentNullException.ThrowIfNull(OperationId, "OperationId is null");
                    PubSub.Hub.Default.Publish(new Tuple<string, string, dynamic>(OperationId, "Dialog Closed", id));
                    CloseDialog();
                }

                await OnAfterSaveAsync(id);
            }
            catch (UnauthorizedAccessException)
            {
                Navigation?.NavigateTo($"/home/<USER>");
            }
            catch (NavigationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Error = ex.Message;
            }
            finally
            {
                IsWorking = false;
                IsSaving = false;
            }
        }




        public event PropertyChangedEventHandler? PropertyChanged;

        public void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

    }
    public static class Ext
    {
        public static T Clone<T>(this object model)
        {
            var json = JsonSerializer.Serialize(model);
            var result = JsonSerializer.Deserialize<T>(json);
            if (result == null)
                throw new Exception("Object clonning failed");
            return result;
        }
    }
}
