﻿using DeepMessage.Framework.Core;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.AuthCodes;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
namespace DeepMessage.Server.DataServices.Features.AuthCodes;
public class AuthCodeServerSideFormDataService : IAuthCodeFormDataService
{

	private readonly AppDbContext _context;
    private readonly IHttpContextAccessor httpContextAccessor;

    public AuthCodeServerSideFormDataService (AppDbContext context, IHttpContextAccessor httpContextAccessor)
	{
		_context = context;
        this.httpContextAccessor = httpContextAccessor;
    }
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(AuthCodeFormBusinessObject formBusinessObject)
	{
        var userId = httpContextAccessor.HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier);
        var authCode = new AuthCode()
        {
            Id = Guid.CreateVersion7().ToString(),
            AuthCodeStatus = ServiceContracts.Enums.AuthCodeStatus.Unused,
            Code = Random.Shared.Next(111111, 999999).ToString(),
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
            ExpiresAt = DateTime.UtcNow.AddMinutes(5)
        };
        _context.AuthCodes.Add(authCode);
        await _context.SaveChangesAsync();
        return authCode.Code;
    }
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<AuthCodeFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return null;
	}
}
