﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DeepMessage.Client.Common.Migrations
{
    /// <inheritdoc />
    public partial class MessageDeliveryStatusV2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PlainContent",
                table: "Messages");

            migrationBuilder.RenameColumn(
                name: "SyncStatus",
                table: "MessageRecipients",
                newName: "ReadAcknowledgementStatus");

            migrationBuilder.RenameColumn(
                name: "DeliveryStatus",
                table: "MessageRecipients",
                newName: "MessageDeliveryStatus");

            migrationBuilder.AddColumn<byte>(
                name: "DeliveryAcknowledgementStatus",
                table: "MessageRecipients",
                type: "INTEGER",
                nullable: false,
                defaultValue: (byte)0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DeliveryAcknowledgementStatus",
                table: "MessageRecipients");

            migrationBuilder.RenameColumn(
                name: "ReadAcknowledgementStatus",
                table: "MessageRecipients",
                newName: "SyncStatus");

            migrationBuilder.RenameColumn(
                name: "MessageDeliveryStatus",
                table: "MessageRecipients",
                newName: "DeliveryStatus");

            migrationBuilder.AddColumn<string>(
                name: "PlainContent",
                table: "Messages",
                type: "TEXT",
                nullable: true);
        }
    }
}
