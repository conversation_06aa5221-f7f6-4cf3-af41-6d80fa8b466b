<?xml version="1.0" encoding="UTF-8" ?>
<?xaml-comp compile="true" ?>
<ResourceDictionary xmlns="http://schemas.microsoft.com/dotnet/2021/maui" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

    <!--  Note: For Android please see also Platforms\Android\Resources\values\colors.xml  -->

    <Color x:Key="Primary">#404040</Color> 
    <Color x:Key="Secondary" >#202020</Color> 

    <Color x:Key="White">White</Color>
    <Color x:Key="Black">Black</Color>
    <Color x:Key="Magenta">#6E6E6E</Color>
    <Color x:Key="MidnightBlue">#190649</Color>
    <Color x:Key="OffBlack">#1f1f1f</Color>

    <Color x:Key="Gray20">#FAFAFA</Color>
    <Color x:Key="Gray50">#F4F4F5</Color>
    <Color x:Key="Gray100">#E1E1E1</Color>
    <Color x:Key="Gray200">#C8C8C8</Color>
    <Color x:Key="Gray300">#ACACAC</Color>
    <Color x:Key="Gray400">#919191</Color>
    <Color x:Key="Gray500">#6E6E6E</Color>
    <Color x:Key="Gray600">#404040</Color>
    <Color x:Key="Gray700">#303030</Color>
    <Color x:Key="Gray800">#2A2A2A</Color>
    <Color x:Key="Gray900">#202020</Color>
    <Color x:Key="Gray950">#141414</Color>

    <Color x:Key="Secondary50">#fef2f2</Color>
    <Color x:Key="Secondary100">#fee2e2</Color>
    <Color x:Key="Secondary200">#fecaca</Color>
    <Color x:Key="Secondary300">#fca5a5</Color>
    <Color x:Key="Secondary400">#f87171</Color>
    <Color x:Key="Secondary500">#ef4444</Color>
    <Color x:Key="Secondary600">#dc2626</Color>
    <Color x:Key="Secondary700">#b91c1c</Color>
    <Color x:Key="Secondary800">#991b1b</Color>
    <Color x:Key="Secondary900">#7f1d1d</Color>
    <Color x:Key="Secondary950">#450a0a</Color>

    <Color x:Key="Blue600">#2563eb</Color>

    <SolidColorBrush x:Key="TitleBarColor" Color="{StaticResource Primary}" />
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource Primary}" />
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource Secondary}" /> 
    <SolidColorBrush x:Key="WhiteBrush" Color="{StaticResource White}" />
    <SolidColorBrush x:Key="BlackBrush" Color="{StaticResource Black}" />

    <SolidColorBrush x:Key="Gray100Brush" Color="{StaticResource Gray100}" />
    <SolidColorBrush x:Key="Gray200Brush" Color="{StaticResource Gray200}" />
    <SolidColorBrush x:Key="Gray300Brush" Color="{StaticResource Gray300}" />
    <SolidColorBrush x:Key="Gray400Brush" Color="{StaticResource Gray400}" />
    <SolidColorBrush x:Key="Gray500Brush" Color="{StaticResource Gray500}" />
    <SolidColorBrush x:Key="Gray600Brush" Color="{StaticResource Gray600}" />
    <SolidColorBrush x:Key="Gray900Brush" Color="{StaticResource Gray900}" />
    <SolidColorBrush x:Key="Gray950Brush" Color="{StaticResource Gray950}" />
 
    <!-- Theme-aware colors for UI components -->
    <Color x:Key="PageBackgroundColor">{AppThemeBinding Light={StaticResource White}, Dark={StaticResource Gray800}}</Color>
    <Color x:Key="CardBackgroundColor">{AppThemeBinding Light={StaticResource White}, Dark={StaticResource Gray800}}</Color>
    <Color x:Key="SurfaceColor">{AppThemeBinding Light={StaticResource Gray50}, Dark={StaticResource Gray700}}</Color>
    <Color x:Key="BorderColor">{AppThemeBinding Light={StaticResource Gray200}, Dark={StaticResource Gray600}}</Color>
    <Color x:Key="TextPrimaryColor">{AppThemeBinding Light={StaticResource Black}, Dark={StaticResource White}}</Color>
    <Color x:Key="TextSecondaryColor">{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray300}}</Color>
    <Color x:Key="TextTertiaryColor">{AppThemeBinding Light={StaticResource Gray500}, Dark={StaticResource Gray400}}</Color>
    <Color x:Key="PlaceholderColor">{AppThemeBinding Light=#9CA3AF, Dark=#6B7280}</Color>
    <Color x:Key="ErrorBackgroundColor">{AppThemeBinding Light=#FEF2F2, Dark=#7F1D1D}</Color>
    <Color x:Key="ErrorBorderColor">{AppThemeBinding Light=#FECACA, Dark=#991B1B}</Color>
    <Color x:Key="ErrorTextColor">{AppThemeBinding Light=#DC2626, Dark=#FCA5A5}</Color>
    <Color x:Key="OverlayColor">{AppThemeBinding Light=#66666666, Dark=#80000000}</Color>
    <Color x:Key="LoadingBackgroundColor">{AppThemeBinding Light={StaticResource White}, Dark={StaticResource Gray800}}</Color>
</ResourceDictionary>