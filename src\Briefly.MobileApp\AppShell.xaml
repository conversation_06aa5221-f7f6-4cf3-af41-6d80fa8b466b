<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="ModelFury.Briefly.MobileApp.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:account="clr-namespace:ModelFury.Briefly.MobileApp.Features.Account"
    xmlns:conversation="clr-namespace:Platform.Client.Common.Features.Conversation;assembly=Platform.Client.Common"
    xmlns:friends="clr-namespace:Platform.Client.Common.Features.Friends;assembly=Platform.Client.Common"
    xmlns:home="clr-namespace:ModelFury.Briefly.MobileApp.Features.Home"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp"
    xmlns:settings="clr-namespace:Platform.Client.Common.Features.Settings;assembly=Platform.Client.Common"
    Title="Briefly"
    BackgroundColor="White"
    Shell.FlyoutBehavior="Disabled">

    <Shell.Resources>
        <FontImageSource
            x:Key="MessagesLight"
            FontFamily="Jelly"
            Glyph="&#xf4ad;"
            Size="24"
            Color="{AppThemeBinding Light={StaticResource Gray700},
                                    Dark={StaticResource Gray300}}" />
        <FontImageSource
            x:Key="MessagesSolid"
            FontFamily="JellySolid"
            Glyph="&#xf4ad;"
            Size="24"
            Color="{AppThemeBinding Light={StaticResource Gray700},
                                    Dark={StaticResource Gray300}}" />

        <FontImageSource
            x:Key="FriendsLight"
            FontFamily="Jelly"
            Glyph="&#xf0c0;"
            Size="24"
            Color="{AppThemeBinding Light={StaticResource Gray700},
                                    Dark={StaticResource Gray300}}" />
        <FontImageSource
            x:Key="FriendsSolid"
            FontFamily="JellySolid"
            Glyph="&#xf0c0;"
            Size="24"
            Color="{AppThemeBinding Light={StaticResource Gray700},
                                    Dark={StaticResource Gray300}}" />

        <Style x:Key="_messages" TargetType="ShellContent">
            <Style.Triggers>
                <Trigger TargetType="ShellContent" Property="IsChecked" Value="False">
                    <Setter Property="Icon" Value="{StaticResource MessagesLight}" />
                </Trigger>
                <Trigger TargetType="ShellContent" Property="IsChecked" Value="True">
                    <Setter Property="Icon" Value="{StaticResource MessagesSolid}" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="_friends" TargetType="ShellContent">
            <Style.Triggers>
                <Trigger TargetType="ShellContent" Property="IsChecked" Value="False">
                    <Setter Property="Icon" Value="{StaticResource FriendsLight}" />
                </Trigger>
                <Trigger TargetType="ShellContent" Property="IsChecked" Value="True">
                    <Setter Property="Icon" Value="{StaticResource FriendsSolid}" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <FontImageSource
            x:Key="SettingsLight"
            FontFamily="Jelly"
            Glyph="&#xf1de;"
            Size="24"
            Color="{AppThemeBinding Light={StaticResource Gray700},
                                    Dark={StaticResource Gray300}}" />
        <FontImageSource
            x:Key="SettingsSolid"
            FontFamily="JellySolid"
            Glyph="&#xf1de;"
            Size="24"
            Color="{AppThemeBinding Light={StaticResource Gray700},
                                    Dark={StaticResource Gray300}}" />

        <FontImageSource
            x:Key="ProfileLight"
            FontFamily="Jelly"
            Glyph="&#xf2bd;"
            Size="24"
            Color="{AppThemeBinding Light={StaticResource Gray700},
                                    Dark={StaticResource Gray300}}" />
        <FontImageSource
            x:Key="ProfileSolid"
            FontFamily="JellySolid"
            Glyph="&#xf2bd;"
            Size="24"
            Color="{AppThemeBinding Light={StaticResource Gray700},
                                    Dark={StaticResource Gray300}}" />

        <Style x:Key="_settings" TargetType="ShellContent">
            <Style.Triggers>
                <Trigger TargetType="ShellContent" Property="IsChecked" Value="False">
                    <Setter Property="Icon" Value="{StaticResource SettingsLight}" />
                </Trigger>
                <Trigger TargetType="ShellContent" Property="IsChecked" Value="True">
                    <Setter Property="Icon" Value="{StaticResource SettingsSolid}" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="_profile" TargetType="ShellContent">
            <Style.Triggers>
                <Trigger TargetType="ShellContent" Property="IsChecked" Value="False">
                    <Setter Property="Icon" Value="{StaticResource ProfileLight}" />
                </Trigger>
                <Trigger TargetType="ShellContent" Property="IsChecked" Value="True">
                    <Setter Property="Icon" Value="{StaticResource ProfileSolid}" />
                </Trigger>
            </Style.Triggers>
        </Style>
    </Shell.Resources>


    <ShellContent
        ContentTemplate="{DataTemplate home:NewsListingComponent}"
        Route="signin"
        Shell.FlyoutBehavior="Disabled" />

    <TabBar>
        <ShellContent
            Title="Chats"
            ContentTemplate="{DataTemplate conversation:ChatThreadsListingView}"
            Route="messages"
            Style="{StaticResource _messages}" />

        <ShellContent
            Title="Friends"
            ContentTemplate="{DataTemplate friends:FriendsListingView}"
            Style="{StaticResource _friends}" />

        <ShellContent
            Title="Settings"
            ContentTemplate="{DataTemplate settings:SettingsView}"
            Style="{StaticResource _settings}" />

        <ShellContent
            Title="Profile"
            ContentTemplate="{DataTemplate account:ProfileListingView}"
            Style="{StaticResource _profile}" />

    </TabBar>

    <ShellContent
        ContentTemplate="{DataTemplate account:SignupFormView}"
        Route="signup"
        Shell.FlyoutBehavior="Disabled" />

    <ShellContent
        ContentTemplate="{DataTemplate account:SignInFormComponent}"
        Route="login"
        Shell.FlyoutBehavior="Disabled" />

</Shell>
