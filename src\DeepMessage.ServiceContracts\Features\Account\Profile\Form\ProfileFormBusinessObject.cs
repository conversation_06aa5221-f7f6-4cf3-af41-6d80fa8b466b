﻿using DeepMessage.Framework.Core;
using System.ComponentModel.DataAnnotations;

namespace DeepMessage.ServiceContracts.Features.Account;
public class ProfileFormBusinessObject
{
    [Required]
    public string? Id { get; set; }

    [Required]
    [StringLength(450)]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Stores the generated avatar image data as base64 string
    /// </summary>
    public string? AvatarData { get; set; }

    /// <summary>
    /// Description used to generate the avatar
    /// </summary>
    [StringLength(450)]
    public string? AvatarDescription { get; set; }
}
