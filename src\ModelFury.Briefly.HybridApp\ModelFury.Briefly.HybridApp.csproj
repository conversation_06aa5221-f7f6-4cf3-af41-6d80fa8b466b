﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <TargetFrameworks>net9.0-android</TargetFrameworks>
   
        <OutputType>Exe</OutputType>
        <RootNamespace>ModelFury.Briefly.HybridApp</RootNamespace>
        <UseMaui>true</UseMaui>
        <SingleProject>true</SingleProject>
        <ImplicitUsings>enable</ImplicitUsings>
        <EnableDefaultCssItems>false</EnableDefaultCssItems>
        <Nullable>enable</Nullable>

        <!-- Display name -->
        <ApplicationTitle>Briefly</ApplicationTitle>

        <!-- App Identifier -->
        <ApplicationId>com.modelfury.briefly</ApplicationId>

        <!-- Versions -->
        <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
        <ApplicationVersion>1</ApplicationVersion>
  
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">24.0</SupportedOSPlatformVersion>
    
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net9.0-android|AnyCPU'">
      <RunAOTCompilation>False</RunAOTCompilation>
      <PublishTrimmed>False</PublishTrimmed>
      <AndroidPackageFormat>apk</AndroidPackageFormat>
    </PropertyGroup>

    <ItemGroup>
        <!-- App Icon -->
        <MauiIcon Include="Resources\AppIcon\appicon.svg" />
        <MauiIcon Include="Resources\AppIcon\appiconfg.svg" />
        <MauiIcon Include="Resources\AppIcon\briefly.svg" />

        <!-- Splash Screen -->
        <MauiSplashScreen Include="Resources\Splash\splash.svg" />

        <!-- Images -->

        <!-- Custom Fonts -->
        <MauiFont Include="Resources\Fonts\*" />

        <!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
        <MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
    </ItemGroup>

	<ItemGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">
		<GoogleServicesJson Include="google-services.json" />
	</ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Components.WebView.Maui" Version="9.0.80" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.4" />
		<PackageReference Include="Xamarin.Google.Guava.ListenableFuture" Version="********" />
	</ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\DeepMessage.Framework.Core\Platform.Framework.Core.csproj" />
      <ProjectReference Include="..\DeepMessage.ServiceContracts\DeepMessage.ServiceContracts.csproj" />
      <ProjectReference Include="..\Platform.Client.Data\Platform.Client.Data.csproj" />
      <ProjectReference Include="..\Platform.Client.Services\Platform.Client.Services.csproj" />
      <ProjectReference Include="..\Platform.Razor\Platform.Razor.csproj" />
    </ItemGroup>

    <ItemGroup>
      <MauiXaml Update="AppShell.xaml">
        <Generator>MSBuild:Compile</Generator>
      </MauiXaml>
    </ItemGroup>

	<Target Name="Tailwind" BeforeTargets="Build">
		<exec command="npm run ui:build" />
	</Target>

	<ItemGroup>
	  <PackageReference Update="Microsoft.Maui.Controls" Version="9.0.80" />
	</ItemGroup>
</Project>
