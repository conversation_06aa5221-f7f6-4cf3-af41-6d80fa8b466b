﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Account;
namespace DeepMessage.Server.WebApis.Controller.Account;
[ApiController, Route("api/[controller]/[action]")]
public class ProfileListingController : ControllerBase, IProfileListingDataService
{

	private readonly IProfileListingDataService dataService;

	public ProfileListingController(IProfileListingDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<PagedDataList<ProfileListingBusinessObject>> GetPaginatedItems([FromQuery] ProfileFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
