@using Microsoft.AspNetCore.Components.Forms
@using Platform.Client.Services.Features.Media
@using Platform.Razor.Components.ImageUpload
@inject IImageUploadService ImageUploadService
@inject IJSRuntime JSRuntime

<!-- Image Upload Component - Sleek Minimal Design -->
<div class="image-upload-container">
    
    <!-- Current Image Display -->
    <div class="relative inline-block">
        @if (!string.IsNullOrEmpty(CurrentImageUrl))
        {
            <img src="@CurrentImageUrl" 
                 alt="@AltText" 
                 class="@GetImageClasses()" />
        }
        else if (!string.IsNullOrEmpty(PreviewImageUrl))
        {
            <img src="@PreviewImageUrl" 
                 alt="Preview" 
                 class="@GetImageClasses()" />
        }
        else
        {
            <!-- Placeholder with initials -->
            <div class="@GetPlaceholderClasses()">
                <span class="@GetInitialsClasses()">
                    @GetInitials()
                </span>
            </div>
        }
        
        <!-- Upload Button Overlay -->
        <button @onclick="TriggerFileInput"
                disabled="@IsUploading"
                class="@GetUploadButtonClasses()"
                aria-label="@(string.IsNullOrEmpty(CurrentImageUrl) ? "Upload image" : "Change image")">
            @if (IsUploading)
            {
                <div class="loading-spinner loading-spinner-sm"></div>
            }
            else
            {
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
            }
        </button>
    </div>

    <!-- Hidden File Input -->
    <InputFile @ref="fileInput" 
               OnChange="HandleFileSelected" 
               accept="image/jpeg,image/jpg,image/png,image/webp"
               style="display: none;" />

    <!-- Upload Progress -->
    @if (IsUploading)
    {
        <div class="mt-3">
            <div class="flex items-center justify-between text-sm text-secondary mb-1">
                <span>Uploading...</span>
                <span>@UploadProgress%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" 
                     style="width: @(UploadProgress)%"></div>
            </div>
        </div>
    }

    <!-- Error Message -->
    @if (!string.IsNullOrEmpty(ErrorMessage))
    {
        <div class="error-container mt-3">
            <div class="flex items-start">
                <svg class="error-icon mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <p class="error-title">Upload Error</p>
                    <p class="error-message">@ErrorMessage</p>
                </div>
            </div>
        </div>
    }

    <!-- Success Message -->
    @if (ShowSuccessMessage)
    {
        <div class="success-container mt-3">
            <div class="flex items-start">
                <svg class="success-icon mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <div>
                    <p class="success-title">Upload Successful</p>
                    <p class="success-message">Image uploaded and processed successfully</p>
                </div>
            </div>
        </div>
    }

    <!-- Help Text -->
    @if (!string.IsNullOrEmpty(HelpText))
    {
        <p class="text-xs text-secondary mt-2">@HelpText</p>
    }
</div>

@code {
    [Parameter] public string? CurrentImageUrl { get; set; }
    [Parameter] public string? PlaceholderText { get; set; }
    [Parameter] public string AltText { get; set; } = "Profile picture";
    [Parameter] public string HelpText { get; set; } = "Supported formats: JPG, PNG, WebP. Max size: 2MB";
    [Parameter] public ImageSize Size { get; set; } = ImageSize.Large;
    [Parameter] public EventCallback<string> OnImageUploaded { get; set; }
    [Parameter] public EventCallback<string> OnUploadError { get; set; }
    [Parameter] public int MaxSizeKB { get; set; } = 2048;

    private InputFile? fileInput;
    private string? PreviewImageUrl;
    private bool IsUploading = false;
    private int UploadProgress = 0;
    private string? ErrorMessage;
    private bool ShowSuccessMessage = false;
    private System.Timers.Timer? successTimer;

    private async Task TriggerFileInput()
    {
        if (fileInput?.Element != null)
        {
            await JSRuntime.InvokeVoidAsync("triggerFileInput", fileInput.Element);
        }
    }

    private async Task HandleFileSelected(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file == null) return;

        // Clear previous messages
        ErrorMessage = null;
        ShowSuccessMessage = false;

        try
        {
            // Validate file
            var validation = ImageUploadService.ValidateImage(file, MaxSizeKB);
            if (!validation.IsValid)
            {
                ErrorMessage = validation.ErrorMessage;
                await OnUploadError.InvokeAsync(ErrorMessage);
                return;
            }

            // Show preview
            IsUploading = true;
            UploadProgress = 10;
            StateHasChanged();

            // Create preview URL
            var buffer = new byte[file.Size];
            using var stream = file.OpenReadStream(MaxSizeKB * 1024);
            await stream.ReadAsync(buffer);
            var base64 = Convert.ToBase64String(buffer);
            PreviewImageUrl = $"data:{file.ContentType};base64,{base64}";
            
            UploadProgress = 30;
            StateHasChanged();

            // Upload image
            var result = await ImageUploadService.UploadProfilePictureAsync(file, MaxSizeKB);
            
            UploadProgress = 90;
            StateHasChanged();

            if (result.Success)
            {
                UploadProgress = 100;
                CurrentImageUrl = result.ImageUrl;
                PreviewImageUrl = null;
                ShowSuccessMessage = true;
                
                // Auto-hide success message
                successTimer?.Dispose();
                successTimer = new System.Timers.Timer(3000);
                successTimer.Elapsed += (s, e) => {
                    ShowSuccessMessage = false;
                    InvokeAsync(StateHasChanged);
                    successTimer?.Dispose();
                };
                successTimer.Start();

                await OnImageUploaded.InvokeAsync(result.ImageUrl!);
            }
            else
            {
                ErrorMessage = result.ErrorMessage;
                PreviewImageUrl = null;
                await OnUploadError.InvokeAsync(ErrorMessage!);
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Upload failed: {ex.Message}";
            PreviewImageUrl = null;
            await OnUploadError.InvokeAsync(ErrorMessage);
        }
        finally
        {
            IsUploading = false;
            UploadProgress = 0;
            StateHasChanged();
        }
    }

    private string GetImageClasses()
    {
        return Size switch
        {
            ImageSize.Small => "w-16 h-16 rounded-full object-cover border-2 border-border",
            ImageSize.Medium => "w-24 h-24 rounded-full object-cover border-2 border-border",
            ImageSize.Large => "w-32 h-32 rounded-full object-cover border-2 border-border",
            ImageSize.ExtraLarge => "w-40 h-40 rounded-full object-cover border-2 border-border",
            _ => "w-32 h-32 rounded-full object-cover border-2 border-border"
        };
    }

    private string GetPlaceholderClasses()
    {
        return Size switch
        {
            ImageSize.Small => "w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center border-2 border-border",
            ImageSize.Medium => "w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center border-2 border-border",
            ImageSize.Large => "w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center border-2 border-border",
            ImageSize.ExtraLarge => "w-40 h-40 rounded-full bg-gray-200 flex items-center justify-center border-2 border-border",
            _ => "w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center border-2 border-border"
        };
    }

    private string GetInitialsClasses()
    {
        return Size switch
        {
            ImageSize.Small => "text-lg font-semibold text-gray-600",
            ImageSize.Medium => "text-xl font-semibold text-gray-600",
            ImageSize.Large => "text-2xl font-semibold text-gray-600",
            ImageSize.ExtraLarge => "text-3xl font-semibold text-gray-600",
            _ => "text-2xl font-semibold text-gray-600"
        };
    }

    private string GetUploadButtonClasses()
    {
        var baseClasses = "absolute bottom-0 right-0 bg-primary-800 hover:bg-primary-900 text-white rounded-full p-2 shadow-lg transition-theme focus-ring-primary disabled:opacity-50";
        return Size switch
        {
            ImageSize.Small => $"{baseClasses} w-8 h-8",
            ImageSize.Medium => $"{baseClasses} w-10 h-10",
            ImageSize.Large => $"{baseClasses} w-12 h-12",
            ImageSize.ExtraLarge => $"{baseClasses} w-14 h-14",
            _ => $"{baseClasses} w-12 h-12"
        };
    }

    private string GetInitials()
    {
        if (string.IsNullOrEmpty(PlaceholderText))
            return "?";

        var parts = PlaceholderText.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length >= 2)
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        
        return parts[0][0].ToString().ToUpper();
    }

    public void Dispose()
    {
        successTimer?.Dispose();
    }
}
