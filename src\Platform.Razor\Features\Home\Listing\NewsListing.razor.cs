using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Features.Home;
using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Platform.Client.Data.EF;
using Platform.Framework.Core;
using Platform.Razor.Features.Authentication.Captcha;
using System.ComponentModel;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Timers;

namespace Platform.Razor.Features.Home.Listing
{
    public partial class NewsListing
    {

        private System.Timers.Timer? _searchTimer;
        private const int SearchDelayMs = 500; // Debounce search for 500ms

        private string stealthModeCode = "***";

        protected override async Task OnInitializedAsync()
        {
            // Initialize the search timer for debounced search
            _searchTimer = new System.Timers.Timer(SearchDelayMs);
            _searchTimer.Elapsed += OnSearchTimerElapsed;
            _searchTimer.AutoReset = false;

            await LoadStealthModeSettings();

            await ActivateStealthMode();

            await base.OnInitializedAsync();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                _ = LoadNewsFromServer();
            }
            await base.OnAfterRenderAsync(firstRender);
        }

        private async Task LoadNewsFromServer()
        {
            try
            {
                var scope1 = ScopeFactory.CreateScope();
                var newsService = scope1.ServiceProvider.GetRequiredKeyedService<INewsListingDataService>("client");
                var newsItems = await newsService.GetPaginatedItems(new NewsFilterBusinessObject());
                foreach (var item in newsItems.Items)
                {
                    try
                    {
                        var scope = ScopeFactory.CreateScope();
                        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                        var localNewsItem = await context.NewsItems.FirstOrDefaultAsync(x => x.Link == item.Link);
                        if (localNewsItem == null)
                        {
                            context.NewsItems.Add(new NewsItem()
                            {
                                Id = Guid.NewGuid().ToString(),
                                Title = item.Title ?? string.Empty,
                                Description = item.Description ?? string.Empty,
                                Link = item.Link ?? string.Empty,
                                ImageUrl = item.Thumbnail ?? string.Empty,
                                PubDate = item.PubDate
                            });
                            await context.SaveChangesAsync();
                        }
                    }
                    catch
                    {
                        // ignore which cannot be stored
                    }
                }
                await LoadItems(false);
            }
            catch
            {
                //ignore if server is not reachable
            }
        }

        /// <summary>
        /// Loads stealth mode settings from storage
        /// </summary>
        private async Task LoadStealthModeSettings()
        {
            try
            {
                //var stealthEnabled = await StorageService.GetValue("ask_code_on_cold");

                // Load custom stealth mode code
                var customCode = await StorageService.GetValue("stealth_mode_code");
                if (!string.IsNullOrEmpty(customCode))
                {
                    stealthModeCode = customCode;
                }
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "Error loading stealth mode settings");

                stealthModeCode = "***";
            }
        }



        protected override async void FilterViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(FilterViewModel.SearchKey))
            {
                if ((FilterViewModel.SearchKey != "***" || FilterViewModel.SearchKey != "###")
                    && FilterViewModel.SearchKey == stealthModeCode)
                {
                    await ActivateStealthMode();

                    StateHasChanged();
                    return;
                }
            }
        }


        /// <summary>
        /// Activates stealth mode and navigates to captcha screen
        /// </summary>
        private async Task ActivateStealthMode()
        {
            try
            {
                // Store the stealth activation timestamp
                var userName = await StorageService.GetValue(ClaimTypes.NameIdentifier);
                var caption = string.IsNullOrEmpty(userName) ? "Activate your News App" : "Prove You're not a Robot";
                ShowDialog<FakeCaptchaScreen>(caption, null, Size.Xl8, Position_.Center, true, P(true, "KeepAlive"));
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error activating stealth mode");
                Error = "Unable to activate stealth mode. Please try again.";
            }
        }

        /// <summary>
        /// Timer elapsed event for debounced search
        /// </summary>
        private void OnSearchTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            _ = InvokeAsync(async () =>
            {
                await LoadItems();
                StateHasChanged();
            });
        }

        /// <summary>
        /// Clears the search text and refreshes the list
        /// </summary>
        private async Task ClearSearch()
        {
            FilterViewModel.SearchKey = string.Empty;
            await LoadItems();
        }

        /// <summary>
        /// Opens an article in a new tab/window
        /// </summary>
        private async Task OpenArticle(string? url)
        {
            if (!string.IsNullOrEmpty(url))
            {
                await JsRuntime.InvokeVoidAsync("open", url, "_blank");
            }
        }

        /// <summary>
        /// Cleans HTML tags and entities from description text
        /// </summary>
        private string GetCleanDescription(string? description)
        {
            if (string.IsNullOrEmpty(description))
                return string.Empty;

            // Remove HTML tags
            var cleanText = Regex.Replace(description, "<.*?>", string.Empty);

            // Decode common HTML entities
            cleanText = cleanText
                .Replace("&amp;", "&")
                .Replace("&lt;", "<")
                .Replace("&gt;", ">")
                .Replace("&quot;", "\"")
                .Replace("&#39;", "'")
                .Replace("&nbsp;", " ");

            // Trim and limit length for better display
            cleanText = cleanText.Trim();
            if (cleanText.Length > 200)
            {
                cleanText = cleanText.Substring(0, 200) + "...";
            }

            return cleanText;
        }

        /// <summary>
        /// Refreshes the news items
        /// </summary>
        private async Task RefreshItems()
        {
            //Error = string.Empty;
            await LoadItems();
        }

        /// <summary>
        /// Checks if a thumbnail URL is valid and accessible
        /// </summary>
        private bool IsValidThumbnailUrl(string? thumbnailUrl)
        {
            if (string.IsNullOrEmpty(thumbnailUrl))
                return false;

            // Basic URL validation
            if (!Uri.TryCreate(thumbnailUrl, UriKind.Absolute, out var uri))
                return false;

            // Check if it's HTTP/HTTPS
            return uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps;
        }

        /// <summary>
        /// Gets a fallback image URL if the thumbnail is not available
        /// </summary>
        private string GetFallbackImageUrl()
        {
            // Return a placeholder image or default news icon
            return "/images/news-placeholder.svg";
        }

        /// <summary>
        /// Formats the publication date for display with relative time
        /// </summary>
        private string GetFormattedDate(DateTime pubDate)
        {
            var now = DateTime.UtcNow;
            var timeSpan = now - pubDate;

            if (timeSpan.TotalDays < 1)
            {
                if (timeSpan.TotalHours < 1)
                {
                    var minutes = (int)timeSpan.TotalMinutes;
                    return minutes <= 1 ? "Just now" : $"{minutes} minutes ago";
                }
                else
                {
                    var hours = (int)timeSpan.TotalHours;
                    return hours == 1 ? "1 hour ago" : $"{hours} hours ago";
                }
            }
            else if (timeSpan.TotalDays < 7)
            {
                var days = (int)timeSpan.TotalDays;
                return days == 1 ? "Yesterday" : $"{days} days ago";
            }
            else
            {
                return pubDate.ToString("MMM dd, yyyy");
            }
        }

        //protected override void Dispose()
        //{
        //    _searchTimer?.Stop();
        //    _searchTimer?.Dispose();
        //    base.Dispose();
        //}
    }
}
