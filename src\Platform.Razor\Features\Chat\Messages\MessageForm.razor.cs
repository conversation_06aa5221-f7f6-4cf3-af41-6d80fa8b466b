using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using Platform.Client.Services.Features.Conversation;
using Platform.Client.Services.Services;
using Platform.Client.Services.Features.Media;
using System.Timers;

namespace Platform.Razor.Features.Chat.Messages
{
    public partial class MessageForm
    {
        [Parameter] public string ConversationId { get; set; } = string.Empty;
        [Parameter] public EventCallback OnMessageSent { get; set; }
     
        private ElementReference messageInput;

        private InputFile? fileInput;

        private List<IBrowserFile>? selectedFiles;

        [Inject]
        IClientEncryptionService _encryptionService { get; set; } = default!;

        [Inject]
        ISecureKeyManager _secureKeyManager { get; set; } = default!;

        [Inject]
        IImageCompressionService _imageCompressionService { get; set; } = default!;

        protected override void OnInitialized()
        {
            if (SelectedItem != null)
            {
                SelectedItem.ConversationId = ConversationId;
            }
            base.OnInitialized();
        }

        protected override async Task<ChatMessageFormViewModel> CreateSelectedItem()
        {
            return await Task.FromResult(new ChatMessageFormViewModel
            {
                ConversationId = ConversationId,
                Content = string.Empty,
                ContentType = 0 // Text message
            });
        }

        protected override ChatMessageFormBusinessObject ConvertViewModelToBusinessModel(ChatMessageFormViewModel formViewModel)
        {
            return new ChatMessageFormBusinessObject
            {
                ConversationId = formViewModel.ConversationId,
                Content = formViewModel.Content?.Trim(),
                ContentType = formViewModel.ContentType,
                Attachments = formViewModel.Attachments
            };
        }

        /// <summary>
        /// Sends the message
        /// </summary>
        private async Task SendMessage()
        {
            // Allow sending if there's content OR attachments
            if ((string.IsNullOrWhiteSpace(SelectedItem?.Content) &&
                 (SelectedItem?.Attachments == null || !SelectedItem.Attachments.Any())) || IsWorking)
                return;

            try
            {
                await HandleFormSubmit();

                // Clear the input after successful send
                if (SelectedItem != null)
                {
                    SelectedItem.Content = string.Empty;
                    SelectedItem.Attachments?.Clear();
                }

                // Clear files if any
                selectedFiles?.Clear();

                // Focus back to input
                await JsRuntime.InvokeVoidAsync("focusElement", messageInput);

                // Notify parent component
                await OnMessageSent.InvokeAsync();
            }
            catch (Exception ex)
            {
                Error = ex.Message;
            }
        }

        /// <summary>
        /// Triggers the file upload dialog
        /// </summary>
        private async Task TriggerFileUpload()
        {
             
                await JsRuntime.InvokeVoidAsync("triggerFileInput", fileInput.Element);
           
        }

        /// <summary>
        /// Handles file selection and processes images
        /// </summary>
        private async Task HandleFileSelected(InputFileChangeEventArgs e)
        {
            try
            {
                selectedFiles = e.GetMultipleFiles(10).ToList(); // Limit to 10 files

                if (SelectedItem?.Attachments == null)
                    SelectedItem!.Attachments = new List<MediaAttachmentDto>();

                foreach (var file in selectedFiles.Where(f => f.ContentType.StartsWith("image/")))
                {
                    // Validate file size (10MB max)
                    if (file.Size > 10_000_000)
                    {
                        Error = $"File {file.Name} is too large. Maximum size is 10MB.";
                        continue;
                    }

                    // Read file data
                    using var stream = file.OpenReadStream(maxAllowedSize: 10_000_000);
                    var imageData = new byte[stream.Length];
                    await stream.ReadAsync(imageData);

                    // Validate image
                    if (!_imageCompressionService.IsValidImage(imageData))
                    {
                        Error = $"File {file.Name} is not a valid image.";
                        continue;
                    }

                    // Compress image
                    var compressedData = await _imageCompressionService.CompressImageAsync(imageData);

                    // Generate thumbnail
                    var thumbnailData = await _imageCompressionService.GenerateThumbnailAsync(compressedData);

                    // Add to attachments
                    SelectedItem.Attachments.Add(new MediaAttachmentDto
                    {
                        AttachmentType = "Image",
                        FileName = file.Name,
                        FileData = compressedData,
                        FileSizeBytes = compressedData.Length,
                        MimeType = file.ContentType,
                        ThumbnailData = thumbnailData
                    });
                }

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Error = $"Error processing files: {ex.Message}";
                StateHasChanged();
            }
        }

        /// <summary>
        /// Removes a selected file
        /// </summary>
        private void RemoveFile(IBrowserFile file)
        {
            selectedFiles?.Remove(file);
            StateHasChanged();
        }

        /// <summary>
        /// Removes an attachment from the message
        /// </summary>
        private void RemoveAttachment(MediaAttachmentDto attachment)
        {
            SelectedItem?.Attachments?.Remove(attachment);
            StateHasChanged();
        }

        /// <summary>
        /// Converts image data to data URL for display
        /// </summary>
        private string GetImageDataUrl(byte[] imageData, string? mimeType = null)
        {
            if (imageData == null || imageData.Length == 0)
                return string.Empty;

            var mime = mimeType ?? "image/jpeg";
            var base64 = Convert.ToBase64String(imageData);
            return $"data:{mime};base64,{base64}";
        }

        /// <summary>
        /// Handles media captured from camera or gallery
        /// </summary>
        private async Task HandleMediaCaptured(MediaCaptureResult result)
        {
            try
            {
                if (SelectedItem?.Attachments == null)
                    SelectedItem!.Attachments = new List<MediaAttachmentDto>();

                // Add the captured media as an attachment
                SelectedItem.Attachments.Add(new MediaAttachmentDto
                {
                    AttachmentType = "Image",
                    FileName = result.FileName,
                    FileData = result.CompressedData,
                    FileSizeBytes = result.CompressedSizeBytes,
                    MimeType = result.MimeType,
                    ThumbnailData = result.ThumbnailData
                });

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Error = $"Error processing captured media: {ex.Message}";
                StateHasChanged();
            }
        }

        /// <summary>
        /// Handles media capture errors
        /// </summary>
        private async Task HandleMediaError(string errorMessage)
        {
            Error = errorMessage;
            StateHasChanged();
            await Task.CompletedTask;
        }

        /// <summary>
        /// Clears all selected files
        /// </summary>
        private void ClearFiles()
        {
            selectedFiles?.Clear();
            StateHasChanged();
        }

        /// <summary>
        /// Formats file size for display
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        public override async Task OnAfterSaveAsync(string key)
        {
            // Message sent successfully
            await base.OnAfterSaveAsync(key);
        }

     

        //protected override void Dispose()
        //{
        //    _typingTimer?.Stop();
        //    _typingTimer?.Dispose();
        //    base.Dispose();
        //}
    }
}
