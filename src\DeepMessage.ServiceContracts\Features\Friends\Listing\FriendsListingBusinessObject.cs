﻿using DeepMessage.Framework.Core;
namespace DeepMessage.ServiceContracts.Features.Friends;
public class FriendsListingBusinessObject
{
    public string Id { get; set; } = null!;

    public string FriendId { get; set; } = null!;

    public string? TagLine { get; set; } = null!;

    public string Name { get; set; } = null!;

    public string? Status { get; set; }

    public string Pub1 { get; set; } = string.Empty;

    public string? AvatarData { get; set; } 
}
