<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base Application Theme with No Action Bar - Light Mode -->
    <style name="MainTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Nothing Phone Black Primary Colors -->
        <item name="android:colorPrimary">#18181b</item>
        <item name="android:colorPrimaryDark">#09090b</item>
        <item name="android:colorAccent">#ff0000</item>

        <!-- Light Theme Status Bar Configuration -->
        <item name="android:statusBarColor">@color/status_bar_light</item>
        <item name="android:windowLightStatusBar">true</item>

        <!-- Light Theme Navigation Bar Configuration -->
        <item name="android:navigationBarColor">@color/navigation_bar_light</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        <!-- Window Configuration for Full Screen -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        
        <!-- Edge-to-Edge Configuration -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        
        <!-- Enable theme-aware dark mode support -->
        <item name="android:forceDarkAllowed">true</item>
        
        <!-- Remove Window Title and Action Bar -->
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="MainTheme.Splash" parent="MainTheme">
        <!--<item name="android:windowBackground">@drawable/splash</item>-->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>
