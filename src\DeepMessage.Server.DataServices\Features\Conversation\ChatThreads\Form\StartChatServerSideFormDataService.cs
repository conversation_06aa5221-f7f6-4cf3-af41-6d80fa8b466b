﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
namespace DeepMessage.Server.DataServices.Features.Conversation.ChatThreads.Form;
public class StartChatServerSideFormDataService : IStartChatFormDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public StartChatServerSideFormDataService(AppDbContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(StartChatFormBusinessObject formBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var participant = new string?[]
        {
             userId?.ToUpper(),
            formBusinessObject.FriendId?.ToUpper()
        };

        var conversation = await _context.Conversations
                .Where(c => participant.All(p => c.Participants.Any(cp => cp.UserId.ToUpper() == p))).FirstOrDefaultAsync();

        if (conversation == null)
        {
            conversation = new Data.Conversation
            {
                Id = Guid.CreateVersion7().ToString().ToLower(),
                CreatedAt = DateTime.UtcNow,

                IsDeleted = false,
                Type = ConversationType.Direct,
                Title = "Direct Chat"

            };
            _context.Conversations.Add(conversation);

            var participants = new List<ConversationParticipant>
                        {
                            new ConversationParticipant
                            {
                                Id = Guid.CreateVersion7().ToString(),
                                UserId = userId!,
                                 ConversationId = conversation.Id,
                                IsAdmin = true,
                                JoinedAt = DateTime.UtcNow
                            },
                            new ConversationParticipant
                            {
                                Id = Guid.CreateVersion7().ToString(),
                                UserId = formBusinessObject.FriendId!,
                                ConversationId = conversation.Id,
                                IsAdmin = false,
                                JoinedAt = DateTime.UtcNow
                            }
                        };
            _context.ConversationParticipants.AddRange(participants);
            await _context.SaveChangesAsync();

        }
        return conversation.Id;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public Task<StartChatFormBusinessObject> GetItemByIdAsync(string id)
    {
        throw new NotImplementedException();
    }
}
