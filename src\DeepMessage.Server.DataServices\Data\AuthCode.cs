﻿using DeepMessage.ServiceContracts.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DeepMessage.Server.DataServices.Data;

public class AuthCode
{
    [StringLength(450), Key]
    public string Id { get; set; } = null!;

    [StringLength(450)]
    public string Code { get; set; } = null!; //todo: Self Hash

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime ExpiresAt { get; set; } = DateTime.UtcNow.AddMinutes(5);

    [StringLength(450)]
    public string CreatedBy { get; set; } = null!;

    [StringLength(450)]
    public string? ConsumedBy { get; set; } //todo: encrypted by signal protocol

    public DateTime? ConsumedAt { get; set; }

    [StringLength(450)]
    public string? ConsumedByIp { get; set; }

    public AuthCodeStatus AuthCodeStatus { get; set; }
}
