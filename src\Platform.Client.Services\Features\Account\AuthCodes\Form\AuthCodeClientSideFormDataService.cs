﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.AuthCodes;
namespace Platform.Client.Services.Features.AuthCodes;
public class AuthCodeClientSideFormDataService : IAuthCodeFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public AuthCodeClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(AuthCodeFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/AuthCodesForm/Save", formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<AuthCodeFormBusinessObject> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<AuthCodeFormBusinessObject>($"api/AuthCodesForm/GetItemById?id=" + id);
	}
}
