using DeepMessage.Server.DataServices;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Server.DataServices.Features.Account;
using DeepMessage.Server.DataServices.Features.AuthCodes;
using DeepMessage.Server.DataServices.Features.Configurations;
using DeepMessage.Server.DataServices.Features.Conversation;
using DeepMessage.Server.DataServices.Features.Conversation.ChatThreads.Form;
using DeepMessage.Server.DataServices.Features.Friends;
using DeepMessage.Server.DataServices.Features.Home;
using DeepMessage.Server.DataServices.Helpers;
using DeepMessage.Server.WebApis.Middleware;
using DeepMessage.ServiceContracts.Features.Account;
using DeepMessage.ServiceContracts.Features.AuthCodes;
using DeepMessage.ServiceContracts.Features.Configurations;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using DeepMessage.ServiceContracts.Features.Profile;
using DeepMessage.ServiceContracts.Features.Home;
using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Serilog;
using System.Text;
using DeepMessage.Server.DataServices.Features.Profile;
using DeepMessage.Server.DataServices.Data;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddDbContext<AppDbContext>(options =>
options.UseSqlServer(
      builder.Configuration.GetConnectionString("DefaultConnection"))
   );

builder.Services.AddDistributedSqlServerCache(options =>
{
    options.ConnectionString = builder.Configuration.GetConnectionString(
        "DefaultConnection");
    options.SchemaName = "dbo";
    options.TableName = "CacheEntries";
});
builder.Services.AddHybridCache();
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options => options.SignIn.RequireConfirmedAccount = false)
 .AddUserManager<UserManager<ApplicationUser>>()
 .AddSignInManager<SignInManager<ApplicationUser>>()
 .AddRoles<IdentityRole>()
 .AddEntityFrameworkStores<AppDbContext>()
 .AddDefaultTokenProviders();

var tokenValidationParameters = new TokenValidationParameters
{
    ValidateIssuerSigningKey = true,
    ValidateIssuer = true,
    ValidateAudience = true,
    ValidateLifetime = true,
    ClockSkew = TimeSpan.Zero,
    ValidIssuer = builder.Configuration["Tokens:Issuer"],
    ValidAudience = builder.Configuration["Tokens:Audience"],
    IssuerSigningKey = new Microsoft.IdentityModel.Tokens.SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Tokens:Key"]))
};
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(o =>
{
    o.RequireHttpsMetadata = false;
    o.SaveToken = true;
    o.TokenValidationParameters = tokenValidationParameters;
    o.Events = new JwtBearerEvents()
    {
        OnAuthenticationFailed = c =>
        {
            c.NoResult();
            return Task.CompletedTask;//c.Response.WriteAsync(c.Exception.ToString());
        },
        OnChallenge = context =>
        {

            context.HandleResponse();
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            context.Response.ContentType = "application/json";

            // Ensure we always have an error and error description.
            if (string.IsNullOrEmpty(context.Error))
                context.Error = "invalid_token";
            if (string.IsNullOrEmpty(context.ErrorDescription))
                context.ErrorDescription = "This request requires a valid JWT access token to be provided";

            // Add some extra context for expired tokens.
            if (context.AuthenticateFailure != null && context.AuthenticateFailure.GetType() == typeof(SecurityTokenExpiredException))
            {
                var authenticationException = context.AuthenticateFailure as SecurityTokenExpiredException;
                context.Response.Headers.Append("x-token-expired", authenticationException.Expires.ToString("o"));
                context.ErrorDescription = $"The token expired on {authenticationException.Expires.ToString("o")}";
            }
            var result = JsonConvert.SerializeObject(new
            {
                Errors = new List<string>() { context.Error, context.ErrorDescription },
                Succeeded = false
            });

            return context.Response.WriteAsync(result);
        },
        OnForbidden = context =>
        {
            context.Response.StatusCode = StatusCodes.Status403Forbidden;
            context.Response.ContentType = "application/json";
            var result = "You are not authorized to access this resource";
            return context.Response.WriteAsync(result);
        },
        OnMessageReceived = context =>
        {
            var accessToken = context.Request.Query["auth_token"];
            if (string.IsNullOrEmpty(accessToken) == false)
            {
                context.Token = accessToken;
            }
            return Task.CompletedTask;
        }
    };
});

builder.Services.AddAuthorization();

// Add rate limiting (commented out for now - requires .NET 8+ specific packages)
// builder.Services.AddRateLimiter(options =>
// {
//     options.AddFixedWindowLimiter("MessagePolicy", limiterOptions =>
//     {
//         limiterOptions.PermitLimit = 50;
//         limiterOptions.Window = TimeSpan.FromMinutes(1);
//     });
//     options.RejectionStatusCode = 429;
// });

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

builder.Services.AddControllers();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

builder.Services.Configure<JWTSettings>(builder.Configuration.GetSection("Tokens"));
builder.Services.AddHttpContextAccessor();
builder.Services.AddTransient<INewsListingDataService, NewsServerSideListingDataService>();
builder.Services.AddTransient<ISignupFormDataService, SignupServerSideFormDataService>();
builder.Services.AddTransient<ISignInFormDataService, SignInServerSideFormDataService>();
builder.Services.AddTransient<IChatThreadsListingDataService, ChatThreadsServerSideListingDataService>();
builder.Services.AddTransient<IStartChatFormDataService, StartChatServerSideFormDataService>();
builder.Services.AddTransient<IChatMessagesListingDataService, ChatMessagesServerSideListingDataService>();
builder.Services.AddTransient<IChatMessageFormDataService, ChatMessageServerSideFormDataService>();
builder.Services.AddScoped<IFriendsListingDataService, FriendsServerSideListingDataService>();
builder.Services.AddScoped<IFriendFormDataService, FriendServerSideFormDataService>();
builder.Services.AddScoped<IFriendProfileFormDataService, FriendProfileServerSideFormDataService>();
builder.Services.AddScoped<IProfileFormDataService, ProfileServerSideFormDataService>();
builder.Services.AddScoped<IAvatarFormDataService, AvatarServerSideFormDataService>();
builder.Services.AddScoped<ImageSeeder>();

builder.Services.AddScoped<IChatThreadSyncFormDataService, ChatThreadSyncServerSideFormDataService>();
builder.Services.AddScoped<IChatMessagesSyncFormDataService, ChatMessagesSyncServerSideFormDataService>();

builder.Services.AddScoped<IDeviceTokenFormDataService, DeviceTokenServerSideFormDataService>();
builder.Services.AddScoped<IAuthCodeListingDataService, AuthCodeServerSideListingDataService>();
builder.Services.AddScoped<IAuthCodeFormDataService, AuthCodeServerSideFormDataService>();

// Add new services
builder.Services.AddScoped<DeepMessage.Server.DataServices.Services.ISecurityService, DeepMessage.Server.DataServices.Services.SecurityService>();
builder.Services.AddSingleton<DeepMessage.Server.DataServices.Services.IPerformanceMonitoringService, DeepMessage.Server.DataServices.Services.PerformanceMonitoringService>();
builder.Services.AddScoped<DeepMessage.Server.DataServices.Services.IServerEncryptionService, DeepMessage.Server.DataServices.Services.ServerEncryptionService>();

builder.Services.AddSignalR();
builder.Services.AddSingleton<DeepChatHub>();
builder.Services.AddSingleton<MessageDispatcher>();
builder.Services.AddSingleton<RssNewsHelper>();
//Add support to logging with SERILOG
builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}
// Configure middleware pipeline
app.UseErrorHandling(); // Add error handling middleware
app.UseHttpsRedirection();
app.UseCors();
app.UseAuthentication();
app.UseAuthorization();

app.MapHub<DeepChatHub>("/chathub");
app.MapControllers().RequireRateLimiting("MessagePolicy");

var scope = app.Services.CreateScope();
scope.ServiceProvider.GetRequiredService<MessageDispatcher>();
scope.ServiceProvider.GetRequiredService<RssNewsHelper>().Start();
scope.ServiceProvider.GetRequiredService<AppDbContext>().Database.Migrate();
//await scope.ServiceProvider.GetRequiredService<ImageSeeder>().SeedAvatarImagesAsync();
FirebaseApp app_ = FirebaseApp.Create(new AppOptions()
{
    Credential = GoogleCredential.FromFile("briefly-service-account.json"),
});
app.Run();
