﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.AuthCodes;
namespace DeepMessage.Server.WebApis.Controller.AuthCodes;
[ApiController, Route("api/[controller]/[action]")]
public class AuthCodesFormController : ControllerBase, IAuthCodeFormDataService
{

	private readonly IAuthCodeFormDataService dataService;

	public AuthCodesFormController(IAuthCodeFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] AuthCodeFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<AuthCodeFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
