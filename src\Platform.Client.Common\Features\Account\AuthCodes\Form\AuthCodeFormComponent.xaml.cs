﻿using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.AuthCodes;
using Platform.Client.Services.Features.AuthCodes;
namespace Platform.Client.Common.Features.AuthCodes;
public class AuthCodeFormViewBase : FormBaseMaui<AuthCodeFormBusinessObject, AuthCodeFormViewModel, string, IAuthCodeFormDataService>
{
    public AuthCodeFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }
}

public partial class AuthCodeFormView : AuthCodeFormViewBase
{
    public AuthCodeFormView(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
        InitializeComponent();
        BindingContext = this;
    }

    public override Task OnAfterSaveAsync(string key)
    {
        SelectedItem.AuthCode = key;

        return base.OnAfterSaveAsync(key);
    }
}
