﻿using DeepMessage.Framework.Core;
namespace Platform.Client.Services.Features.Conversation;
public class ChatThreadsFilterViewModel :  BaseFilterViewModel
{
	private string _searchText = string.Empty;

	/// <summary>
	/// Search text for filtering chat threads by participant name or last message
	/// </summary>
	public string SearchText
	{
		get => _searchText;
		set
		{
			if (SetField(ref _searchText, value))
			{
				// Update the base SearchKey property when SearchText changes
				SearchKey = value;
			}
		}
	}
}
