using System.ComponentModel;

namespace Platform.Client.Common.Features.Settings;

public partial class SettingsView : ContentPage, INotifyPropertyChanged
{
    private bool _showCaptchaOnStartup;
    private string _secretCode = string.Empty;
    private bool _isDarkTheme;
    private bool _playSoundForNotifications = true;

    public SettingsView()
    {
        InitializeComponent();
        BindingContext = this;
        LoadSettings();
    }

    public bool ShowCaptchaOnStartup
    {
        get => _showCaptchaOnStartup;
        set
        {
            if (_showCaptchaOnStartup != value)
            {
                _showCaptchaOnStartup = value;
                OnPropertyChanged();
                SaveSettings();
            }
        }
    }

    public string SecretCode
    {
        get => _secretCode;
        set
        {
            if (_secretCode != value)
            {
                _secretCode = value;
                OnPropertyChanged();
                SaveSettings();
            }
        }
    }

    public bool IsDarkTheme
    {
        get => _isDarkTheme;
        set
        {
            if (_isDarkTheme != value)
            {
                _isDarkTheme = value;
                OnPropertyChanged();
                ApplyTheme();
                SaveSettings();
            }
        }
    }

    public bool PlaySoundForNotifications
    {
        get => _playSoundForNotifications;
        set
        {
            if (_playSoundForNotifications != value)
            {
                _playSoundForNotifications = value;
                OnPropertyChanged();
                SaveSettings();
            }
        }
    }

    private void LoadSettings()
    {
        // TODO: Load settings from storage service
        // This is a placeholder implementation
        _showCaptchaOnStartup = false;
        _secretCode = "***";
        _isDarkTheme = Application.Current?.RequestedTheme == AppTheme.Dark;
        _playSoundForNotifications = true;

        // Notify UI of initial values
        OnPropertyChanged(nameof(ShowCaptchaOnStartup));
        OnPropertyChanged(nameof(SecretCode));
        OnPropertyChanged(nameof(IsDarkTheme));
        OnPropertyChanged(nameof(PlaySoundForNotifications));
    }

    private void SaveSettings()
    {
        // TODO: Save settings to storage service
        // This is a placeholder implementation
        System.Diagnostics.Debug.WriteLine($"Settings saved: Captcha={ShowCaptchaOnStartup}, Theme={IsDarkTheme}, Sound={PlaySoundForNotifications}");
    }

    private void ApplyTheme()
    {
        if (Application.Current != null)
        {
            Application.Current.UserAppTheme = IsDarkTheme ? AppTheme.Dark : AppTheme.Light;
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
