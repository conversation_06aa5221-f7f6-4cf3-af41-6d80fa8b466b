﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
namespace DeepMessage.Server.WebApis.Controller.Conversation;
[ApiController, Authorize, Route("api/[controller]/[action]")]
public class ChatMessagesListingController : ControllerBase, IChatMessagesListingDataService
{

	private readonly IChatMessagesListingDataService dataService;

	public ChatMessagesListingController(IChatMessagesListingDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<PagedDataList<ChatMessagesListingBusinessObject>> GetPaginatedItems([FromQuery] ChatMessagesFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
