﻿using DeepMessage.Framework.Core;
namespace ModelFury.Briefly.MobileApp.Features.Account;
public class ProfileListingViewModel : ObservableBase
{
    private string _id = string.Empty;

    public string Id
    {
        get { return _id; }
        set { SetField(ref _id, value); }
    }

    private string _nickName = string.Empty;

    public string NickName
    {
        get { return _nickName; }
        set { SetField(ref _nickName, value); }
    }

    private string? _displayName;

    public string? DisplayName
    {
        get { return _displayName; }
        set { SetField(ref _displayName, value); }
    }


    private string? _avatarData;

    public string? AvatarData
    {
        get { return _avatarData; }
        set { SetField(ref _avatarData, value); }
    }



}
