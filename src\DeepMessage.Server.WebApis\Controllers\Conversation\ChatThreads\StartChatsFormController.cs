﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
namespace DeepMessage.Server.WebApis.Controllers.Conversation.ChatThreads;
[ApiController, Route("api/[controller]/[action]")]
public class StartChatsFormController : ControllerBase, IStartChatFormDataService
{

	private readonly IStartChatFormDataService dataService;

	public StartChatsFormController(IStartChatFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] StartChatFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<StartChatFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
