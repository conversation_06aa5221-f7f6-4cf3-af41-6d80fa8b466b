using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Platform.Client.Services.Features.Friends;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;

namespace Platform.Razor.Features.Friends.Listing
{
    public partial class FriendsListing : IDisposable
    {
        [Inject] private ILogger<FriendsListing> ComponentLogger { get; set; } = null!;

        private System.Timers.Timer? _searchTimer;
        private const int SearchDelayMs = 500; // Debounce search for 500ms
        private bool IsSyncing { get; set; } = false;
        private string? SuccessMessage { get; set; }
        private System.Timers.Timer? _successMessageTimer;
         
        protected override async Task OnInitializedAsync()
        {
            // Initialize the search timer for debounced search
            _searchTimer = new System.Timers.Timer(SearchDelayMs);
            _searchTimer.Elapsed += OnSearchTimerElapsed;
            _searchTimer.AutoReset = false;
            await base.OnInitializedAsync();
        }


        protected override async Task ItemsLoaded(IFriendsListingDataService service)
        {
            if (Items.Count == 0)
            {
                await CheckAndPerformSync();
            }
        }

        /// <summary>
        /// Handles search input with debouncing
        /// </summary>
        private void OnSearchKeyUp()
        {
            // Reset and restart the timer for debounced search
            _searchTimer?.Stop();
            _searchTimer?.Start();
        }

        /// <summary>
        /// Timer elapsed event for debounced search
        /// </summary>
        private async void OnSearchTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            await InvokeAsync(async () =>
            {
                // Trigger search when timer elapses
                await LoadItems();
                StateHasChanged();
            });
        }

        /// <summary>
        /// Clears the search text and refreshes the list
        /// </summary>
        private async Task ClearSearch()
        {
            FilterViewModel.SearchText = string.Empty;
            await LoadItems();
        }

        /// <summary>
        /// Checks if synchronization is needed and performs it
        /// </summary>
        private async Task CheckAndPerformSync()
        {
            try
            {
                var userId = await StorageService.GetValue(ClaimTypes.NameIdentifier);

                using var scope = ScopeFactory.CreateScope();
                var friendsService = scope.ServiceProvider.GetRequiredKeyedService<IFriendsListingDataService>("client"); ;

                var friendsData = await friendsService.GetPaginatedItems(new FriendsFilterBusinessObject() { UsePagination = false });

                foreach (var friend in friendsData.Items)
                {
                    var localScope = ScopeFactory.CreateScope();
                    var context = localScope.ServiceProvider.GetRequiredService<AppDbContext>();
                    var friendship = await context.Friendships.FirstOrDefaultAsync(x => x.Id == friend.Id);
                    if (friendship == null)
                    {
                        friendship = new Friendship()
                        {
                            Id = friend.Id,
                            UserId = userId,
                            FriendId = friend.FriendId, 
                            CreatedAt = DateTime.Now,

                        };
                        context.Friendships.Add(friendship);

                    }
                    friendship.Pub1 = friend.Pub1; // Friend's RSA public key for message encryption
                    friendship.Name = friend.Name;
                    friendship.AvatarData = friend.AvatarData; 
                    friendship.TagLine = friend.TagLine;
                    await context.SaveChangesAsync();
                }

            }
            catch (Exception ex)
            {
                ComponentLogger.LogError(ex, "Error during sync check and execution");
            }
        }



        /// <summary>
        /// Manually syncs friends from server
        /// </summary>
        private async Task SyncFriendsFromServer()
        {
            if (IsSyncing) return; // Prevent multiple simultaneous syncs

            try
            {
                IsSyncing = true;
                Error = string.Empty;
                StateHasChanged();

                ComponentLogger.LogInformation("Starting manual friends sync from server");
                
                await CheckAndPerformSync();
                await LoadItems(); // Refresh the list after sync
                
                // Show success message temporarily
                SuccessMessage = "Friends synced successfully!";
                ShowSuccessMessageTemporarily();
                
                ComponentLogger.LogInformation("Manual friends sync completed successfully");
            }
            catch (Exception ex)
            {
                ComponentLogger.LogError(ex, "Error during manual friends sync");
                Error = "Failed to sync friends from server. Please try again later.";
            }
            finally
            {
                IsSyncing = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Shows success message for 3 seconds
        /// </summary>
        private void ShowSuccessMessageTemporarily()
        {
            _successMessageTimer?.Stop();
            _successMessageTimer?.Dispose();
            
            _successMessageTimer = new System.Timers.Timer(3000); // 3 seconds
            _successMessageTimer.Elapsed += (sender, e) =>
            {
                InvokeAsync(() =>
                {
                    SuccessMessage = null;
                    StateHasChanged();
                });
                _successMessageTimer?.Dispose();
            };
            _successMessageTimer.AutoReset = false;
            _successMessageTimer.Start();
        }

        /// <summary>
        /// Shows the add friend form
        /// </summary>
        private void ShowAddFriendForm()
        {
            Navigation?.NavigateTo("/friends/add");
        }

        /// <summary>
        /// Starts a chat with the selected friend
        /// </summary>
        private async Task StartChat(FriendsListingViewModel friend)
        {
            try
            {
                var scope = ScopeFactory!.CreateAsyncScope();
                var startChatFormDataService = scope.ServiceProvider.GetRequiredService<IStartChatFormDataService>();
                var chatId = await startChatFormDataService.SaveAsync(new StartChatFormBusinessObject()
                {
                    FriendId = friend.FriendId
                });
                Navigation?.NavigateTo($"/chat/{chatId}?ChatTitle={friend.Name}&ChatIcon={friend.AvatarData}");
            }
            catch (Exception ex)
            {
                ComponentLogger.LogError(ex, "Error starting chat with friend {FriendId}", friend.FriendId);
                Error = "Failed to start chat. Please try again later.";
            }
        }

        /// <summary>
        /// Opens the friend profile editing form
        /// </summary>
        private void EditFriend(FriendsListingViewModel friend)
        {
            Navigation?.NavigateTo($"/friends/profile/{friend.Id}");
        }

        /// <summary>
        /// Gets the initials from a name for avatar display
        /// </summary>
        private string GetInitials(string? name)
        {
            if (string.IsNullOrEmpty(name))
                return "?";

            var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0)
                return "?";

            if (parts.Length == 1)
                return parts[0].Substring(0, Math.Min(2, parts[0].Length)).ToUpper();

            return $"{parts[0][0]}{parts[^1][0]}".ToUpper();
        }

        /// <summary>
        /// Refreshes the friends list
        /// </summary>
        private async Task RefreshItems()
        {
            Error = string.Empty;
            await LoadItems();
        }


        void IDisposable.Dispose()
        {
            _searchTimer?.Stop();
            _searchTimer?.Dispose();
            
            _successMessageTimer?.Stop();
            _successMessageTimer?.Dispose();
        }
    }
}
