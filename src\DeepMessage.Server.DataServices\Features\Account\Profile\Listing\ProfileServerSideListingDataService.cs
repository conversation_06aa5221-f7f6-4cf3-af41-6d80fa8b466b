﻿using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Account;
namespace DeepMessage.Server.DataServices.Features.Account;
public class ProfileServerSideListingDataService : ServerSideListingDataService<ProfileListingBusinessObject, ProfileFilterBusinessObject>, IProfileListingDataService
{

    private readonly AppDbContext _context;

    public ProfileServerSideListingDataService(AppDbContext context)
    {
        _context = context;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public override IQueryable<ProfileListingBusinessObject> GetQuery(ProfileFilterBusinessObject filterBusinessObject)
    {
        return (from u in _context.Users
                select new ProfileListingBusinessObject
                {
                    Id = u.Id,
                    NickName = u.UserName,
                    AvatarData = u.AvatarData,
                    DisplayName = u.DisplayName
                });
    }
}
