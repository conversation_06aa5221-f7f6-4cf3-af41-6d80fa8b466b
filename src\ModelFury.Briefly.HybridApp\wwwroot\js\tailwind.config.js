module.exports = {
    content: {
        files: [
            "../Platform.Razor/**/*.{razor,cshtml,js,cs,html}",
            "**/*.{razor,cshtml,cs,html}",
        ],
        transform: {
            // `razor` here is the file extension of your razor view files
            razor: (content) => content.replace(/@@/g, "@"),
        }
    },
    darkMode: 'class',
    theme: {
        container: {
            center: true,
            padding: '1rem',
            screens: {
                sm: '640px',
                md: '768px',
                lg: '1024px',
                xl: '1152px',
            }
        },
        fontFamily: {
            'display': ['Inter', 'sans-serif'],
            'body': ['Inter', 'sans-serif'],
            'fa': ['"Font Awesome 6 Pro"']
        },
        // Mobile-First Responsive Breakpoints
        screens: {
            'xs': '375px',   // Small phones
            'sm': '640px',   // Large phones
            'md': '768px',   // Tablets
            'lg': '1024px',  // Small laptops
            'xl': '1280px',  // Large laptops
            '2xl': '1536px', // Desktops
        },
        extend: {
            fontSize: {
                '2xs': ['0.625rem', '0.75rem'],
                '3xs': ['0.5rem', '0.625rem'],
            },
            spacing: {
                '18': '4.5rem'
            },
            boxShadow: {
                'xs': '0px 0px 1px rgba(0, 0, 0, 0.25)',
            },
            colors: {
                // === SEMANTIC COLOR SYSTEM ===
                // Primary Scale (Nothing Phone Black) - Main color scale
                'primary': {
                    '50': 'var(--dm-primary-50)',
                    '100': 'var(--dm-primary-100)',
                    '200': 'var(--dm-primary-200)',
                    '300': 'var(--dm-primary-300)',
                    '400': 'var(--dm-primary-400)',
                    '500': 'var(--dm-primary-500)',
                    '600': 'var(--dm-primary-600)',
                    '700': 'var(--dm-primary-700)',
                    '800': 'var(--dm-primary-800)',
                    '900': 'var(--dm-primary-900)',
                    '950': 'var(--dm-primary-950)',
                    // Semantic aliases for common usage
                    'DEFAULT': 'var(--dm-text-primary)',
                },

                // Secondary Scale (Nothing Phone Red) - Accent color scale
                'secondary': {
                    '50': 'var(--dm-secondary-50)',
                    '100': 'var(--dm-secondary-100)',
                    '200': 'var(--dm-secondary-200)',
                    '300': 'var(--dm-secondary-300)',
                    '400': 'var(--dm-secondary-400)',
                    '500': 'var(--dm-secondary-500)',
                    '600': 'var(--dm-secondary-600)',
                    '700': 'var(--dm-secondary-700)',
                    '800': 'var(--dm-secondary-800)',
                    '900': 'var(--dm-secondary-900)',
                    '950': 'var(--dm-secondary-950)',
                    // Semantic aliases for common usage
                    'DEFAULT': 'var(--dm-text-secondary)',
                },

                // === SEMANTIC TEXT COLORS ===
                'text-primary': 'var(--dm-text-primary)',
                'text-secondary': 'var(--dm-text-secondary)',
                'text-tertiary': 'var(--dm-text-tertiary)',
                'text-inverse': 'var(--dm-text-inverse)',
                'text-accent': 'var(--dm-text-accent)',

                // === SEMANTIC BACKGROUND COLORS ===
                'surface': 'var(--dm-bg-surface)',
                'background': 'var(--dm-bg-primary)',
                'muted': 'var(--dm-bg-secondary)',
                'subtle': 'var(--dm-bg-tertiary)',

                // === SEMANTIC BORDER COLORS ===
                'border': 'var(--dm-border-primary)',
                'border-secondary': 'var(--dm-border-secondary)',
                'border-focus': 'var(--dm-border-focus)',

                // === COMPONENT-SPECIFIC COLORS ===
                'header': 'var(--dm-header-bg)',
                'nav': 'var(--dm-nav-bg)',
                'chat': 'var(--dm-chat-bg)',
                'dialog': 'var(--dm-dialog-bg)',

                // === CHAT BUBBLE COLORS ===
                'bubble-sent': 'var(--dm-chat-bubble-sent)',
                'bubble-received': 'var(--dm-chat-bubble-received)',
                'input-bg': 'var(--dm-chat-input-bg)',

                // Keep gray scale for compatibility
                'gray': {
                    '50': '#fafafa',
                    '100': '#f5f5f5',
                    '200': '#eeeeee',
                    '300': '#e0e0e0',
                    '400': '#bdbdbd',
                    '500': '#9e9e9e',
                    '600': '#757575',
                    '700': '#424242',
                    '800': '#2c2c2c',
                    '900': '#1a1a1a',
                },
            },
            dropShadow: {
                'top': [
                    '0 -5px 8px rgb(0 0 0 / 10%)',
                    '0 -8px 5px rgb(0 0 0 / 6%)'
                ],
                'right': [
                    '0 0px 40px rgba(0,0,0,0.4)',
                    '0 0px 36px rgb(0 0 0/38%)'
                ]
            },

            keyframes: {
                // Existing animations - refined
                fadeIn: {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' },
                },
                fadeIn2: {
                    '50%': { opacity: '0.5' },
                    '100%': { opacity: '1' },
                },

                // WhatsApp-inspired animations
                slideInFromRight: {
                    '0%': { transform: 'translateX(100%)', opacity: '0' },
                    '100%': { transform: 'translateX(0)', opacity: '1' },
                },
                slideInFromLeft: {
                    '0%': { transform: 'translateX(-100%)', opacity: '0' },
                    '100%': { transform: 'translateX(0)', opacity: '1' },
                },
                slideInFromBottom: {
                    '0%': { transform: 'translateY(100%)', opacity: '0' },
                    '100%': { transform: 'translateY(0)', opacity: '1' },
                },
                scaleIn: {
                    '0%': { transform: 'scale(0.95)', opacity: '0' },
                    '100%': { transform: 'scale(1)', opacity: '1' },
                },
                pulse: {
                    '0%, 100%': { opacity: '1' },
                    '50%': { opacity: '0.5' },
                },
                typing: {
                    '0%, 60%, 100%': { transform: 'translateY(0)' },
                    '30%': { transform: 'translateY(-10px)' },
                },
            },
            animation: {
                // Refined existing animations
                'fade-in': 'fadeIn 0.15s ease-in-out',
                'fade-in-slow': 'fadeIn 0.3s ease-in-out',
                'fade-in-delayed': 'fadeIn2 0.25s ease-in-out',

                // WhatsApp-inspired animations
                'slide-in-right': 'slideInFromRight 0.3s ease-out',
                'slide-in-left': 'slideInFromLeft 0.3s ease-out',
                'slide-in-bottom': 'slideInFromBottom 0.3s ease-out',
                'scale-in': 'scaleIn 0.2s ease-out',
                'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                'typing': 'typing 1.4s ease-in-out infinite',

                // Message-specific animations
                'message-in': 'slideInFromRight 0.2s ease-out',
                'message-out': 'slideInFromLeft 0.2s ease-out',
            },
            height: {
                '100vh-32rem': 'calc(100vh - 32rem)',
                '100vh-16rem': 'calc(100vh - 16rem)',
            },
            // WhatsApp & Nothing Phone Inspired Background Utilities
            backgroundImage: {
                // Nothing Phone gradients - refined for minimalism
                'gradient-nothing': 'linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%)',
                'gradient-nothing-subtle': 'linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)',
                'gradient-nothing-dark': 'linear-gradient(135deg, #0d0d0d 0%, #1a1a1a 100%)',

                // WhatsApp-inspired gradients
                'gradient-whatsapp': 'linear-gradient(135deg, #25d366 0%, #1da851 100%)',
                'gradient-whatsapp-subtle': 'linear-gradient(135deg, #f0f9f0 0%, #dcf2dc 100%)',
                'gradient-whatsapp-dark': 'linear-gradient(135deg, #128c3e 0%, #0d5d2a 100%)',

                // Refined accent gradients
                'gradient-red-accent': 'linear-gradient(135deg, #e53e3e 0%, #c53030 100%)',
                'gradient-red-subtle': 'linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%)',
                'gradient-blue-accent': 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)',
                'gradient-blue-subtle': 'linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%)',
            },

            // WhatsApp-inspired spacing for consistent layouts
            spacing: {
                '18': '4.5rem',
                '88': '22rem',
                '112': '28rem',
                '128': '32rem',
                'chat-input': '3.5rem',
                'tab-height': '4rem',
                'header-height': '3.5rem',
            },

        },
    },
    plugins: [
        require('@tailwindcss/container-queries'),
        require('@tailwindcss/forms'),
        require('tailwindcss'),
        require('autoprefixer'),
        function ({ addComponents, theme }) {
            const buttons = {
                '.btn': {
                    display: 'flex',
                    minHeight: '2.25rem', // h-9
                    alignItems: 'center',
                    gap: '0.5rem', // gap-2
                    borderRadius: theme('borderRadius.lg'),
                    borderWidth: '1px',
                    padding: '0 1rem', // px-4
                    textAlign: 'center',
                    fontSize: '0.875rem', // text-sm
                    fontWeight: '500', // font-medium
                    '&:focus': {
                        outline: 'none',
                        ringWidth: '4px',
                    },

                    // Button Sizes
                    '&.btn-xs': {
                        minHeight: '1.75rem', // h-7
                        gap: '0.25rem', // gap-1
                        borderRadius: theme('borderRadius.md'),
                        padding: '0 0.5rem', // px-2
                        fontSize: '0.75rem', // text-xs
                    },
                    '&.btn-sm': {
                        minHeight: '2rem', // h-8
                        gap: '0.5rem', // gap-2
                        padding: '0 0.75rem', // px-3
                        fontSize: '0.75rem', // text-xs
                    },
                    '&.btn-lg': {
                        minHeight: '2.5rem', // h-10
                        padding: '0 1.25rem', // px-5
                        fontSize: '0.875rem', // text-sm
                    },
                    '&.btn-xl': {
                        minHeight: '3rem', // h-12
                        padding: '0 1.5rem', // px-6
                        fontSize: '1rem', // text-base
                    },

                    // Disabled state
                    '&.disabled, &[disabled]': {
                        pointerEvents: 'none',
                        opacity: '0.5',
                    },

                    // Primary Button (Nothing Phone Black)
                    '&.btn-primary': {
                        borderColor: theme('colors.primary.800'),
                        backgroundColor: theme('colors.primary.800'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.primary.900'),
                        },
                        '&:focus': {
                            ringColor: theme('colors.primary.300'),
                        },
                        '&.btn-outline': {
                            backgroundColor: 'transparent',
                            color: theme('colors.primary.800'),
                            '&:hover': {
                                color: theme('colors.white'),
                                backgroundColor: theme('colors.primary.800'),
                            },
                        },
                        '&.btn-light': {
                            borderColor: theme('colors.primary.100'),
                            backgroundColor: theme('colors.primary.100'),
                            color: theme('colors.primary.700'),
                            '&:hover': {
                                borderColor: theme('colors.primary.200'),
                                backgroundColor: theme('colors.primary.200'),
                            },
                            '&:focus': {
                                ringColor: theme('colors.primary.300'),
                            },
                        },
                    },

                    // Dark Button (Nothing Phone Black)
                    '&.btn-dark': {
                        borderColor: theme('colors.primary.700'),
                        backgroundColor: theme('colors.primary.700'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.primary.800'),
                        },
                        '&:focus': {
                            ringColor: theme('colors.primary.300'),
                        },
                        '&.btn-outline': {
                            backgroundColor: 'transparent',
                            color: theme('colors.primary.700'),
                            '&:hover': {
                                color: theme('colors.white'),
                                backgroundColor: theme('colors.primary.700'),
                            },
                        },
                    },

                    // WhatsApp Blue Button for Links
                    '&.btn-blue': {
                        borderColor: theme('colors.primary.600'),
                        backgroundColor: theme('colors.primary.600'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.primary.700'),
                        },
                        '&:focus': {
                            ringColor: theme('colors.primary.300'),
                        },
                        '&.btn-outline': {
                            backgroundColor: 'transparent',
                            color: theme('colors.primary.600'),
                            '&:hover': {
                                color: theme('colors.white'),
                                backgroundColor: theme('colors.primary.600'),
                            },
                        },
                    },

                    // Critical Button (Nothing Phone Red)
                    '&.btn-critical': {
                        borderColor: theme('colors.secondary.500'),
                        backgroundColor: theme('colors.secondary.500'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.secondary.600'),
                        },
                        '&:focus': {
                            ringColor: theme('colors.secondary.300'),
                        },
                        '&.btn-outline': {
                            backgroundColor: 'transparent',
                            color: theme('colors.secondary.500'),
                            '&:hover': {
                                color: theme('colors.white'),
                                backgroundColor: theme('colors.secondary.500'),
                            },
                        },
                    },

                    '&.btn-success': {
                        borderColor: theme('colors.green.700'),
                        backgroundColor: theme('colors.green.700'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.green.700'),
                        },
                        '&:focus': {
                            ringColor: theme('colors.green.300'),
                        },
                        '&.btn-outline': {
                            backgroundColor: 'transparent',
                            color: theme('colors.green.700'),
                            '&:hover': {
                                color: theme('colors.white'),
                                backgroundColor: theme('colors.green.700'),
                            },
                        },
                        '&.btn-light': {
                            borderColor: theme('colors.green.100'),
                            backgroundColor: theme('colors.green.100'),
                            color: theme('colors.green.600'),
                            '&:hover': {
                                borderColor: theme('colors.green.200'),
                                backgroundColor: theme('colors.green.200'),
                            },
                            '&:focus': {
                                ringColor: theme('colors.green.300'),
                            },
                        },
                    },

                    '&.btn-danger': {
                        borderColor: theme('colors.secondary.500'),
                        backgroundColor: theme('colors.secondary.500'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.secondary.600'),
                        },
                        '&:focus': {
                            ringColor: theme('colors.secondary.300'),
                        },
                        '&.btn-outline': {
                            backgroundColor: 'transparent',
                            color: theme('colors.secondary.500'),
                            '&:hover': {
                                color: theme('colors.white'),
                                backgroundColor: theme('colors.secondary.500'),
                            },
                        },
                        '&.btn-light': {
                            borderColor: theme('colors.secondary.100'),
                            backgroundColor: theme('colors.secondary.100'),
                            color: theme('colors.secondary.600'),
                            '&:hover': {
                                borderColor: theme('colors.secondary.200'),
                                backgroundColor: theme('colors.secondary.200'),
                            },
                            '&:focus': {
                                ringColor: theme('colors.secondary.300'),
                            },
                        },
                    },

                    '&.btn-warning': {
                        borderColor: theme('colors.yellow.500'),
                        backgroundColor: theme('colors.yellow.500'),
                        color: theme('colors.white'),
                        '&:hover': {
                            backgroundColor: theme('colors.yellow.600'),
                        },
                        '&:focus': {
                            ringColor: theme('colors.yellow.300'),
                        },
                        '&.btn-outline': {
                            backgroundColor: 'transparent',
                            color: theme('colors.yellow.500'),
                            '&:hover': {
                                color: theme('colors.white'),
                            },
                        },
                        '&.btn-light': {
                            borderColor: theme('colors.yellow.100'),
                            backgroundColor: theme('colors.yellow.100'),
                            color: theme('colors.yellow.500'),
                            '&:hover': {
                                borderColor: theme('colors.yellow.200'),
                                backgroundColor: theme('colors.yellow.200'),
                            },
                            '&:focus': {
                                ringColor: theme('colors.yellow.300'),
                            },
                        },
                    },

                    '&.btn-secondary': {
                        borderColor: theme('colors.gray.100'),
                        backgroundColor: theme('colors.gray.100'),
                        color: theme('colors.gray.600'),
                        '&:hover': {
                            borderColor: theme('colors.gray.200'),
                            backgroundColor: theme('colors.gray.200'),
                        },
                        '&:focus': {
                            ringColor: theme('colors.gray.300'),
                        },
                        '&.btn-outline': {
                            backgroundColor: 'transparent',
                            borderColor: theme('colors.gray.100'),
                            color: theme('colors.gray.600'),
                            '&:hover': {
                                borderColor: theme('colors.gray.300'),
                                color: theme('colors.gray.600'),
                            },
                        },
                        '&.btn-light': {
                            borderColor: theme('colors.gray.100'),
                            backgroundColor: theme('colors.gray.100'),
                            color: theme('colors.gray.600'),
                            '&:hover': {
                                borderColor: theme('colors.gray.200'),
                                backgroundColor: theme('colors.gray.200'),
                            },
                            '&:focus': {
                                ringColor: theme('colors.gray.300'),
                            },
                        },
                    },

                    '&.btn-light': {
                        borderColor: theme('colors.white'),
                        backgroundColor: theme('colors.white'),
                        color: theme('colors.gray.600'),
                        '&:hover': {
                            borderColor: theme('colors.gray.100'),
                            backgroundColor: theme('colors.gray.100'),
                        },
                        '&:focus': {
                            ringColor: theme('colors.gray.300'),
                        },
                    },

                    '&.btn-outline': {
                        backgroundColor: 'transparent',
                    },

                    '&.btn-link': {
                        minHeight: '0',
                        padding: '0',
                        border: 0,
                        color: theme('colors.primary.600'),
                        '&:hover': {
                            textDecoration: 'underline',
                        },
                    },

                    // Button Icon
                    '&.btn-icon': {
                        width: '2.25rem', // w-9
                        flexShrink: '0',
                        justifyContent: 'center',
                        gap: '0',
                        padding: '0',

                        '&.btn-xs': {
                            width: '1.75rem', // w-7
                        },
                        '&.btn-sm': {
                            width: '2rem', // w-8
                        },
                        '&.btn-lg': {
                            width: '2.5rem', // w-10
                        },
                    },

                    // Icon sizes
                    '& i': {
                        fontSize: '1.25rem', // text-xl
                        lineHeight: '1',
                    },

                    '&.btn-icon i': {
                        fontSize: '1.25rem', // text-xl
                    },

                    '&.btn-xs i': {
                        fontSize: '0.875rem', // text-sm
                    },

                    '&.btn-xs.btn-icon i': {
                        fontSize: '1rem', // text-base
                    },

                    '&.btn-sm i': {
                        fontSize: '0.875rem', // text-sm
                    },

                    '&.btn-sm.btn-icon i': {
                        fontSize: '1.125rem', // text-lg
                    },

                    '&.btn-lg i': {
                        fontSize: '1.25rem', // text-xl
                    },

                    '&.btn-lg.btn-icon i': {
                        fontSize: '1.5rem', // text-2xl
                    },

                    '&.btn-icon-xs i': {
                        fontSize: '0.75rem', // text-xs
                    },

                    '&.btn-icon-sm i': {
                        fontSize: '0.875rem', // text-sm
                    },

                    '&.btn-icon-lg i': {
                        fontSize: '1.125rem', // text-lg
                    },

                    '&.btn-icon-xl i': {
                        fontSize: '1.25rem', // text-xl
                    },

                    '&.btn-icon-sm i': {
                        fontSize: '0.875rem', // text-sm
                    },

                    '&.btn-icon-md i': {
                        fontSize: '0.9375rem', // text-[0.9375rem]
                    },

                    '&.btn-icon-lg i': {
                        fontSize: '1.125rem', // text-lg
                    },

                    '&.btn-icon-xl i': {
                        fontSize: '1.25rem', // text-xl
                    },

                    '&.btn-icon-2xl i': {
                        fontSize: '1.5rem', // text-2xl
                    },
                },
            };

            addComponents(buttons);
        },
    ],
}
