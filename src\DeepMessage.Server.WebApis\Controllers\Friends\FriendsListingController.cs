﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Authorization;
namespace DeepMessage.Server.WebApis.Controller.Friends;
 
[ApiController, Authorize, Route("api/[controller]/[action]")]
public class FriendsListingController : ControllerBase, IFriendsListingDataService
{

	private readonly IFriendsListingDataService dataService;

	public FriendsListingController(IFriendsListingDataService dataService)
	{
		this.dataService = dataService;
	}

	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<PagedDataList<FriendsListingBusinessObject>> GetPaginatedItems([FromQuery] FriendsFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
