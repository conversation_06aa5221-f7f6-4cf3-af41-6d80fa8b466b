using DeepMessage.Framework.Core;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using System.Text.Json;

namespace DeepMessage.Server.DataServices.Features.Friends;

public class FriendProfileServerSideFormDataService : IFriendProfileFormDataService
{
    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor _contextAccessor;
    private readonly ILogger<FriendProfileServerSideFormDataService> _logger;

    public FriendProfileServerSideFormDataService(
        AppDbContext context,
        IHttpContextAccessor contextAccessor,
        ILogger<FriendProfileServerSideFormDataService> logger)
    {
        _context = context;
        _contextAccessor = contextAccessor;
        _logger = logger;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(FriendProfileFormBusinessObject formBusinessObject)
    {
        ArgumentNullException.ThrowIfNull(formBusinessObject.Id, "Friend ID is required");

        // Find the friendship relationship
        var friendship = await _context.Friendships
            .FirstOrDefaultAsync(f => f.Id == formBusinessObject.Id);

        if (friendship == null)
        {
            throw new InvalidOperationException("Friendship not found");
        }
        friendship.Name = formBusinessObject.FriendName;
        friendship.TagLine = formBusinessObject.Tagline;
        friendship.AvatarData = formBusinessObject.AvatarData;
        await _context.SaveChangesAsync();
        return friendship.Id;

    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<FriendProfileFormBusinessObject> GetItemByIdAsync(string id)
    {

        // Find the friendship relationship
        var friendship = await _context.Friendships
            .Select(x => new FriendProfileFormBusinessObject
            {
                Id = x.Id,
                FriendName = x.Name,
                Tagline = x.TagLine,
                AvatarData = x.AvatarData
            })

            .FirstOrDefaultAsync(f => f.Id == id);
        ArgumentNullException.ThrowIfNull(friendship, "Friendship not found");
        return friendship;
    }
}
