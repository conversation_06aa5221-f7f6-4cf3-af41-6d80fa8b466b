﻿using DeepMessage.Framework.Core;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
namespace DeepMessage.ServiceContracts.Features.Account;
public class SignupFormBusinessObject
{
    [Required]
    public string NickName { get; set; } = null!;

    [Required]
    public string PassKey { get; set; } = null!;

    [Required]
    public string ReferralCode { get; set; } = null!;

    public string? DisplayName { get; set; }

    public string? AvatarData { get; set; }

    public string? Pub1 { get; set; }

    public string? Pub2 { get; set; } 

    public string? DeviceString { get; set; }
}


public class AuthorizationClaimsModel
{
    public AuthorizationClaimsModel(string token, string refreshToken, string userId, 
        string? username, string pub1, string pub2, string? displayName ,string? avatarData, string? avatarDescription)
    {
        Token = token ?? throw new ArgumentNullException(nameof(token));
        RefreshToken = refreshToken ?? throw new ArgumentNullException(nameof(refreshToken));
        UserId = userId ?? throw new ArgumentNullException(nameof(userId));
        Username = username ?? throw new ArgumentNullException(nameof(username)); 
        Pub1 = pub1 ?? throw new ArgumentNullException(nameof(pub1));
        Pub2 = pub2 ?? throw new ArgumentNullException(nameof(pub2));
        DisplayName = displayName;
        AvatarData = avatarData;
        AvatarDescription = avatarDescription;
    }

    public string Token { get; set; }
    public string RefreshToken { get; set; }
    public string UserId { get; set; }
    public string Username { get; set; }

    public string? DisplayName { get; set; }
    public string? AvatarData { get; set; }

    [StringLength(450)]
    public string? AvatarDescription { get; set; }


    public string Pub1 { get; set; } = null!;

    public string Pub2 { get; set; } = null!;



}