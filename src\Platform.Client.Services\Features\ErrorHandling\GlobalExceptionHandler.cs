using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using System.Security;
using System.Text.Json;

namespace Platform.Client.Services.Features.ErrorHandling;

/// <summary>
/// Global exception handler service for centralized error logging and reporting
/// </summary>
public interface IGlobalExceptionHandler
{
    /// <summary>
    /// Handles and logs exceptions with context information
    /// </summary>
    Task HandleExceptionAsync(Exception exception, string? componentName = null, Dictionary<string, object>? context = null);

    /// <summary>
    /// Reports critical errors that require immediate attention
    /// </summary>
    Task ReportCriticalErrorAsync(Exception exception, string componentName, string userAction);

    /// <summary>
    /// Logs user-initiated error recovery attempts
    /// </summary>
    Task LogRecoveryAttemptAsync(string componentName, string recoveryAction, bool successful);

    /// <summary>
    /// Gets error statistics for monitoring
    /// </summary>
    Task<ErrorStatistics> GetErrorStatisticsAsync();
}

public class GlobalExceptionHandler : IGlobalExceptionHandler
{
    private readonly ILogger<GlobalExceptionHandler> _logger;
    private readonly IJSRuntime _jsRuntime;
    private readonly List<ErrorRecord> _errorHistory = new();
    private readonly object _lockObject = new();

    public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger, IJSRuntime jsRuntime)
    {
        _logger = logger;
        _jsRuntime = jsRuntime;
    }

    public async Task HandleExceptionAsync(Exception exception, string? componentName = null, Dictionary<string, object>? context = null)
    {
        var errorId = Guid.NewGuid().ToString("N")[..8];
        var timestamp = DateTime.UtcNow;

        try
        {
            // Create error record
            var errorRecord = new ErrorRecord
            {
                ErrorId = errorId,
                Timestamp = timestamp,
                ComponentName = componentName ?? "Unknown",
                ExceptionType = exception.GetType().Name,
                Message = exception.Message,
                StackTrace = exception.StackTrace,
                Context = context
            };

            // Add to history (keep last 100 errors)
            lock (_lockObject)
            {
                _errorHistory.Add(errorRecord);
                if (_errorHistory.Count > 100)
                {
                    _errorHistory.RemoveAt(0);
                }
            }

            // Log based on exception type and severity
            await LogExceptionAsync(exception, errorRecord);

            // Send client-side error tracking if available
            await TrackClientSideErrorAsync(errorRecord);

        }
        catch (Exception loggingEx)
        {
            // Fallback logging if error handling itself fails
            _logger.LogCritical(loggingEx, "Failed to handle exception in GlobalExceptionHandler. Original exception: {OriginalException}", exception.Message);
        }
    }

    public async Task ReportCriticalErrorAsync(Exception exception, string componentName, string userAction)
    {
        var context = new Dictionary<string, object>
        {
            ["UserAction"] = userAction,
            ["Severity"] = "Critical",
            ["RequiresAttention"] = true
        };

        await HandleExceptionAsync(exception, componentName, context);

        // Additional critical error handling
        _logger.LogCritical(exception,
            "CRITICAL ERROR in {ComponentName} during {UserAction}. Immediate attention required.",
            componentName, userAction);
    }

    public async Task LogRecoveryAttemptAsync(string componentName, string recoveryAction, bool successful)
    {
        try
        {
            _logger.LogInformation(
                "Recovery attempt in {ComponentName}: {RecoveryAction} - {Result}",
                componentName, recoveryAction, successful ? "SUCCESS" : "FAILED");

            // Track recovery patterns for improvement
            var recoveryRecord = new
            {
                ComponentName = componentName,
                RecoveryAction = recoveryAction,
                Successful = successful,
                Timestamp = DateTime.UtcNow
            };

            await TrackRecoveryAttemptAsync(recoveryRecord);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log recovery attempt for {ComponentName}", componentName);
        }
    }

    public async Task<ErrorStatistics> GetErrorStatisticsAsync()
    {
        try
        {
            await Task.Yield(); // Ensure async context
            lock (_lockObject)
            {
                var now = DateTime.UtcNow;
                var last24Hours = _errorHistory.Where(e => e.Timestamp > now.AddHours(-24)).ToList();
                var lastHour = _errorHistory.Where(e => e.Timestamp > now.AddHours(-1)).ToList();

                return new ErrorStatistics
                {
                    TotalErrors = _errorHistory.Count,
                    ErrorsLast24Hours = last24Hours.Count,
                    ErrorsLastHour = lastHour.Count,
                    MostCommonErrors = _errorHistory
                        .GroupBy(e => e.ExceptionType)
                        .OrderByDescending(g => g.Count())
                        .Take(5)
                        .ToDictionary(g => g.Key, g => g.Count()),
                    MostProblematicComponents = _errorHistory
                        .GroupBy(e => e.ComponentName)
                        .OrderByDescending(g => g.Count())
                        .Take(5)
                        .ToDictionary(g => g.Key, g => g.Count())
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate error statistics");
            return new ErrorStatistics();
        }
    }

    private async Task LogExceptionAsync(Exception exception, ErrorRecord errorRecord)
    {
        // Determine log level based on exception type
        var logLevel = GetLogLevel(exception);

        switch (logLevel)
        {
            case LogLevel.Critical:
                _logger.LogCritical(exception,
                    "Critical error in {ComponentName}. Error ID: {ErrorId}",
                    errorRecord.ComponentName, errorRecord.ErrorId);
                break;

            case LogLevel.Error:
                _logger.LogError(exception,
                    "Error in {ComponentName}. Error ID: {ErrorId}. Type: {ExceptionType}",
                    errorRecord.ComponentName, errorRecord.ErrorId, errorRecord.ExceptionType);
                break;

            case LogLevel.Warning:
                _logger.LogWarning(exception,
                    "Warning in {ComponentName}. Error ID: {ErrorId}. Type: {ExceptionType}",
                    errorRecord.ComponentName, errorRecord.ErrorId, errorRecord.ExceptionType);
                break;

            default:
                _logger.LogInformation(exception,
                    "Exception in {ComponentName}. Error ID: {ErrorId}. Type: {ExceptionType}",
                    errorRecord.ComponentName, errorRecord.ErrorId, errorRecord.ExceptionType);
                break;
        }

        await Task.CompletedTask;
    }

    private LogLevel GetLogLevel(Exception exception)
    {
        return exception switch
        {
            OutOfMemoryException => LogLevel.Critical,
            StackOverflowException => LogLevel.Critical,
            AccessViolationException => LogLevel.Critical,
            UnauthorizedAccessException => LogLevel.Error,
            SecurityException => LogLevel.Error,
            HttpRequestException => LogLevel.Warning,
            TaskCanceledException => LogLevel.Information,
            OperationCanceledException => LogLevel.Information,
            JSException => LogLevel.Warning,
            _ => LogLevel.Error
        };
    }

    private async Task TrackClientSideErrorAsync(ErrorRecord errorRecord)
    {
        try
        {
            // Send error data to client-side tracking (if available)
            var errorData = new
            {
                errorRecord.ErrorId,
                errorRecord.ComponentName,
                errorRecord.ExceptionType,
                errorRecord.Message,
                Timestamp = errorRecord.Timestamp.ToString("O"),
                UserAgent = await GetUserAgentAsync(),
                Url = await GetCurrentUrlAsync()
            };

            await _jsRuntime.InvokeVoidAsync("console.error",
                $"[DeepMessage Error] {errorRecord.ComponentName}: {errorRecord.Message} (ID: {errorRecord.ErrorId})");

        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to track client-side error for {ErrorId}", errorRecord.ErrorId);
        }
    }

    private async Task TrackRecoveryAttemptAsync(object recoveryRecord)
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("console.info",
                $"[DeepMessage Recovery] {JsonSerializer.Serialize(recoveryRecord)}");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to track recovery attempt");
        }
    }

    private async Task<string> GetUserAgentAsync()
    {
        try
        {
            return await _jsRuntime.InvokeAsync<string>("eval", "navigator.userAgent");
        }
        catch
        {
            return "Unknown";
        }
    }

    private async Task<string> GetCurrentUrlAsync()
    {
        try
        {
            return await _jsRuntime.InvokeAsync<string>("eval", "window.location.href");
        }
        catch
        {
            return "Unknown";
        }
    }
}

/// <summary>
/// Error record for tracking and analysis
/// </summary>
public class ErrorRecord
{
    public string ErrorId { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string ComponentName { get; set; } = string.Empty;
    public string ExceptionType { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? StackTrace { get; set; }
    public Dictionary<string, object>? Context { get; set; }
}

/// <summary>
/// Error statistics for monitoring and analysis
/// </summary>
public class ErrorStatistics
{
    public int TotalErrors { get; set; }
    public int ErrorsLast24Hours { get; set; }
    public int ErrorsLastHour { get; set; }
    public Dictionary<string, int> MostCommonErrors { get; set; } = new();
    public Dictionary<string, int> MostProblematicComponents { get; set; } = new();
}
