@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Account
@using ModelFury.Briefly.MobileApp.Features.Account
@using Platform.Framework.Core
@page "/captcha"
@page "/verify"
@inherits FormBase<SignInFormBusinessObject, SignInFormViewModel, string, ISignInFormDataService>

<!-- Main Container with Fixed Dimensions - Enhanced Mobile Design -->
<div class="p-4 bg-white dark:bg-gray-900 rounded-lg shadow-lg">
    <!-- Fixed Height Container to Prevent Layout Shifts -->
    <div class="min-h-96 flex flex-col">

        @if (IsWorking)
        {
            <!-- Loading State with Enhanced Animation -->
            <div class="flex-1 flex flex-col justify-center items-center space-y-4 w-full">
                <div class="animate-pulse">
                    <!-- Enhanced Loading Animation -->
                    <div class="flex items-center justify-center mb-4">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    </div>
                    <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48 mb-4"></div>
                    <div class="h-20 bg-gray-200 dark:bg-gray-700 rounded w-full mb-4"></div>
                    <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full mb-4"></div>
                    <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400">Verifying...</p>
            </div>
        }
        else
        {
            <!-- Header Section - Enhanced with Dynamic Content -->
            <div class="mb-6 flex flex-col justify-center">
                <div class="text-center space-y-2">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                        @(string.IsNullOrEmpty(NickName) ? "Activate Application" : "Welcome back")
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        @(string.IsNullOrEmpty(NickName) 
                            ? "Activate using Purchase or Referral code" 
                            : "Enter your verification code to continue")
                    </p>
                </div>

                @if (!string.IsNullOrEmpty(NickName))
                {
                    <!-- Registration Status with Reset Option -->
                    <div class="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-yellow-800 dark:text-yellow-200">
                                Activated for <span class="font-mono">@ObfuscateUsername(NickName)</span>
                            </span>
                            <button @onclick="ResetRegistration"
                                    class="text-xs text-yellow-600 dark:text-yellow-400 underline hover:text-yellow-700 dark:hover:text-yellow-300 transition-colors">
                                Reset
                            </button>
                        </div>
                    </div>
                }
            </div>

            <!-- Captcha Section - Enhanced Visual Design -->
            @if (!string.IsNullOrEmpty(NickName))
            {
                <div class="mb-6">
                    <div class="border border-gray-300 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                        <div class="flex justify-center mb-3">
                            <div class="w-48 h-16 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded border-2 border-dashed border-gray-300 dark:border-gray-500 flex items-center justify-center">
                                <span class="text-gray-500 dark:text-gray-400 text-sm font-mono">CAPTCHA</span>
                            </div>
                        </div>
                        <div class="flex justify-center space-x-4 text-xs">
                            <button type="button" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors flex items-center space-x-1">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span>Refresh</span>
                            </button>
                            <button type="button" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors flex items-center space-x-1">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 11.293l-4.95-4.95M11 7l4 4-4 4"></path>
                                </svg>
                                <span>Audio</span>
                            </button>
                        </div>
                    </div>
                </div>
            }

            <!-- Form Section - Enhanced with Better UX -->
            <div class="flex-1 flex flex-col">
                <EditForm Model="SelectedItem" OnValidSubmit="HandleFormSubmit" class="flex-1 flex flex-col space-y-4">
                    <DataAnnotationsValidator />

                    <!-- Error Display - Enhanced -->
                    @if (!string.IsNullOrEmpty(Error))
                    {
                        <div class="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                            <div class="flex items-start space-x-2">
                                <svg class="w-5 h-5 text-red-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-red-800 dark:text-red-200">Verification failed</h4>
                                    <p class="text-sm text-red-700 dark:text-red-300 mt-1">@Error</p>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Verification Field - Enhanced Design -->
                    <div class="space-y-2">
                        <label for="authCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            @(string.IsNullOrEmpty(NickName) ? "Activation Code" : "Verification Code")
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m5 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V9a2 2 0 012-2h1m2-4a2 2 0 012-2h4a2 2 0 012 2v2a2 2 0 01-2 2h-4a2 2 0 01-2-2V5z" />
                                </svg>
                            </div>
                            <InputText @bind-Value="SelectedItem!.PassKey"
                                       id="authCode"
                                       type="password"
                                       placeholder="@(string.IsNullOrEmpty(NickName) ? "Enter activation code" : "Enter verification code")"
                                       class="block w-full pl-10 pr-3 py-3 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors" />
                        </div>
                        <div class="min-h-5">
                            <ValidationMessage For="@(() => SelectedItem!.PassKey)" class="text-xs text-red-600 dark:text-red-400" />
                        </div>
                    </div>

                    <!-- Submit Button - Enhanced with Better States -->
                    <button type="submit"
                            disabled="@(IsWorking)"
                            class="w-full py-3 px-4 text-sm font-medium rounded-lg bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                        @if (IsWorking)
                        {
                            <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Verifying...</span>
                        }
                        else
                        {
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                            <span>@(string.IsNullOrEmpty(NickName) ? "Activate" : "Verify")</span>
                        }
                    </button>

                    <!-- Back to Search Link -->
                    <div class="text-center">
                        <button type="button" @onclick="BackToSearch" 
                                class="text-sm text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                            Want to go back? Return to search
                        </button>
                    </div>
                </EditForm>
            </div>
        }
    </div>
</div>
