﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Text.Json;
using Microsoft.AspNetCore.Identity;
using System.Security;
using Microsoft.Extensions.Logging;
namespace DeepMessage.Server.DataServices.Features.Friends;
public class FriendServerSideFormDataService : IFriendFormDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<FriendServerSideFormDataService> logger;

    public FriendServerSideFormDataService(AppDbContext context, IHttpContextAccessor contextAccessor,
        UserManager<ApplicationUser> userManager, ILogger<FriendServerSideFormDataService> logger)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
        _userManager = userManager;
        this.logger = logger;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(FriendFormBusinessObject formBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        ArgumentNullException.ThrowIfNull(userId, "User not found");
        var trx = await _context.Database.BeginTransactionAsync();
        try
        {
            var authCode = await _context.AuthCodes.FirstOrDefaultAsync(x => x.Code == formBusinessObject.AuthCode &&
                     x.AuthCodeStatus == ServiceContracts.Enums.AuthCodeStatus.Unused);

            if (authCode == null)
            {
                throw new InvalidOperationException("Invalid activation Code");
            }
            var friend = _context.Friendships.FirstOrDefault(x => x.UserId == userId && x.FriendId == authCode.CreatedBy);
            if (friend != null)
            {
                throw new InvalidOperationException("Friend already exists, sync your friend list");
            }
            // Retrieve friend's public key from ApplicationUser
            var friendUser = await _userManager.FindByIdAsync(authCode.CreatedBy);
            if (friendUser == null)
            {
                throw new InvalidOperationException("Friend user not found");
            }

            // Security validation: Ensure we only share public keys, never private keys
            if (string.IsNullOrEmpty(friendUser.Pub1))
            {
                throw new InvalidOperationException("Friend's public key is not available");
            }
             
            var friendshipId = CombineGuidsXor(userId, authCode.CreatedBy);
            var friendUserData = await _userManager.FindByIdAsync(authCode.CreatedBy);

            var friendship = new Friendship
            {
                UserId = userId,
                FriendId = authCode.CreatedBy,
                Id = friendshipId,
                CreatedAt = DateTime.UtcNow,
                Name = friendUserData.DisplayName ?? "No Name",
                AvatarData = friendUserData.AvatarData
            };

            _context.Friendships.Add(friendship);
            authCode.AuthCodeStatus = ServiceContracts.Enums.AuthCodeStatus.Consumed;
            authCode.ConsumedAt = DateTime.UtcNow;
            authCode.ConsumedByIp = contextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString();
            await _context.SaveChangesAsync();

            var reciprocalFriendshipId = CombineGuidsXor(authCode.CreatedBy, userId);

            var reciprocalFriendship = await _context.Friendships
                .FirstOrDefaultAsync(x => (x.UserId == authCode.CreatedBy && x.FriendId == userId));
            if (reciprocalFriendship == null)
            {
                var userDisplayName = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.GivenName)?.Value;
                var avatarData = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.UserData)?.Value;
                reciprocalFriendship = new Friendship()
                {
                    UserId = authCode.CreatedBy,
                    FriendId = userId,
                    Id = reciprocalFriendshipId,
                    CreatedAt = DateTime.UtcNow,
                    Name = userDisplayName ?? "No Name",
                    AvatarData = avatarData
                };
                _context.Friendships.Add(reciprocalFriendship);
            }
            await _context.SaveChangesAsync();
            await trx.CommitAsync();

            // Create structured response with friend's public key
            var response = new FriendFormResponseDto
            {
                Id = friendship.Id,
                FriendId = friendship.FriendId,
                Username = friendUser.UserName ?? string.Empty,
                Name = friendship.Name,
                Pub1 = friendUser.Pub1, // Friend's RSA public key for encryption
                                        //DisplayPictureUrl = friendship.DisplayPictureUrl,
                CreatedAt = friendship.CreatedAt
            };

            return JsonSerializer.Serialize(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error saving friend form data");
            await trx.RollbackAsync();
            throw;
        }
        finally
        {
            await trx.DisposeAsync();
        }
    }

    public string CombineGuidsXor(string guid1, string guid2)
    {
        string combined = string.Empty;
        for (int i = 0; i < guid1.Length; i++)
        {
            combined += (i % 2 == 0) ? guid1[i] : guid2[i];
        }

        return combined;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public Task<FriendFormBusinessObject> GetItemByIdAsync(string id)
    {
        throw new NotImplementedException();
    }
}
