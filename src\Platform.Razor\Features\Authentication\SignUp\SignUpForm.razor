@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Account
@using ModelFury.Briefly.MobileApp.Features.Account
@using Platform.Framework.Core
@page "/register"
@inherits FormBase<SignupFormBusinessObject, SignupFormViewModel, string, ISignupFormDataService>

<!-- Main Container with Nothing Phone Aesthetic -->
<div class="min-h-screen bg-background flex items-center justify-center touch-spacing">
    <div class="mobile-container w-full space-y-6 sm:space-y-8">
        <!-- Header with Nothing Phone Black Icon -->
        <div class="text-center">
            <div class="auth-header-icon">
                <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                </svg>
            </div>
            <h2 class="text-responsive-xl font-bold text-primary">Create your account</h2>
            <p class="mt-2 text-responsive-sm text-secondary">
                Join us to get started with secure messaging
            </p>
        </div>

        <!-- Sign Up Form -->
        <div class="auth-form-container">
            <EditForm Model="SelectedItem" OnValidSubmit="HandleFormSubmit">
                <DataAnnotationsValidator />
                <ValidationSummary />

                 
                <label for="avatarSelect" class="block text-sm font-medium text-gray-700 mb-2">
                    Select Avatar
                </label>
                <div class="relative">
                    <select @bind="SelectedItem.AvatarData" id="avatarSelect"
                            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="">Choose an avatar...</option>
                        @for (int i = 1; i <= 12; i++)
                        {
                            <option value="/avatars/@(i).png">Avatar @i</option>
                        }
                    </select>
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                </div>

                <!-- Display Name Field -->
                <div class="form-group">
                    <label for="nickname-input" class="form-label">
                        Display Name
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" aria-hidden="true">
                            <svg class="h-5 w-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <InputText @bind-Value="SelectedItem!.DisplayName"
                                   id="nickname-input"
                                   placeholder="Your friends will see this name"
                                   class="form-control pl-10"
                                   autocomplete="none" />
                    </div>
                    <ValidationMessage For="@(() => SelectedItem!.DisplayName)" class="form-error" id="nickname-error" />
                </div>



                <!-- Nick name Field -->
                <div class="form-group">
                    <label for="nickname-input" class="form-label">
                        Username
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" aria-hidden="true">
                            <svg class="h-5 w-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <InputText @bind-Value="SelectedItem!.NickName"
                                   id="nickname-input"
                                   placeholder="Choose a Username"
                                   class="form-control pl-10"
                                   autocomplete="none" />
                    </div>
                    <ValidationMessage For="@(() => SelectedItem!.NickName)" class="form-error" id="nickname-error" />
                </div>

                <!-- Password Field -->
                <div class="form-group mt-4">
                    <label for="password-input" class="form-label">
                        Password
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" aria-hidden="true">
                            <svg class="h-5 w-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <InputText @bind-Value="SelectedItem!.PassKey"
                                   id="password-input"
                                   type="@(SelectedItem!.ShowPassword ? "text" : "password")"
                                   placeholder="Create a password"
                                   class="form-control pl-10 pr-12"
                                   aria-describedby="password-error password-toggle"
                                   autocomplete="new-password"
                                   required />
                        <button type="button" @onclick="TogglePasswordVisibility"
                                id="password-toggle"
                                class="password-toggle"
                                aria-label="@(SelectedItem!.ShowPassword ? "Hide password" : "Show password")"
                                aria-pressed="@SelectedItem!.ShowPassword.ToString().ToLower()">
                            @if (SelectedItem!.ShowPassword)
                            {
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                </svg>
                            }
                            else
                            {
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            }
                        </button>
                    </div>
                    <ValidationMessage For="@(() => SelectedItem!.PassKey)" class="form-error" id="password-error" />
                </div>
                 
                <!-- Sign Up Button -->
                <div>
                    <button type="submit"
                            disabled="@(IsWorking)"
                            class="auth-form-button rounded-full mt-4">
                        @if (IsWorking)
                        {
                            <div class="auth-loading-spinner"></div>
                            <span>Creating account...</span>
                        }
                        else
                        {
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <svg class="h-5 w-5 text-white/80 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                                </svg>
                            </span>
                            <span>Create Account</span>
                        }
                    </button>
                </div>

                <!-- Error Display -->
                @if (!string.IsNullOrEmpty(Error))
                {
                    <div class="auth-error-container">
                        <div class="flex">
                            <svg class="h-5 w-5 text-secondary-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h3 class="text-sm font-medium text-secondary-600">Registration failed</h3>
                                <p class="mt-1 text-sm text-secondary">@Error</p>
                            </div>
                        </div>
                    </div>
                }
            </EditForm>

            <!-- Sign In Link -->
            <div class="text-center auth-form-divider">
                <p class="text-responsive-sm text-secondary">
                    Already have an account?
                    <a href="/login" class="auth-form-link">
                        Sign in here
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
