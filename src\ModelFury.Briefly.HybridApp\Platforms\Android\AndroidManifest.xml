﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <application android:allowBackup="true" android:icon="@mipmap/appicon" 
				 android:roundIcon="@mipmap/appicon_round" android:supportsRtl="true">

	<service
	   android:name="com.google.firebase.iid.FirebaseInstanceIdInternalReceiver"
	   android:exported="false" />
	<receiver
	  android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
	  android:exported="true"
	  android:permission="com.google.android.c2dm.permission.SEND">
		<intent-filter>
			<action android:name="com.google.android.c2dm.intent.RECEIVE" />
			<action android:name="com.google.android.c2dm.intent.REGISTRATION" />
			<category android:name="${applicationId}" />
		</intent-filter>
	</receiver>
	</application>


	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.CAMERA" />
	<uses-permission android:name="android.permission.FOREGROUND_SERVICE"  android:minSdkVersion="34"/>
	<uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC"  android:minSdkVersion="34"/>
	<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
	<uses-permission android:name="android.permission.VIBRATE" />

	<!-- Media and Storage Permissions for Picture Messages -->
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
	<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" android:minSdkVersion="33" />
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="28" />

	<!-- Camera feature declaration -->
	<uses-feature android:name="android.hardware.camera" android:required="false" />
	<uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />
</manifest>