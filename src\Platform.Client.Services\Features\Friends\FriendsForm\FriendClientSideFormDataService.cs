﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Friends;
namespace Platform.Client.Services.Features.Friends;
public class FriendClientSideFormDataService : IFriendFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public FriendClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(FriendFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/FriendsForm/Save", formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<FriendFormBusinessObject> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<FriendFormBusinessObject>($"api/FriendsForm/GetItemById?id=" + id);
	}
}

 
