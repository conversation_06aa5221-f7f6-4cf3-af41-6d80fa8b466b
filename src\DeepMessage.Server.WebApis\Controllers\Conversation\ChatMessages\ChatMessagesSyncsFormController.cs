﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
namespace DeepMessage.Server.WebApis.Controller.Conversation;
[ApiController, Route("api/[controller]/[action]")]
public class ChatMessagesSyncsFormController : ControllerBase, IChatMessagesSyncFormDataService
{

	private readonly IChatMessagesSyncFormDataService dataService;

	public ChatMessagesSyncsFormController(IChatMessagesSyncFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] ChatMessagesSyncFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<ChatMessagesSyncFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
