using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Core;
using SkiaSharp;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore;

namespace DeepMessage.Server.WebApis.Controllers;

[ApiController]
[Route("api/[controller]/[action]")]
[Authorize]
public class MediaUploadController : ControllerBase
{
    private readonly AppDbContext _context;
    private readonly ILogger<MediaUploadController> _logger;
    private readonly IWebHostEnvironment _environment;
    
    // File size limits
    private const long MaxImageSizeBytes = 10_000_000; // 10MB
    private const int ThumbnailMaxWidth = 300;
    private const int ThumbnailMaxHeight = 300;
    
    // Allowed image MIME types
    private static readonly string[] AllowedImageTypes = {
        "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
    };

    public MediaUploadController(
        AppDbContext context, 
        ILogger<MediaUploadController> logger,
        IWebHostEnvironment environment)
    {
        _context = context;
        _logger = logger;
        _environment = environment;
    }

    [HttpPost]
    [RequestSizeLimit(MaxImageSizeBytes)]
    public async Task<IActionResult> UploadImage(
        [FromForm] IFormFile file, 
        [FromForm] string conversationId,
        [FromForm] string? caption = null)
    {
        try
        {
            // Validate input
            if (file == null || file.Length == 0)
                return BadRequest(new { error = "No file provided" });

            if (string.IsNullOrEmpty(conversationId))
                return BadRequest(new { error = "ConversationId is required" });

            // Validate file
            var validationResult = ValidateImageFile(file);
            if (!validationResult.IsValid)
                return BadRequest(new { error = validationResult.ErrorMessage });

            // Get current user
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            // Verify user has access to conversation
            var conversation = await _context.Conversations
                .Include(c => c.Participants)
                .FirstOrDefaultAsync(c => c.Id == conversationId);

            if (conversation == null)
                return NotFound(new { error = "Conversation not found" });

            if (!conversation.Participants.Any(p => p.UserId == userId))
                return Forbid("Access denied to conversation");

            // Read file data
            byte[] fileData;
            using (var memoryStream = new MemoryStream())
            {
                await file.CopyToAsync(memoryStream);
                fileData = memoryStream.ToArray();
            }

            // Generate thumbnail
            var thumbnailData = await GenerateThumbnailAsync(fileData);

            // Create unique file ID and storage path
            var fileId = Guid.NewGuid().ToString();
            var fileName = $"{fileId}_{SanitizeFileName(file.FileName)}";
            var filePath = await StoreFileAsync(fileName, fileData);

            // Create message attachment record
            var attachment = new MessageAttachment
            {
                Id = fileId,
                AttachmentType = "Image",
                FileName = file.FileName,
                FileUrl = filePath,
                FileSizeBytes = file.Length,
                MimeType = file.ContentType,
                // Note: MessageId will be set when the message is created
            };

            // Return response with file info and thumbnail
            var response = new MediaUploadResponse
            {
                FileId = fileId,
                FileName = file.FileName,
                FileSizeBytes = file.Length,
                MimeType = file.ContentType,
                ThumbnailBase64 = Convert.ToBase64String(thumbnailData),
                FileUrl = filePath
            };

            _logger.LogInformation("Image uploaded successfully: {FileId}, Size: {Size} bytes", 
                fileId, file.Length);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading image");
            return StatusCode(500, new { error = "Internal server error during upload" });
        }
    }

    [HttpGet("{messageId}/{attachmentId}")]
    public async Task<IActionResult> GetMedia(string messageId, string attachmentId)
    {
        try
        {
            // Get current user
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
                return Unauthorized();

            // Find the attachment and verify access
            var attachment = await _context.MessageAttachments
                .Include(a => a.Message)
                .ThenInclude(m => m.Conversation)
                .ThenInclude(c => c.Participants)
                .FirstOrDefaultAsync(a => a.Id == attachmentId && a.MessageId == messageId);

            if (attachment == null)
                return NotFound();

            // Verify user has access to the conversation
            if (!attachment.Message.Conversation.Participants.Any(p => p.UserId == userId))
                return Forbid();

            // Read and return file
            var filePath = GetFullFilePath(attachment.FileUrl);
            if (!System.IO.File.Exists(filePath))
                return NotFound(new { error = "File not found on disk" });

            var fileData = await System.IO.File.ReadAllBytesAsync(filePath);
            return File(fileData, attachment.MimeType ?? "application/octet-stream", attachment.FileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving media: {MessageId}/{AttachmentId}", messageId, attachmentId);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    private FileValidationResult ValidateImageFile(IFormFile file)
    {
        // Check file size
        if (file.Length > MaxImageSizeBytes)
            return new FileValidationResult(false, $"File size exceeds maximum limit of {MaxImageSizeBytes / 1_000_000}MB");

        // Check MIME type
        if (!AllowedImageTypes.Contains(file.ContentType.ToLower()))
            return new FileValidationResult(false, "Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed");

        // Validate file signature
        using var stream = file.OpenReadStream();
        var header = new byte[8];
        stream.Read(header, 0, 8);

        if (!ValidateImageHeader(header, file.ContentType))
            return new FileValidationResult(false, "Invalid file format or corrupted file");

        return new FileValidationResult(true, null);
    }

    private static bool ValidateImageHeader(byte[] header, string contentType)
    {
        return contentType.ToLower() switch
        {
            "image/jpeg" or "image/jpg" => header[0] == 0xFF && header[1] == 0xD8 && header[2] == 0xFF,
            "image/png" => header[0] == 0x89 && header[1] == 0x50 && header[2] == 0x4E && header[3] == 0x47,
            "image/gif" => (header[0] == 0x47 && header[1] == 0x49 && header[2] == 0x46) || // GIF87a or GIF89a
                          (header[0] == 0x47 && header[1] == 0x49 && header[2] == 0x46),
            "image/webp" => header[0] == 0x52 && header[1] == 0x49 && header[2] == 0x46 && header[3] == 0x46,
            _ => false
        };
    }

    private async Task<byte[]> GenerateThumbnailAsync(byte[] imageData)
    {
        try
        {
            using var originalImage = SKImage.FromEncodedData(imageData);
            using var bitmap = SKBitmap.FromImage(originalImage);
            
            // Calculate thumbnail dimensions maintaining aspect ratio
            var (newWidth, newHeight) = CalculateThumbnailDimensions(
                bitmap.Width, bitmap.Height, ThumbnailMaxWidth, ThumbnailMaxHeight);
            
            // Resize image
            using var resizedBitmap = bitmap.Resize(new SKImageInfo(newWidth, newHeight), SKSamplingOptions.Default);
            using var resizedImage = SKImage.FromBitmap(resizedBitmap);
            
            // Encode as JPEG with 80% quality
            using var data = resizedImage.Encode(SKEncodedImageFormat.Jpeg, 80);
            return data.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating thumbnail");
            // Return original image data if thumbnail generation fails
            return imageData;
        }
    }

    private static (int width, int height) CalculateThumbnailDimensions(
        int originalWidth, int originalHeight, int maxWidth, int maxHeight)
    {
        var aspectRatio = (double)originalWidth / originalHeight;
        
        int newWidth, newHeight;
        
        if (aspectRatio > 1) // Landscape
        {
            newWidth = Math.Min(originalWidth, maxWidth);
            newHeight = (int)(newWidth / aspectRatio);
        }
        else // Portrait or square
        {
            newHeight = Math.Min(originalHeight, maxHeight);
            newWidth = (int)(newHeight * aspectRatio);
        }
        
        return (newWidth, newHeight);
    }

    private async Task<string> StoreFileAsync(string fileName, byte[] fileData)
    {
        // Create uploads directory if it doesn't exist
        var uploadsPath = Path.Combine(_environment.ContentRootPath, "uploads", "media");
        Directory.CreateDirectory(uploadsPath);
        
        // Store file
        var filePath = Path.Combine(uploadsPath, fileName);
        await System.IO.File.WriteAllBytesAsync(filePath, fileData);
        
        // Return relative path for storage in database
        return Path.Combine("uploads", "media", fileName);
    }

    private string GetFullFilePath(string relativePath)
    {
        return Path.Combine(_environment.ContentRootPath, relativePath);
    }

    private static string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
    }
}

public class MediaUploadResponse
{
    public string FileId { get; set; } = null!;
    public string FileName { get; set; } = null!;
    public long FileSizeBytes { get; set; }
    public string? MimeType { get; set; }
    public string ThumbnailBase64 { get; set; } = null!;
    public string FileUrl { get; set; } = null!;
}

public class FileValidationResult
{
    public bool IsValid { get; }
    public string? ErrorMessage { get; }

    public FileValidationResult(bool isValid, string? errorMessage)
    {
        IsValid = isValid;
        ErrorMessage = errorMessage;
    }
}
