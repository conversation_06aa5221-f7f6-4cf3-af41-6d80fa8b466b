﻿<?xml version="1.0" encoding="utf-8" ?>
<local:AuthCodeFormViewBase
    x:Class="Platform.Client.Common.Features.AuthCodes.AuthCodeFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.AuthCodes"
    Title="AuthCodeFormView"
    x:DataType="local:AuthCodeFormView">
    <VerticalStackLayout>
        <Border
            Background="#EEF3F4"
            HeightRequest="50"
            StrokeThickness="0">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="16,16,0,0" />
            </Border.StrokeShape>
            <Grid Margin="20,8" ColumnDefinitions="*,Auto">
                <Label
                    FontSize="14"
                    HeightRequest="24"
                    Text="{Binding SelectedItem.AuthCode}" />
                <Image Grid.Column="1" Source="lock" />
            </Grid>
        </Border>

        <Button
            BackgroundColor="#333"
            Command="{Binding SaveCommand}"
            FontFamily="Poppins"
            FontSize="20"
            Text="Generate Code"
            TextColor="#fff" />
    </VerticalStackLayout>
</local:AuthCodeFormViewBase>
