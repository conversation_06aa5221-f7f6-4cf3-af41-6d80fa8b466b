# Service Injection Reference Guide

## Base Component Architecture Analysis

### Inheritance Hierarchy

```
ComponentBase (Blazor)
    ↓
FrameworkBaseComponent
    ↓
FormBase<TFormModel, TFormViewModel, TKey, TService>
ListingBase<TListViewModel, TListBusinessObject, TFilterViewModel, TFilterBusinessObject, TService>
```

### Available Services by Inheritance Level

#### 1. FrameworkBaseComponent (Root Level)
**Location**: `src/DeepMessage.Framework.Core/Framework/FrameworkBaseComponent.cs`

**Automatically Injected Services**:
```csharp
[Inject] public NavigationManager? NavigationManager { get; set; }
[Inject] public ILocalStorageService? StorageService { get; set; }
[Inject] public KtDialogService? DialogService { get; set; }
[Inject] public KtNotificationService NotificationService { get; set; }
[Inject] public AlertService AlertService { get; set; }
[Inject] protected IServiceScopeFactory? ScopeFactory { get; set; }
[Inject] public IJSRuntime? JsRuntime { get; set; }
[Inject] private ILogger<FrameworkBaseComponent>? Logger { get; set; }
```

**Available Properties**:
- `ModalSize`: String for modal dialog sizing
- `DisplayDialogCross`: Boolean for dialog cross display
- `DialogConfig`: Modal dialog configuration
- `OperationId`: Unique operation identifier (Guid)

**Available Methods**:
- `ShowDialog()`: Display modal dialogs
- `GetTypeByFullName()`: Type resolution utility
- `P()`: Parameter helper for component parameters

#### 2. FormBase<TFormModel, TFormViewModel, TKey, TService>
**Location**: `src/DeepMessage.Framework.Core/Framework/FormBase.cs`

**Additional Injected Services**:
```csharp
[Inject] protected ILogger<TService> Logger { get; set; }
```

**Available Properties**:
- `Id`: Primary key parameter from query string
- `Args`: Generic arguments object
- `Position`: Form position parameter
- `KeepAlive`: Form persistence flag
- `OperationId`: Operation identifier
- `Error`: Error message string
- `SelectedItem`: Form view model instance
- `IsWorking`: Loading state indicator

**Available Methods**:
- `LoadItem()`: Load form data by ID
- `SaveAsync()`: Save form data
- `BeforeSaveAsync()`: Pre-save validation hook
- `OnAfterSaveAsync()`: Post-save processing hook
- `CreateSelectedItem()`: Create new view model instance
- `InitialzeJsScrips()`: JavaScript initialization hook

#### 3. ListingBase<TListViewModel, TListBusinessObject, TFilterViewModel, TFilterBusinessObject, TService>
**Location**: `src/DeepMessage.Framework.Core/Framework/ListingBase.cs`

**Additional Injected Services**: None (inherits all from FrameworkBaseComponent)

**Available Properties**:
- `Items`: List of view model items
- `FilterViewModel`: Filter/search parameters
- `TotalPages`: Pagination total pages
- `TotalRecords`: Total record count
- `IsWorking`: Loading state indicator
- `Error`: Error message string
- `LoadItemsOnInit`: Auto-load flag

**Available Methods**:
- `LoadItems()`: Load paginated data
- `BlockTableUI()` / `UnBlockTableUI()`: UI state management
- `LoadSelectLists()`: Load dropdown data
- `ConvertToListViewModel()`: Business object to view model conversion
- `ItemsLoaded()`: Post-load processing hook
- `FilterViewModel_PropertyChanged()`: Auto-refresh on filter changes

## Service Injection Best Practices

### ✅ DO NOT Inject These Services (Already Available)
```csharp
// ❌ REDUNDANT - Already available in base classes
[Inject] private NavigationManager Navigation { get; set; }
[Inject] private IJSRuntime JSRuntime { get; set; }
[Inject] private ILocalStorageService StorageService { get; set; }
[Inject] private IServiceScopeFactory ScopeFactory { get; set; }
[Inject] private KtDialogService DialogService { get; set; }
[Inject] private KtNotificationService NotificationService { get; set; }
[Inject] private AlertService AlertService { get; set; }
```

### ✅ DO Inject Component-Specific Services
```csharp
// ✅ CORRECT - Component-specific services not in base classes
[Inject] private IAuthenticationStateProvider AuthStateProvider { get; set; }
[Inject] private ISpecificBusinessService BusinessService { get; set; }
[Inject] private ICustomNotificationService CustomNotifications { get; set; }
```

### Service Access Patterns

#### From FormBase Components:
```csharp
// Use inherited services directly
NavigationManager.NavigateTo("/some-route");
await StorageService.SetValue("key", "value");
await JsRuntime.InvokeVoidAsync("console.log", "message");
Logger.LogInformation("Form operation completed");
```

#### From ListingBase Components:
```csharp
// Use inherited services directly
await LoadItems(); // Built-in method
FilterViewModel.SearchKey = "search term"; // Auto-triggers reload
NavigationManager.NavigateTo($"/edit/{item.Id}");
```

## Component Template Examples

### Optimized FormBase Component
```csharp
// ✅ CORRECT - Only inject services not available in base class
public partial class MyForm : FormBase<MyBusinessObject, MyViewModel, string, IMyDataService>
{
    // Only inject component-specific services
    [Inject] private IAuthenticationStateProvider AuthStateProvider { get; set; }
    
    // Use inherited services directly (no injection needed)
    private async Task HandleSave()
    {
        await SaveAsync(); // From FormBase
        NavigationManager.NavigateTo("/success"); // From FrameworkBaseComponent
        await StorageService.SetValue("last_save", DateTime.Now.ToString()); // From FrameworkBaseComponent
    }
}
```

### Optimized ListingBase Component
```csharp
// ✅ CORRECT - Only inject services not available in base class
public partial class MyListing : ListingBase<MyViewModel, MyBusinessObject, MyFilterViewModel, MyFilterBusinessObject, IMyDataService>
{
    // Only inject component-specific services
    [Inject] private ICustomService CustomService { get; set; }
    
    // Use inherited services directly (no injection needed)
    private async Task HandleItemClick(MyViewModel item)
    {
        NavigationManager.NavigateTo($"/edit/{item.Id}"); // From FrameworkBaseComponent
        await JsRuntime.InvokeVoidAsync("highlight", item.Id); // From FrameworkBaseComponent
    }
    
    protected override async Task OnInitializedAsync()
    {
        // LoadItems() is automatically called if LoadItemsOnInit is true
        await base.OnInitializedAsync();
    }
}
```

## Migration Checklist

### For Existing Components:
1. **Remove Redundant Injections**: Delete `[Inject]` attributes for services available in base classes
2. **Update Service Access**: Change from injected properties to inherited properties
3. **Verify Functionality**: Ensure all service calls still work correctly
4. **Update Documentation**: Document any component-specific service injections

### For New Components:
1. **Choose Correct Base Class**: FormBase for forms, ListingBase for listings
2. **Identify Required Services**: Only inject services not available in base classes
3. **Document Injections**: Add comments explaining why component-specific services are needed
4. **Follow Naming Conventions**: Use consistent property naming patterns

## Architecture Compliance

### XAML Application Alignment
- **Business Objects**: Located in `DeepMessage.ServiceContracts.Features.*`
- **View Models**: Located in `Platform.Client.Services.Features.*`
- **Data Services**: Both server-side and client-side implementations
- **Component Structure**: Consistent inheritance patterns across platforms

### Component Organization
```
Platform.Razor/Features/
├── [FeatureName]/
│   ├── Listing/
│   │   ├── [Feature]Listing.razor
│   │   └── [Feature]Listing.razor.cs
│   └── Form/
│       ├── [Feature]Form.razor
│       └── [Feature]Form.razor.cs
```

This guide ensures optimal service injection patterns while maintaining consistency with the existing XAML application architecture.
