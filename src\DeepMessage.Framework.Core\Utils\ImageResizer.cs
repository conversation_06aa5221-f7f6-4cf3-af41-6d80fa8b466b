﻿using SkiaSharp;

namespace MobileApp.MauiShared.Utils
{
    public static class ImageResizer
    {
        public static SKBitmap ResizeImageWithAspectRatio(SKBitmap originalBitmap, int maxWidth, int maxHeight)
        {
            double ratioX = (double)maxWidth / originalBitmap.Width;
            double ratioY = (double)maxHeight / originalBitmap.Height;
            double ratio = Math.Min(ratioX, ratioY);

            int newWidth = (int)(originalBitmap.Width * ratio);
            int newHeight = (int)(originalBitmap.Height * ratio);

            SKBitmap resizedBitmap = new SKBitmap(newWidth, newHeight);
            using (SKCanvas canvas = new SKCanvas(resizedBitmap))
            {
                canvas.DrawBitmap(originalBitmap, new SKRect(0, 0, newWidth, newHeight));
            }

            return resizedBitmap;
        }

    }
}
