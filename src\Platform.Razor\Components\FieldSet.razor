﻿@using Microsoft.AspNetCore.Components.Forms;
@using System.Linq.Expressions;
@using System.Reflection;
@using System.ComponentModel.DataAnnotations;

<div class="flex flex-col gap-2 w-full">
    <label for="@Id" class="block text-xs font-medium text-gray-900 md:text-sm">
        @Label  <span class="text-red-600">@RequiredMark</span>
    </label>
    @ChildContent
    @if (For != null)
    {
        <ValidationMessage For="@For" class="text-red-500 text-xs md:text-sm" />
    }
    @if (!string.IsNullOrEmpty(HelpText))
    {
        <p class="text-xs text-gray-600 md:text-sm">@HelpText</p>
    }
</div>


@code {
    public string Id { get; set; } = $"__{Guid.NewGuid().ToString().ToLower().Split("-")[0]}";

    [Parameter, EditorRequired]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public string? Label { get; set; }

    [Parameter]
    public string? HelpText { get; set; }

    [Parameter]
    public Expression<Func<object>>? For { get; set; }

    [Parameter]
    public bool Required { get; set; }

    public string RequiredMark
    {
        get
        {
            return Required ? "*" : string.Empty;
        }
    }

}

