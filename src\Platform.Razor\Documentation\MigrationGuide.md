# Migration Guide: Optimizing Service Injection Patterns

## Overview

This guide provides step-by-step instructions for migrating existing Razor components to use optimized service injection patterns and implementing server-side data synchronization.

## Phase 1: Service Injection Optimization

### Step 1: Identify Components to Migrate

Run this analysis on your existing components:

```bash
# Find all components with redundant service injections
grep -r "\[Inject\].*NavigationManager" src/Platform.Razor/Features/
grep -r "\[Inject\].*IJSRuntime" src/Platform.Razor/Features/
grep -r "\[Inject\].*ILocalStorageService" src/Platform.Razor/Features/
grep -r "\[Inject\].*IServiceScopeFactory" src/Platform.Razor/Features/
```

### Step 2: Update Component Code-Behind Files

For each component found, apply these changes:

#### Before (Redundant Injections):
```csharp
public partial class MyComponent : ListingBase<...>
{
    [Inject] private NavigationManager Navigation { get; set; } = null!;
    [Inject] private IJSRuntime JSRuntime { get; set; } = null!;
    [Inject] private ILocalStorageService StorageService { get; set; } = null!;
    [Inject] private IServiceScopeFactory ScopeFactory { get; set; } = null!;
    
    private async Task HandleClick()
    {
        Navigation.NavigateTo("/some-route");
        await JSRuntime.InvokeVoidAsync("console.log", "clicked");
        await StorageService.SetValue("key", "value");
    }
}
```

#### After (Optimized):
```csharp
public partial class MyComponent : ListingBase<...>
{
    // ✅ Only inject component-specific services
    [Inject] private IDataSynchronizationService SyncService { get; set; } = null!;
    
    // ❌ Removed redundant injections - use inherited properties instead
    
    private async Task HandleClick()
    {
        // ✅ Use inherited properties
        NavigationManager?.NavigateTo("/some-route");
        await JsRuntime?.InvokeVoidAsync("console.log", "clicked");
        await StorageService?.SetValue("key", "value");
    }
}
```

### Step 3: Update Service Usage Patterns

Replace injected service usage with inherited property usage:

| Old Pattern | New Pattern | Notes |
|-------------|-------------|-------|
| `Navigation.NavigateTo()` | `NavigationManager?.NavigateTo()` | Use null-conditional operator |
| `JSRuntime.InvokeAsync()` | `JsRuntime?.InvokeAsync()` | Property name is different |
| `StorageService.GetValue()` | `StorageService?.GetValue()` | Same property name |
| `ScopeFactory.CreateScope()` | `ScopeFactory?.CreateScope()` | Same property name |

### Step 4: Validation Checklist

For each migrated component:

- [ ] Removed redundant `[Inject]` attributes
- [ ] Updated service usage to use inherited properties
- [ ] Added null-conditional operators for safety
- [ ] Verified component still compiles
- [ ] Tested component functionality
- [ ] Added component-specific service injections only when needed

## Phase 2: Data Synchronization Implementation

### Step 1: Register Synchronization Service

Add to your DI container (Program.cs or Startup.cs):

```csharp
// Register data synchronization service
services.AddScoped<IDataSynchronizationService, DataSynchronizationService>();

// Ensure required dependencies are registered
services.AddScoped<ILocalStorageService, LocalStorageService>();
services.AddScoped<IFriendsListingDataService, FriendsClientSideListingDataService>();
services.AddScoped<IChatThreadsListingDataService, ChatThreadsClientSideListingDataService>();
services.AddScoped<IChatMessagesListingDataService, ChatMessagesClientSideListingDataService>();
```

### Step 2: Update Listing Components with Sync

For listing components that need server synchronization:

```csharp
public partial class MyListing : ListingBase<...>
{
    [Inject] private IDataSynchronizationService SyncService { get; set; } = null!;
    [Inject] private ILogger<MyListing> ComponentLogger { get; set; } = null!;
    
    private bool _isFirstLoad = true;
    private bool _isSyncing = false;

    protected override async Task OnInitializedAsync()
    {
        // Subscribe to sync events
        SyncService.SyncProgressChanged += OnSyncProgressChanged;
        SyncService.SyncCompleted += OnSyncCompleted;
        
        await base.OnInitializedAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && _isFirstLoad)
        {
            _isFirstLoad = false;
            await CheckAndPerformSync();
        }
        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task CheckAndPerformSync()
    {
        try
        {
            var userId = await StorageService?.GetValue("user_id") ?? string.Empty;
            if (string.IsNullOrEmpty(userId)) return;

            var syncRequired = await SyncService.IsSyncRequiredAsync(userId);
            if (syncRequired && !_isSyncing)
            {
                _isSyncing = true;
                var result = await SyncService.SynchronizeUserDataAsync(userId);
                if (result.Success)
                {
                    await LoadItems(); // Refresh after sync
                }
                _isSyncing = false;
            }
        }
        catch (Exception ex)
        {
            ComponentLogger.LogError(ex, "Error during sync");
            _isSyncing = false;
        }
    }

    private async void OnSyncProgressChanged(object? sender, SyncProgressEventArgs e)
    {
        await InvokeAsync(StateHasChanged);
    }

    private async void OnSyncCompleted(object? sender, SyncCompletedEventArgs e)
    {
        await InvokeAsync(async () =>
        {
            if (e.Result.Success)
            {
                await LoadItems();
            }
            StateHasChanged();
        });
    }

    public void Dispose()
    {
        if (SyncService != null)
        {
            SyncService.SyncProgressChanged -= OnSyncProgressChanged;
            SyncService.SyncCompleted -= OnSyncCompleted;
        }
    }
}
```

### Step 3: Add Sync UI Indicators

Update your Razor templates to show sync status:

```razor
@if (_isSyncing)
{
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
            <span class="text-blue-800 dark:text-blue-200">Synchronizing data...</span>
        </div>
    </div>
}
```

## Phase 3: Testing and Validation

### Step 1: Component Testing

For each migrated component:

1. **Functionality Test**: Verify all features work as before
2. **Service Access Test**: Confirm inherited services are accessible
3. **Error Handling Test**: Test null-conditional operators work correctly
4. **Sync Test**: Verify synchronization works for listing components

### Step 2: Integration Testing

1. **Authentication Flow**: Test with stealth mode authentication
2. **Navigation**: Verify bottom tab navigation works correctly
3. **Data Consistency**: Confirm synchronized data appears correctly
4. **Performance**: Check for any performance regressions

### Step 3: Error Scenarios

Test these error scenarios:

1. **Network Failure**: Sync fails due to network issues
2. **Authentication Expiry**: Token expires during sync
3. **Service Unavailable**: Backend services are down
4. **Partial Sync**: Some data syncs successfully, some fails

## Phase 4: Performance Optimization

### Step 1: Implement Caching

```csharp
// Cache frequently accessed data
private readonly MemoryCache _cache = new MemoryCache(new MemoryCacheOptions());

private async Task<List<T>> GetCachedData<T>(string cacheKey, Func<Task<List<T>>> dataLoader)
{
    if (_cache.TryGetValue(cacheKey, out List<T> cachedData))
    {
        return cachedData;
    }

    var data = await dataLoader();
    _cache.Set(cacheKey, data, TimeSpan.FromMinutes(5));
    return data;
}
```

### Step 2: Implement Background Sync

```csharp
// Background sync service
public class BackgroundSyncService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await PerformBackgroundSync();
            await Task.Delay(TimeSpan.FromMinutes(15), stoppingToken);
        }
    }
}
```

### Step 3: Optimize Data Loading

```csharp
// Implement pagination for large datasets
private async Task LoadItemsWithPagination(int pageSize = 50)
{
    FilterViewModel.RowsPerPage = pageSize;
    FilterViewModel.UsePagination = true;
    await LoadItems();
}
```

## Rollback Plan

If issues arise during migration:

### Step 1: Identify Problem Components
```bash
# Find components with compilation errors
dotnet build 2>&1 | grep -E "(error|Error)"
```

### Step 2: Revert Service Injections
```csharp
// Temporarily restore injections for problematic components
[Inject] private NavigationManager Navigation { get; set; } = null!;
// Update usage back to: Navigation.NavigateTo()
```

### Step 3: Disable Sync Features
```csharp
// Disable sync temporarily
private async Task CheckAndPerformSync()
{
    // TODO: Re-enable after fixing issues
    return;
}
```

## Success Metrics

Track these metrics to measure migration success:

1. **Code Reduction**: Lines of code removed from redundant injections
2. **Performance**: Page load times before/after migration
3. **Error Rate**: Component error rates before/after migration
4. **Sync Efficiency**: Data synchronization success rates
5. **User Experience**: User feedback on responsiveness and reliability

## Support and Troubleshooting

### Common Issues

1. **NullReferenceException**: Add null-conditional operators
2. **Service Not Found**: Verify service registration in DI container
3. **Sync Failures**: Check network connectivity and authentication
4. **Performance Issues**: Implement caching and pagination

### Getting Help

1. Check the Service Injection Reference Guide
2. Review Component Template Examples
3. Examine the updated FriendsListing component as a reference
4. Test with the TabNavigationDemo for integration examples

This migration guide ensures a smooth transition to optimized service injection patterns while implementing robust server-side data synchronization capabilities.
