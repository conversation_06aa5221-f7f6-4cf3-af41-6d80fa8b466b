﻿<?xml version="1.0" encoding="utf-8" ?>
<local:AuthCodeListingViewBase
    x:Class="Platform.Client.Common.Features.AuthCodes.AuthCodeListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:authcodes="clr-namespace:Platform.Client.Services.Features.AuthCodes;assembly=Platform.Client.Services"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.AuthCodes"
    Title="Referral Codes"
    x:DataType="local:AuthCodeListingView"
    BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                      Dark={StaticResource Gray800}}"
    Shell.BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                            Dark={StaticResource Gray800}}"
    Shell.TitleColor="{AppThemeBinding Light={StaticResource Gray800},
                                       Dark={StaticResource White}}">

    <ContentPage.ToolbarItems>
        <ToolbarItem Command="{Binding SyncDownItemsCommand}">
            <ToolbarItem.IconImageSource>
                <FontImageSource
                    FontFamily="Jelly"
                    Glyph="&#xf055;"
                    Size="20"
                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                            Dark={StaticResource Gray300}}" />
            </ToolbarItem.IconImageSource>
        </ToolbarItem>
    </ContentPage.ToolbarItems>

    <Grid RowDefinitions="Auto,16,*">



        <!--  Referral Codes List  -->
        <CollectionView
            x:Name="collection"
            Grid.Row="2"
            BackgroundColor="{AppThemeBinding Light={StaticResource Gray50},
                                              Dark={StaticResource Gray700}}"
            ItemsSource="{Binding Items}"
            VerticalOptions="Fill">
            <CollectionView.ItemTemplate>
                <DataTemplate x:DataType="authcodes:AuthCodeListingViewModel">
                    <Border
                        Margin="16,8"
                        Padding="16"
                        Background="{AppThemeBinding Light={StaticResource White},
                                                     Dark={StaticResource Gray800}}"
                        Stroke="{AppThemeBinding Light={StaticResource Gray200},
                                                 Dark={StaticResource Gray600}}">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="12" />
                        </Border.StrokeShape>
                        <Grid ColumnDefinitions="*,Auto" RowDefinitions="Auto,12,Auto,0,Auto">

                            <!--  Referral Code  -->
                            <VerticalStackLayout
                                Grid.Row="0"
                                Grid.Column="0"
                                Spacing="4">
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="18"
                                    Text="{Binding AuthCode}"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray800},
                                                                Dark={StaticResource White}}" />
                                <Label
                                    FontSize="12"
                                    Text="Referral Code"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                                Dark={StaticResource Gray400}}" />
                            </VerticalStackLayout>

                            <!--  Status Badge  -->
                            <Border
                                Grid.Row="0"
                                Grid.Column="1"
                                Padding="12,4"
                                Background="{AppThemeBinding Light={StaticResource Gray50},
                                                             Dark={StaticResource Gray600}}"
                                StrokeThickness="0.3"
                                VerticalOptions="Start">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="16" />
                                </Border.StrokeShape>
                                <Label
                                    FontSize="12"
                                    Text="{Binding AuthCodeStatus}"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray300}}" />
                            </Border>

                            <!--  Date Information  -->
                            <VerticalStackLayout
                                Grid.Row="2"
                                Grid.Column="0"
                                Grid.ColumnSpan="2"
                                Spacing="4">
                                <HorizontalStackLayout Spacing="16">
                                    <VerticalStackLayout Spacing="2">
                                        <Label
                                            FontSize="12"
                                            Text="Created"
                                            TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                                        Dark={StaticResource Gray400}}" />
                                        <Label
                                            FontSize="14"
                                            Text="{Binding CreatedAt, StringFormat='{0:MMM dd, yyyy}'}"
                                            TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                        Dark={StaticResource Gray300}}" />
                                    </VerticalStackLayout>
                                    <VerticalStackLayout Spacing="2">
                                        <Label
                                            FontSize="12"
                                            Text="Expires"
                                            TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                                        Dark={StaticResource Gray400}}" />
                                        <Label
                                            FontSize="14"
                                            Text="{Binding ExpiresAt, StringFormat='{0:MMM dd, yyyy}'}"
                                            TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                        Dark={StaticResource Gray300}}" />
                                    </VerticalStackLayout>
                                </HorizontalStackLayout>
                            </VerticalStackLayout>

                            <!--  Action Buttons  -->
                            <HorizontalStackLayout
                                Grid.Row="4"
                                Grid.Column="0"
                                Grid.ColumnSpan="2"
                                HorizontalOptions="End">
                                <!--  Copy Button  -->
                                <Button
                                    Background="Transparent"
                                    Command="{Binding Source={RelativeSource AncestorType={x:Type local:AuthCodeListingView}}, Path=CopyCodeCommand}"
                                    CommandParameter="{Binding AuthCode}"
                                    WidthRequest="40">
                                    <Button.ImageSource>
                                        <FontImageSource
                                            FontFamily="Jelly"
                                            Glyph="&#xf24d;"
                                            Size="16"
                                            Color="{AppThemeBinding Light={StaticResource Gray700},
                                                                    Dark={StaticResource Gray300}}" />
                                    </Button.ImageSource>
                                </Button>

                                <!--  Share Button  -->
                                <Button
                                    Background="Transparent"
                                    Command="{Binding Source={RelativeSource AncestorType={x:Type local:AuthCodeListingView}}, Path=ShareCodeCommand}"
                                    CommandParameter="{Binding AuthCode}"
                                    WidthRequest="40">
                                    <Button.ImageSource>
                                        <FontImageSource
                                            FontFamily="Jelly"
                                            Glyph="&#xf1e0;"
                                            Size="16"
                                            Color="{AppThemeBinding Light={StaticResource Gray700},
                                                                    Dark={StaticResource Gray300}}" />
                                    </Button.ImageSource>
                                </Button>
                            </HorizontalStackLayout>
                        </Grid>
                    </Border>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
    </Grid>
</local:AuthCodeListingViewBase>
