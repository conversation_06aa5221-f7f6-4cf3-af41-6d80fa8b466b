﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="ModelFury.Briefly.HybridApp.MainPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.HybridApp"
    BackgroundColor="White"
    NavigationPage.HasBackButton="False"
    NavigationPage.HasNavigationBar="False"
    Shell.FlyoutBehavior="Disabled"
    Shell.NavBarIsVisible="False"
    Shell.TabBarIsVisible="False">

    <BlazorWebView
        x:Name="blazorWebView"
        BackgroundColor="White"
        HostPage="wwwroot/index.html">
        <BlazorWebView.RootComponents>
            <RootComponent ComponentType="{x:Type local:Components.Routes}" Selector="#app" />
        </BlazorWebView.RootComponents>
    </BlazorWebView>

</ContentPage>
