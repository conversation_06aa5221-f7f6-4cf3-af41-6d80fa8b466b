﻿using Android.App;
using Android.Content;
using Android.Runtime;
using Firebase;
using Plugin.Firebase.CloudMessaging;

namespace ModelFury.Briefly.MobileApp
{
    [Application]
    public class MainApplication : MauiApplication
    {
        //UpdateBroadcastReceiver? _receiver;
        public MainApplication(IntPtr handle, JniHandleOwnership ownership)
            : base(handle, ownership)
        {
            try
            {
                //_receiver = new UpdateBroadcastReceiver();
                //var filter = new IntentFilter("com.companyname.deepmessage.UPDATE");
                //RegisterReceiver(_receiver, filter);
                var options = FirebaseOptions.FromResource(this);
                if (FirebaseApp.InitializeApp(this, options) == null)
                {
                    Console.WriteLine("Firebase already initialized");
                }
                CreateNotificationChannel();
            }
            catch (Exception ex)
            {
                // Handle any exceptions that may occur during receiver registration
                System.Diagnostics.Debug.WriteLine($"Error registering broadcast receiver: {ex.Message}");
            }
        }
        private void CreateNotificationChannel()
        {
            var channelId = $"{PackageName}.general";
            var notificationManager = (NotificationManager?)GetSystemService(NotificationService);
            var channel = new NotificationChannel(channelId, "General", NotificationImportance.Default);
            notificationManager.CreateNotificationChannel(channel);
            FirebaseCloudMessagingImplementation.ChannelId = channelId; 
        }

        public override void OnCreate()
        {       
            base.OnCreate();
        }

        protected override Microsoft.Maui.Hosting.MauiApp CreateMauiApp() => MauiProgram.CreateMauiApp();
    }
}
