namespace Platform.Client.Services.Features.Media;

/// <summary>
/// Service for compressing images before upload to optimize bandwidth and storage
/// </summary>
public interface IImageCompressionService
{
    /// <summary>
    /// Compresses an image to reduce file size while maintaining quality
    /// </summary>
    /// <param name="imageData">Original image data</param>
    /// <param name="maxWidth">Maximum width in pixels (default: 1920)</param>
    /// <param name="maxHeight">Maximum height in pixels (default: 1920)</param>
    /// <param name="quality">JPEG quality 1-100 (default: 85)</param>
    /// <returns>Compressed image data</returns>
    Task<byte[]> CompressImageAsync(byte[] imageData, int maxWidth = 1920, int maxHeight = 1920, int quality = 85);

    /// <summary>
    /// Generates a thumbnail from image data
    /// </summary>
    /// <param name="imageData">Original image data</param>
    /// <param name="maxWidth">Maximum thumbnail width (default: 300)</param>
    /// <param name="maxHeight">Maximum thumbnail height (default: 300)</param>
    /// <returns>Thumbnail image data</returns>
    Task<byte[]> GenerateThumbnailAsync(byte[] imageData, int maxWidth = 300, int maxHeight = 300);

    /// <summary>
    /// Gets image dimensions without loading the full image
    /// </summary>
    /// <param name="imageData">Image data</param>
    /// <returns>Width and height in pixels</returns>
    Task<(int width, int height)> GetImageDimensionsAsync(byte[] imageData);

    /// <summary>
    /// Validates if the provided data is a valid image
    /// </summary>
    /// <param name="imageData">Image data to validate</param>
    /// <returns>True if valid image, false otherwise</returns>
    bool IsValidImage(byte[] imageData);
}
