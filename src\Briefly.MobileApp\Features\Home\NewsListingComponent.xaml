﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="ModelFury.Briefly.MobileApp.Features.Home.NewsListingComponent"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp.Features.Home"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    Title="Ai Summarized News"
    x:DataType="local:NewsListingComponent"
    BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                      Dark={StaticResource Gray800}}"
    Shell.BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                            Dark={StaticResource Gray800}}"
    Shell.TitleColor="{AppThemeBinding Light={StaticResource Gray800},
                                       Dark={StaticResource White}}">

    <Border BackgroundColor="{AppThemeBinding Light={StaticResource Gray50}, Dark={StaticResource Gray700}}" StrokeThickness="0">
        <!--<Border.StrokeShape>
            <RoundRectangle CornerRadius="0" />
        </Border.StrokeShape>-->
        <Grid
            Padding="8"
            RowDefinitions="Auto, *"
            VerticalOptions="Fill">
            <Grid Margin="8,12">
                <Entry
                    x:Name="txtSearch"
                    Placeholder="Search News"
                    Text="{Binding SelectedItem.SelectedItem.PassKey}" />
                <ImageButton
                    BackgroundColor="Transparent"
                    Clicked="ImageButton_Clicked"
                    HorizontalOptions="End"
                    MaximumHeightRequest="32"
                    MaximumWidthRequest="32"
                    Source="search_dark.svg" />
            </Grid>
            <CollectionView
                Grid.Row="1"
                ItemsSource="{Binding NewsItems}"
                SelectionMode="Single">
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="local:NewsItemViewModel">
                        <Border
                            Margin="8,4"
                            Padding="12"
                            BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                                              Dark={StaticResource Gray800}}"
                            StrokeThickness="0">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="12" />
                            </Border.StrokeShape>
                            <Border.Shadow>
                                <Shadow
                                    Brush="{AppThemeBinding Light={StaticResource Gray300},
                                                            Dark={StaticResource Black}}"
                                    Opacity="0.1"
                                    Radius="4"
                                    Offset="0,2" />
                            </Border.Shadow>
                            <Grid ColumnDefinitions="80,*" RowDefinitions="Auto,*,Auto">
                                <Grid.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type local:NewsListingComponent}}, Path=BindingContext.ReaArticleCommand}" CommandParameter="{Binding .}" />
                                </Grid.GestureRecognizers>

                                <!--  News Image  -->
                                <Border
                                    Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="0"
                                    Margin="0,0,12,0"
                                    BackgroundColor="{AppThemeBinding Light={StaticResource Gray200},
                                                                      Dark={StaticResource Gray600}}"
                                    StrokeThickness="0">
                                    <Border.StrokeShape>
                                        <RoundRectangle CornerRadius="8" />
                                    </Border.StrokeShape>
                                    <Image
                                        Aspect="AspectFill"
                                        HeightRequest="60"
                                        Source="{Binding ImageUrl, FallbackValue='news_placeholder.svg'}"
                                        WidthRequest="80" />
                                </Border>

                                <!--  News Content  -->
                                <VerticalStackLayout
                                    Grid.Row="0"
                                    Grid.Column="1"
                                    Spacing="4">
                                    <Label
                                        FontAttributes="Bold"
                                        FontSize="14"
                                        LineBreakMode="TailTruncation"
                                        MaxLines="2"
                                        Text="{Binding Title}"
                                        TextColor="{AppThemeBinding Light={StaticResource Gray900},
                                                                    Dark={StaticResource White}}" />
                                    <Grid>
                                        <Label
                                            Margin="0,4,0,0"
                                            FontSize="12"
                                            Text="{Binding PubDate, StringFormat='{0:MMM dd, yyyy}'}"
                                            TextColor="{AppThemeBinding Light={StaticResource Gray600},
                                                                        Dark={StaticResource Gray300}}" />

                                        <HorizontalStackLayout
                                            Grid.Row="2"
                                            Grid.Column="0"
                                            Grid.ColumnSpan="2"
                                            HorizontalOptions="End"
                                            Spacing="12">

                                            <!--  Share Button  -->
                                            <Image
                                                BackgroundColor="Transparent"
                                                HeightRequest="16"
                                                Source="share_light.svg"
                                                WidthRequest="16" />

                                            <!--  Bookmark Button  -->
                                            <Image
                                                BackgroundColor="Transparent"
                                                HeightRequest="16"
                                                Source="bookmark_light.svg"
                                                WidthRequest="16" />

                                            <!--  Like Button  -->
                                            <Image
                                                BackgroundColor="Transparent"
                                                HeightRequest="16"
                                                Source="heart_light.svg"
                                                WidthRequest="16" />
                                        </HorizontalStackLayout>
                                    </Grid>
                                </VerticalStackLayout>

                                <Label
                                    Grid.Row="1"
                                    Grid.Column="1"
                                    Margin="0,8,0,0"
                                    FontSize="12"
                                    LineBreakMode="TailTruncation"
                                    MaxLines="2"
                                    Text="{Binding Description}"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray600},
                                                                Dark={StaticResource Gray200}}" />

                                <!--  Social Media Buttons  -->

                            </Grid>
                        </Border>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </Grid>
    </Border>
</ContentPage>
