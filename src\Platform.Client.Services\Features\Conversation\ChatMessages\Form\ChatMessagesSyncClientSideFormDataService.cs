﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
namespace Platform.Client.Services.Features.Conversation;
public class ChatMessagesSyncClientSideFormDataService : IChatMessagesSyncFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public ChatMessagesSyncClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(ChatMessagesSyncFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/ChatMessagesSyncsForm/Save", formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<ChatMessagesSyncFormBusinessObject> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<ChatMessagesSyncFormBusinessObject>($"api/ChatMessagesSyncsForm/GetItemById?id=" + id);
	}
}
