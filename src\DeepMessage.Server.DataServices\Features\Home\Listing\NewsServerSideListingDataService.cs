﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Home;
using System.Threading.Tasks;
using System.Xml.Linq;
namespace DeepMessage.Server.DataServices.Features.Home;
public class NewsServerSideListingDataService : ServerSideListingDataService<NewsListingBusinessObject, NewsFilterBusinessObject>, INewsListingDataService
{
    [SystemClaim(SystemClaimType.SystemDefault)]
    public override IQueryable<NewsListingBusinessObject> GetQuery(NewsFilterBusinessObject filterBusinessObject)
    {
        var newsItems = RssService.GetNewsAsync().GetAwaiter().GetResult().AsQueryable();

        if (!string.IsNullOrWhiteSpace(filterBusinessObject.SearchKey))
        {
            var searchTerm = filterBusinessObject.SearchKey.ToLower();
            newsItems = newsItems.Where(item =>
                (item.Title != null && item.Title.ToLower().Contains(searchTerm)) ||
                (item.Description != null && item.Description.ToLower().Contains(searchTerm))
            );
        }     

        return newsItems.OrderByDescending(item => item.PubDate);
    }

    public class RssService
    {
        private const string FeedUrl = "https://feeds.bbci.co.uk/news/rss.xml";
        private static readonly XNamespace MediaNamespace = "http://search.yahoo.com/mrss/";

        public static async Task<List<NewsListingBusinessObject>> GetNewsAsync()
        {
            try
            {
                var client = new HttpClient();
                var response = await client.GetStringAsync(FeedUrl);
                var doc = XDocument.Parse(response);

                var items = doc.Descendants("item").Select(item => new NewsListingBusinessObject
                {
                    Title = item.Element("title")?.Value,
                    Description = item.Element("description")?.Value,
                    Link = item.Element("link")?.Value,
                    Thumbnail = item.Element(MediaNamespace + "thumbnail")?.Attribute("url")?.Value,
                    PubDate = DateTime.TryParse(item.Element("pubDate")?.Value, out var date) ? date : DateTime.MinValue
                }).ToList();

                return items;
            }
            catch
            {
                return new List<NewsListingBusinessObject>();
            }
        }
    }
}
