﻿using Android.App;
using Firebase.Messaging;

namespace ModelFury.Briefly.MobileApp.Platforms.Android.Source;

[Service(Exported = false)]
[IntentFilter(new[] { "com.google.firebase.MESSAGING_EVENT" })]
public class BrieflyFirebaseMessagingService : FirebaseMessagingService
{
    public override void OnMessageReceived(RemoteMessage message)
    {
        base.OnMessageReceived(message);
        var title = message.GetNotification()?.Title;
        var body = message.GetNotification()?.Body;

        Console.WriteLine($"[Background] {title}: {body}");
    }
}
