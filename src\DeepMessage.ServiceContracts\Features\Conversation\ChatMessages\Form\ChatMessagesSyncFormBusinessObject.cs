﻿using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Enums;
using System.ComponentModel.DataAnnotations;
namespace DeepMessage.ServiceContracts.Features.Conversation;

/// <summary>
/// ✅ SECURE: E2E Encrypted Message Sync Business Object
/// Syncs MessageRecipient records with encrypted content, never plaintext
/// </summary>
public class ChatMessagesSyncFormBusinessObject
{
    [StringLength(450)]
    public string Id { get; set; } = null!;

    [StringLength(450)]
    public string ConversationId { get; set; } = null!;

    public string SenderId { get; set; } = null!;

    /// <summary>
    /// ❌ DEPRECATED: PlainContent should NEVER be synced for E2E encryption
    /// This field is kept for backward compatibility but should always be null
    /// </summary>
    [Obsolete("PlainContent violates E2E encryption. Use MessageRecipients instead.")]
    public string? PlainContent { get; set; } = null;


    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    //public byte SyncStatus { get; set; }

    // Soft-deletion
    public bool IsDeleted { get; set; } = false;
    public DateTime? DeletedAt { get; set; }

    // If you want to track edits:
    public bool IsEdited { get; set; } = false;
    public DateTime? EditedAt { get; set; }

    // Disappearing (ephemeral) message fields
    public bool IsEphemeral { get; set; } = false;
    public TimeSpan? DisappearAfter { get; set; }
    public DateTime? DisappearAt { get; set; }

    public bool EnableFallBackChannel { get; set; }

    public string? MessageRecepientId { get; set; } // extra field for queing logic
    public string? MessageRecepientUserName { get; set; } // extra field for queing logic
    //public IEnumerable<MessageRecipientSyncFormBusinessObject> MessageRecipients { get; set; }

    /// <summary>
    /// ✅ SECURE: List of encrypted message copies per recipient
    /// Each recipient gets their own encrypted copy with their public key
    /// </summary>
    public List<MessageRecipientSyncFormBusinessObject> MessageRecipients { get; set; } = new();

    public ChatMessagesSyncFormBusinessObject()
    {
        MessageRecipients = new List<MessageRecipientSyncFormBusinessObject>();
    }
}

/// <summary>
/// ✅ SECURE: MessageRecipient sync object for E2E encrypted messaging
/// Contains encrypted content specific to each recipient
/// </summary>
public class MessageRecipientSyncFormBusinessObject
{
    [StringLength(450)]
    public string Id { get; set; } = null!;

    [StringLength(450)]
    public string MessageId { get; set; } = null!;

    /// <summary>
    /// The user ID this encrypted message copy is intended for
    /// </summary>
    public string RecipientId { get; set; } = null!;

    /// <summary>
    /// ✅ SECURE: Message content encrypted with recipient's RSA public key
    /// This is the ONLY content that should be synced for E2E encryption
    /// </summary>
    public string EncryptedContent { get; set; } = null!; 

    /// <summary>
    /// Per-recipient delivery status tracking
    /// </summary>
    public MessageDeliveryStatus DeliveryStatus { get; set; }

    /// <summary>
    /// When the delivery status was last updated
    /// </summary>
    public DateTime? DeliveryStatusTime { get; set; }

    /// <summary>
    /// Whether this recipient has read the message
    /// </summary>
    public bool IsRead { get; set; }

    /// <summary>
    /// When the message was read by this recipient
    /// </summary>
    public DateTime? ReadAt { get; set; }

 
}

public class ChatMessageUpdate
{
    public string MessageId { get; set; } = null!;

    public string TargetUserId { get; set; } = null!;

    public MessageDeliveryStatus DeliveryStatus { get; set; }

    public DateTime DeliveryStatusTime { get; set; }

    public string SenderId { get; set; } = null!;

    /// <summary>
    /// ID of the MessageRecipient record that was processed (for tracking acknowledgement updates)
    /// </summary>
    public string MessageRecipientId { get; set; } = null!;
}