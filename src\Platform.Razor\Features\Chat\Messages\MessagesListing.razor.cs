using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Platform.Client.Data;
using Platform.Client.Services.Features.Conversation;
using Platform.Client.Services.Services;
using Platform.Framework.Core;
using System.Security.Cryptography;
using System.Text;
using System.Timers;

namespace Platform.Razor.Features.Chat.Messages
{
    public partial class MessagesListing : IDisposable
    {
        [Parameter] public string ConversationId { get; set; } = string.Empty;

        [Inject] ISecureKeyManager secureKeyManager { get; set; } = null!; 

        private ElementReference? messagesDiv;
        private System.Timers.Timer? _searchTimer;
        private const int SearchDelayMs = 500;
        private bool showSearch = false;

        [Parameter, SupplyParameterFromQuery]
        public string? ChatTitle { get; set; }

        [Parameter, SupplyParameterFromQuery]
        public string? Tagline { get; set; }

        [Parameter, SupplyParameterFromQuery]
        public string? ChatIcon { get; set; }
 
        // Image viewer state
        private bool showImageViewer = false;
        private List<MessageAttachmentInfo>? currentImageAttachments;
        private int currentImageIndex = 0;

        // Scroll indicator state
        private bool showScrollIndicator = false;
        private string scrollIndicatorText = "";
        private DateTime? currentScrollDate = null;
        private System.Timers.Timer? scrollIndicatorTimer;

        RSA rsaKey = null!;

        protected override async Task OnInitializedAsync()
        {
            // Set the conversation ID in the filter
            FilterViewModel.ConversationId = ConversationId;

            // Initialize the search timer for debounced search
            _searchTimer = new System.Timers.Timer(SearchDelayMs);
            _searchTimer.Elapsed += OnSearchTimerElapsed;
            _searchTimer.AutoReset = false;
            rsaKey = secureKeyManager.GetRSAPrivateKeyAsync();
            PubSub.Hub.Default.Subscribe<string>((m) =>
            {
                if (m == "NewMessageReceived")
                {
                    _ = LoadItems(false);
                }
            }); 
            await base.OnInitializedAsync();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            await base.OnAfterRenderAsync(firstRender);
            if (messagesDiv != null && JsRuntime != null)
            {
                try
                {
                    await JsRuntime.InvokeVoidAsync("initStickyBadges", messagesDiv);

                    // Initialize WhatsApp-style scroll behavior
                    await JsRuntime.InvokeVoidAsync("initWhatsAppScroll", messagesDiv, firstRender);
                }
                catch (Exception ex)
                {
                    Logger.LogWarning($"Error initializing chat scroll: {ex.Message}");
                }
            }
        }

        protected override List<ChatMessagesListingViewModel> ConvertToListViewModel(List<ChatMessagesListingBusinessObject> list)
        {
            var orderedItems = new List<ChatMessagesListingViewModel>();
            foreach (var item in list.DistinctBy(x=>x.Id).OrderByDescending(x=>x.Timestamp))
            {
                orderedItems.Add(new ChatMessagesListingViewModel
                {
                    Id = item.Id,
                    Content = DecryptMessageContent(item.Content),
                    IsIncoming = item.IsIncoming,
                    Timestamp = item.Timestamp,
                    DeliveryStatus = item.DeliveryStatus,
                    Attachments = item.Attachments
                });
            }
            return orderedItems;
        }

        /// <summary>
        /// Decrypts message content using RSA private key from secure memory
        /// </summary>
        private string DecryptMessageContent(string? encryptedContent)
        {
            if (string.IsNullOrEmpty(encryptedContent))
                return string.Empty;

            try
            {
                // Check if RSA key is available in memory
                if (!secureKeyManager.IsRSAKeyAvailable())
                {
                    // If no key available, return placeholder indicating authentication needed
                    return "[Authentication required to decrypt message]";
                }

                // Get RSA private key from secure memory

                // Decrypt the message content
                var encryptedBytes = Convert.FromBase64String(encryptedContent);
                var decryptedBytes = rsaKey.Decrypt(encryptedBytes, RSAEncryptionPadding.OaepSHA256);
                return Encoding.UTF8.GetString(decryptedBytes);

            }
            catch (Exception ex)
            {
                // Log the error (in production, use proper logging)
                System.Diagnostics.Debug.WriteLine($"Failed to decrypt message: {ex.Message}");
                return "[Failed to decrypt message]";
            }
        }

        /// <summary>
        /// Handles search input with debouncing
        /// </summary>
        private void OnSearchKeyUp()
        {
            _searchTimer?.Stop();
            _searchTimer?.Start();
        }

        /// <summary>
        /// Timer elapsed event for debounced search
        /// </summary>
        private async void OnSearchTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            await InvokeAsync(async () =>
            {
                await LoadItems();
                StateHasChanged();
            });
        }

        /// <summary>
        /// Toggles the search bar visibility
        /// </summary>
        private void ToggleSearch()
        {
            showSearch = !showSearch;
            if (!showSearch)
            {
                FilterViewModel.SearchText = string.Empty;
                _ = LoadItems();
            }
        }

        /// <summary>
        /// Clears the search text and refreshes messages
        /// </summary>
        private async Task ClearSearch()
        {
            FilterViewModel.SearchText = string.Empty;
            await LoadItems();
        }

        /// <summary>
        /// Navigates back to chat threads list
        /// </summary>
        private void GoBack()
        {
            Navigation.NavigateTo("/chat");
        }
 
        /// <summary>
        /// Gets the initials from a name for avatar display
        /// </summary>
        private string GetInitials(string? name)
        {
            if (string.IsNullOrEmpty(name))
                return "?";

            var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0)
                return "?";

            if (parts.Length == 1)
                return parts[0].Substring(0, Math.Min(2, parts[0].Length)).ToUpper();

            return $"{parts[0][0]}{parts[^1][0]}".ToUpper();
        }

        /// <summary>
        /// Smart date formatting for message timestamps
        /// </summary>
        private string GetFormattedTime(DateTime? timestamp)
        {
            if (!timestamp.HasValue)
                return "";

            var now = DateTime.UtcNow;
            var messageTime = timestamp.Value;
            var timeSpan = now - messageTime;

            // TODAY: Show relative time format
            if (messageTime.Date == now.Date)
            {
                if (timeSpan.TotalMinutes < 1)
                    return "Just now";
                if (timeSpan.TotalMinutes < 60)
                    return $"{(int)timeSpan.TotalMinutes} minutes ago";
                if (timeSpan.TotalHours < 24)
                    return $"{(int)timeSpan.TotalHours} hours ago";

                return messageTime.ToString("HH:mm");
            }

            // YESTERDAY: Show "Yesterday" without time
            if (messageTime.Date == now.Date.AddDays(-1))
                return "Yesterday";

            // PREVIOUS DATES: Show actual date without time
            if (messageTime.Year == now.Year)
                return messageTime.ToString("MMM dd, yyyy");

            return messageTime.ToString("MMM dd, yyyy");
        }

        /// <summary>
        /// Formats date for separators
        /// </summary>
        private string GetDateSeparatorText(DateTime date)
        {
            var now = DateTime.UtcNow;

            if (date.Date == now.Date)
                return "Today";

            if (date.Date == now.Date.AddDays(-1))
                return "Yesterday";

            if (date.Year == now.Year)
                return date.ToString("MMMM dd");

            return date.ToString("MMMM dd, yyyy");
        }

        /// <summary>
        /// Checks if a date separator should be shown between two messages
        /// </summary>
        private bool ShouldShowDateSeparator(DateTime? currentMessageDate, DateTime? previousMessageDate)
        {
            if (!currentMessageDate.HasValue || !previousMessageDate.HasValue)
                return true;

            return currentMessageDate.Value.Date != previousMessageDate.Value.Date;
        }

      
       
        /// <summary>
        /// Helper class for participant information
        /// </summary>
        private class ParticipantInfo
        {
            public string Name { get; set; } = string.Empty;
            public string? Avatar { get; set; }
            public string UserId { get; set; } = string.Empty;
        }

        /// <summary>
        /// Gets the scroll indicator date text
        /// </summary>
        private string GetScrollIndicatorText(DateTime date)
        {
            var now = DateTime.UtcNow;

            if (date.Date == now.Date)
                return "Today";

            if (date.Date == now.Date.AddDays(-1))
                return "Yesterday";

            return date.ToString("MMM dd, yyyy");
        }

        int scrollSkip = 0;

        /// <summary>
        /// Handles scroll events for both date indicator and pagination
        /// </summary>
        private async Task OnScroll()
        {
            try
            {
                // Handle date indicator
                if (Items?.Any() == true)
                {
                    var firstVisibleMessage = Items.FirstOrDefault();
                    if (firstVisibleMessage?.Timestamp.HasValue == true)
                    {
                        var messageDate = firstVisibleMessage.Timestamp.Value;
                        var newScrollDate = messageDate.Date;

                        // Only update if the date has changed
                        if (currentScrollDate != newScrollDate)
                        {
                            currentScrollDate = newScrollDate;
                            scrollIndicatorText = GetScrollIndicatorText(messageDate);
                            showScrollIndicator = true;

                            // Reset the timer
                            scrollIndicatorTimer?.Stop();
                            scrollIndicatorTimer?.Dispose();

                            scrollIndicatorTimer = new System.Timers.Timer(2000); // Hide after 2 seconds
                            scrollIndicatorTimer.Elapsed += async (sender, e) =>
                            {
                                showScrollIndicator = false;
                                await InvokeAsync(StateHasChanged);
                                scrollIndicatorTimer?.Dispose();
                            };
                            scrollIndicatorTimer.Start();

                            await InvokeAsync(StateHasChanged);
                        }
                    }
                }

                // Handle pagination
                if (++scrollSkip > 10)
                {
                    if (JsRuntime != null && messagesDiv != null)
                    {
                        // Check if user scrolled to the top (which loads older messages in reverse mode)
                        var scrollPosition = await JsRuntime.InvokeAsync<double>("isScrolledToTop", messagesDiv);
                        if (scrollPosition < 100 && !IsWorking && TotalRecords > Items.Count)
                        {
                            // Load older messages when scrolled to top
                            await LoadOlderMessages();
                        }
                    }
                    scrollSkip = 0;
                }
            }
            catch (Exception ex)
            {
                Logger.LogWarning($"Error handling scroll event: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads older messages for pagination in WhatsApp-style reverse scroll
        /// </summary>
        private async Task LoadOlderMessages()
        {
            try
            {
                // Store current scroll position to maintain it after loading
                var scrollHeight = await JsRuntime!.InvokeAsync<double>("getScrollHeight", messagesDiv);

                // Load previous page of messages
                PaginationStrip.RowsPerPage += 15;
                FilterViewModel.RowsPerPage += 15;
                await LoadItems(true);

                // Restore scroll position to prevent jumping
                //await JsRuntime.InvokeVoidAsync("maintainScrollPosition", messagesDiv, scrollHeight);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading older messages");
            }
        }

        private async Task OnMessageSent()
        {
            // Reload messages and maintain bottom position for new messages
            await LoadItems(false);

            // Ensure we stay at the bottom for new messages in reverse scroll mode
            if (JsRuntime != null && messagesDiv != null)
            {
                try
                {
                    await JsRuntime.InvokeVoidAsync("scrollToBottomWhatsApp", messagesDiv);
                }
                catch (Exception ex)
                {
                    Logger.LogWarning($"Error scrolling to bottom after message sent: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Handles retry operations for error boundary
        /// </summary>
        private async Task HandleRetry()
        {
            try
            {
                Logger.LogInformation("Retrying MessagesListing component for conversation: {ConversationId}", ConversationId);

                // Reset component state
                IsWorking = false;
                Error = null;

                // Reload messages
                await LoadItems(true);

                // Reinitialize scroll behavior
                if (JsRuntime != null && messagesDiv != null)
                {
                    await JsRuntime.InvokeVoidAsync("initWhatsAppScroll", messagesDiv, true);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error during MessagesListing retry for conversation: {ConversationId}", ConversationId);
                Error = "Failed to reload messages. Please try again.";
            }
        }

        /// <summary>
        /// Checks if a message has image attachments
        /// </summary>
        private bool HasImageAttachments(ChatMessagesListingViewModel message)
        {
            return message.Attachments?.Any(a => a.AttachmentType == "Image") == true;
        }

        /// <summary>
        /// Shows full-screen image viewer
        /// </summary>
        private async Task ShowFullImage(MessageAttachmentInfo attachment)
        {
            // Find the message containing this attachment
            var message = Items?.FirstOrDefault(m => m.Attachments?.Contains(attachment) == true);
            if (message?.Attachments != null)
            {
                // Get all image attachments from this message
                currentImageAttachments = message.Attachments
                    .Where(a => a.AttachmentType == "Image")
                    .ToList();

                // Find the index of the clicked attachment
                currentImageIndex = currentImageAttachments.IndexOf(attachment);
                if (currentImageIndex < 0) currentImageIndex = 0;

                // Show the image viewer
                showImageViewer = true;
                StateHasChanged();
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Closes the full-screen image viewer
        /// </summary>
        private async Task CloseImageViewer()
        {
            showImageViewer = false;
            currentImageAttachments = null;
            currentImageIndex = 0;
            StateHasChanged();
            await Task.CompletedTask;
        }

        void IDisposable.Dispose()
        {
            _searchTimer.Stop();
            _searchTimer.Dispose();
        }
    }
}
