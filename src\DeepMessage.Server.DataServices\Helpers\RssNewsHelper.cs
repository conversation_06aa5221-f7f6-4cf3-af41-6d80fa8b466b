﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace DeepMessage.Server.DataServices.Helpers
{
    public class RssNewsHelper
    {
        private const string FeedUrl = "https://feeds.bbci.co.uk/news/rss.xml";

        public NewsItemViewModel[]? News { get; set; }

        public void Start()
        {
            Task.Factory.StartNew(async () =>
            {
                while (true)
                {
                    News = await GetNewsAsync();
                    await Task.Delay(TimeSpan.FromMinutes(30)); // Fetch every 30 minutes
                }
                ;
            });
        }


        public async Task<NewsItemViewModel[]> GetNewsAsync()
        {
            try
            {
                var client = new HttpClient();
                var response = await client.GetStringAsync(FeedUrl);
                var doc = XDocument.Parse(response);

                var items = doc.Descendants("item").Select(item => new NewsItemViewModel
                {
                    Title = item.Element("title")?.Value,
                    Description = item.Element("description")?.Value,
                    Link = item.Element("link")?.Value,
                    PubDate = DateTime.TryParse(item.Element("pubDate")?.Value, out var date) ? date : DateTime.MinValue
                }).ToArray();

                return items;
            }
            catch
            {
                return Array.Empty<NewsItemViewModel>();
            }
        }
    }


    public class NewsItemViewModel
    {
        public string? Title { get; set; } = null!;
        public string? Description { get; set; } = null!;
        public string? Link { get; set; } = null!;
        public DateTime PubDate { get; set; }
    }

}
