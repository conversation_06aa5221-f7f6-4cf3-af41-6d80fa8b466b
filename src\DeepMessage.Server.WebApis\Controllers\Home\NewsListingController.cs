﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Home;
namespace DeepMessage.Server.WebApis.Controller.Home;
[ApiController, Route("api/[controller]/[action]")]
public class NewsListingController : ControllerBase, INewsListingDataService
{

	private readonly INewsListingDataService dataService;

	public NewsListingController(INewsListingDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<PagedDataList<NewsListingBusinessObject>> GetPaginatedItems([FromQuery] NewsFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
