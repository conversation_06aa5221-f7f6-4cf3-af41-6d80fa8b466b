﻿using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Conversation;
using Platform.Client.Services.Features.Conversation.ChatThreads.Form;
namespace Platform.Client.Common.Features.Conversation;
public class StartChatFormViewBase : FormBaseMaui<StartChatFormBusinessObject, StartChatFormViewModel, string, IStartChatFormDataService>
{
    public StartChatFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }
}

public partial class StartChatFormView : StartChatFormViewBase
{
    public StartChatFormView(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
        InitializeComponent();
        BindingContext = this;
    }
}
