﻿using Android.App;
using Android.Content;
using Android.Util;
using CommunityToolkit.Mvvm.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DeepMessage.MauiApp.Platforms.Android.Source;

[BroadcastReceiver(Enabled = true, Exported = false)]
[IntentFilter(new[] { "com.companyname.deepmessage.UPDATE" })]
public class UpdateBroadcastReceiver : BroadcastReceiver
{
    public override void OnReceive(Context? context, Intent? intent)
    {
        if (intent.Action == "com.companyname.deepmessage.UPDATE")
        {
            var message = intent.GetStringExtra("message");
            Log.Debug("DeepMessage", $"Broadcast received with message: {message}");

            if (message == "Logout")
            {
                WeakReferenceMessenger.Default.Send("Logout");
            }
        }
    }
}
