﻿using Android.Content;
using CommunityToolkit.Mvvm.Messaging;
using DeepMessage.MauiApp.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DeepMessage.MauiApp.Platforms.Android.Source
{
    public class UpdateReceiver : BroadcastReceiver
    {
        public override void OnReceive(Context? context, Intent? intent)
        {
            // Extract data and update UI (remember to dispatch to the main thread if needed).
            var message = intent.GetStringExtra("message");
            WeakReferenceMessenger.Default.Send(new ChatSyncItem() { SyncType = 1 });
        }
    }
}
