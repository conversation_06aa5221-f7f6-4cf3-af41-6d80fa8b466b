﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using System.ComponentModel.DataAnnotations;
namespace DeepMessage.ServiceContracts.Features.Conversation;
public class ChatThreadSyncFormBusinessObject
{
    [StringLength(450)]
    public string Id { get; set; } = null!;

    public string Title { get; set; } = null!;
    public ConversationType Type { get; set; }

    public bool IsDeleted { get; set; } = false;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public IEnumerable<ChatParticipentsSyncFormBusinessObject>? ChatParticipents { get; set; }
}
public class ChatParticipentsSyncFormBusinessObject
{
    [StringLength(450)]
    public string Id { get; set; } = null!;

    [StringLength(450)]
    public string ConversationId { get; set; } = null!;
     

    [StringLength(450)]
    public string UserId { get; set; } = null!;

    public bool IsAdmin { get; set; } = false;

    public DateTime JoinedAt { get; set; } = DateTime.UtcNow;
     
}