<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="600" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Ultra-subtle gradient for sleek minimal design -->
    <linearGradient id="sleekGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1"/>
      <stop offset="50%" style="stop-color:#fefefe;stop-opacity:1"/>
      <stop offset="100%" style="stop-color:#fdfdfd;stop-opacity:1"/>
    </linearGradient>
    
    <!-- Barely visible texture pattern -->
    <pattern id="sleekTexture" x="0" y="0" width="120" height="120" patternUnits="userSpaceOnUse">
      <circle cx="60" cy="60" r="0.3" fill="#f8f8f8" opacity="0.4"/>
      <circle cx="30" cy="30" r="0.2" fill="#f5f5f5" opacity="0.3"/>
      <circle cx="90" cy="90" r="0.2" fill="#f5f5f5" opacity="0.3"/>
    </pattern>
    
    <!-- Whisper-soft accent -->
    <radialGradient id="whisperAccent" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#f9f9f9;stop-opacity:0.2"/>
      <stop offset="100%" style="stop-color:#f5f5f5;stop-opacity:0.1"/>
    </radialGradient>
  </defs>
  
  <!-- Pure minimal base -->
  <rect width="400" height="600" fill="url(#sleekGradient)"/>
  
  <!-- Ultra-subtle accent shapes -->
  <circle cx="200" cy="300" r="150" fill="url(#whisperAccent)" opacity="0.3"/>
  <circle cx="100" cy="150" r="80" fill="url(#whisperAccent)" opacity="0.2"/>
  <circle cx="320" cy="450" r="100" fill="url(#whisperAccent)" opacity="0.25"/>
  
  <!-- Barely-there texture overlay -->
  <rect width="400" height="600" fill="url(#sleekTexture)" opacity="0.6"/>
  
  <!-- Minimal geometric hints -->
  <path d="M0,200 Q200,195 400,200" stroke="#f8f8f8" stroke-width="0.5" fill="none" opacity="0.4"/>
  <path d="M0,400 Q200,395 400,400" stroke="#f6f6f6" stroke-width="0.3" fill="none" opacity="0.3"/>
  
  <!-- Clean corner elements -->
  <rect x="5" y="5" width="0.5" height="15" fill="#f0f0f0" opacity="0.3"/>
  <rect x="394.5" y="580" width="0.5" height="15" fill="#f0f0f0" opacity="0.3"/>
</svg>
