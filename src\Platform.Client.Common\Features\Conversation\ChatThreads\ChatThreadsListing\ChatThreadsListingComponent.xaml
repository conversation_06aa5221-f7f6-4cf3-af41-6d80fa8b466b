﻿<?xml version="1.0" encoding="utf-8" ?>
<local:ChatThreadsListingViewBase
    x:Class="Platform.Client.Common.Features.Conversation.ChatThreadsListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:chat="clr-namespace:Platform.Client.Services.Features.Conversation;assembly=Platform.Client.Services"
    xmlns:controls="clr-namespace:Platform.Client.Common.Controls"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Conversation"
    Title="Chats"
    x:DataType="local:ChatThreadsListingView"
    BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                      Dark={StaticResource Gray800}}"
    Shell.BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                            Dark={StaticResource Gray800}}"
    Shell.TitleColor="{AppThemeBinding Light={StaticResource Gray800},
                                       Dark={StaticResource White}}">

    <ContentPage.ToolbarItems>
        <ToolbarItem Command="{Binding SyncDownItemsCommand}">
            <ToolbarItem.IconImageSource>
                <FontImageSource
                    FontFamily="Jelly"
                    Glyph="&#xf021;"
                    Size="20"
                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                            Dark={StaticResource Gray300}}" />
            </ToolbarItem.IconImageSource>
        </ToolbarItem>
        <ToolbarItem>
            <ToolbarItem.IconImageSource>
                <FontImageSource
                    FontFamily="Jelly"
                    Glyph="&#xf0c9;"
                    Size="20"
                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                            Dark={StaticResource Gray300}}" />
            </ToolbarItem.IconImageSource>
        </ToolbarItem>
    </ContentPage.ToolbarItems>

    <Grid RowDefinitions="Auto,8, *" VerticalOptions="Fill">

        <Border
            Margin="8,0,8,0"
            Background="{AppThemeBinding Light={StaticResource Gray50},
                                         Dark={StaticResource Gray700}}"
            Stroke="{AppThemeBinding Light={StaticResource Gray100},
                                     Dark={StaticResource Gray500}}">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="45" />
            </Border.StrokeShape>
            <Grid>
                <Image
                    Margin="16,0"
                    HorizontalOptions="End"
                    MaximumHeightRequest="32">
                    <Image.Source>
                        <FontImageSource
                            FontFamily="Jelly"
                            Glyph="&#xf002;"
                            Size="20"
                            Color="{AppThemeBinding Light={StaticResource Gray700},
                                                    Dark={StaticResource Gray300}}" />
                    </Image.Source>
                </Image>
                <controls:BorderlessEntry
                    Grid.Column="0"
                    Margin="12,0"
                    FontSize="16"
                    HorizontalOptions="Fill"
                    Placeholder="Search messages..." />


            </Grid>
        </Border>


        <CollectionView
            Grid.Row="2"
            BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                              Dark=Black}"
            ItemsSource="{Binding Items, Mode=OneWay}"
            ItemsUpdatingScrollMode="KeepLastItemInView"
            VerticalOptions="Fill">
            <CollectionView.ItemTemplate>
                <DataTemplate x:DataType="chat:ChatThreadsListingViewModel">
                    <Grid Padding="12" ColumnSpacing="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type local:ChatThreadsListingView}}, Path=BindingContext.MessageTappedCommand}" CommandParameter="{Binding .}" />
                        </Grid.GestureRecognizers>
                        <Border
                            Grid.Column="0"
                            BackgroundColor="{AppThemeBinding Light={StaticResource Gray200},
                                                              Dark={StaticResource Gray600}}"
                            HeightRequest="50"
                            Stroke="{AppThemeBinding Light={StaticResource Gray400},
                                                     Dark={StaticResource Gray500}}"
                            WidthRequest="50">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="25" />
                            </Border.StrokeShape>
                            <Grid>
                                <Image
                                    Aspect="AspectFill"
                                    IsVisible="{Binding Avatar, Converter={StaticResource StringToBoolConverter}}"
                                    Source="{Binding Avatar}" />
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="18"
                                    HorizontalOptions="Center"
                                    IsVisible="{Binding Avatar, Converter={StaticResource InverseStringToBoolConverter}}"
                                    Text="{Binding Name, Converter={StaticResource InitialsConverter}}"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray200}}"
                                    VerticalOptions="Center" />
                            </Grid>
                        </Border>
                        <StackLayout Grid.Column="1" Spacing="2">
                            <Grid>
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="16"
                                    Text="{Binding Name}"
                                    TextColor="{AppThemeBinding Light=Black,
                                                                Dark=White}" />

                                <Label
                                    FontSize="12"
                                    HorizontalOptions="End"
                                    Text="{Binding LastMessageTimeString}"
                                    TextColor="{AppThemeBinding Light=Gray,
                                                                Dark=LightGray}" />

                            </Grid>
                            <Label
                                FontSize="15"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Text="{Binding LastMessage}"
                                TextColor="{AppThemeBinding Light=Gray,
                                                            Dark=LightGray}" />
                        </StackLayout>

                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>

        <Border
            Grid.Row="2"
            Stroke="{AppThemeBinding Light={StaticResource Gray100},
                                     Dark={StaticResource Gray800}}"
            StrokeThickness="0.5"
            VerticalOptions="End" />
    </Grid>

</local:ChatThreadsListingViewBase>
