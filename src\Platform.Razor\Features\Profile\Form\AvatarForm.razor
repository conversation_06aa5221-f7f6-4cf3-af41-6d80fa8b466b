﻿@inherits FormBase<AvatarFormBusinessObject,AvatarFormViewModel, string, IAvatarFormDataService>
@using DeepMessage.ServiceContracts.Features.Profile
@using Platform.Client.Services.Features.Profile
@using Platform.Framework.Core
@using System.Text.Json

<div class="bg-white p-6 max-w-lg mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-2">Generate Avatar</h2>
        <p class="text-gray-600 text-sm">Describe your ideal avatar and we'll create it for you using AI</p>
    </div>

    <!-- Error State -->
    @if (!string.IsNullOrEmpty(Error))
    {
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-red-800 text-sm font-medium">Error</span>
            </div>
            <p class="text-red-700 text-sm mt-1">@Error</p>
        </div>
    }

    <!-- Success State -->
    @if (!string.IsNullOrEmpty(SelectedItem?.SuccessMessage))
    {
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-green-800 text-sm font-medium">Success!</span>
            </div>
            <p class="text-green-700 text-sm mt-1">@SelectedItem.SuccessMessage</p>
        </div>
    }

    <!-- Generated Avatar Preview -->
    @if (SelectedItem?.HasGeneratedAvatar == true && SelectedItem?.GeneratedAvatarData != null)
    {
        <div class="mb-6 text-center">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Generated Avatar</h3>
            <div class="inline-block">
                <img src="@GetAvatarDataUrl()" alt="Generated avatar" class="w-32 h-32 rounded-full object-cover border-4 border-gray-200" />
            </div>
        </div>
    }
    <EditForm Model="SelectedItem">
        <div class="space-y-4 mb-6">
            <div>
                <label for="avatar-description" class="block text-sm font-medium text-gray-900 mb-2">
                    Describe Your Avatar
                </label>
                <InputTextArea id="avatar-description"
                               @bind-Value="SelectedItem.AvatarDescription"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-900 focus:border-transparent resize-none text-gray-900 placeholder-gray-500"
                               placeholder="e.g., A balled guy with glasses and a parrot on shoulder..."
                               rows="2"
                               disabled="@(SelectedItem?.IsGenerating == true || SelectedItem?.IsSaving == true)" />
                <ValidationMessage For="@(() => SelectedItem.AvatarDescription)" class="text-red-600 text-sm mt-1" />
            </div>
             
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col space-y-3">
            <!-- Generate Button -->
            @if (!SelectedItem?.HasGeneratedAvatar == true)
            {
                <button type="button"
                        @onclick="GenerateAvatar"
                        class="w-full bg-gray-900 text-white py-3 px-4 rounded-lg font-medium hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-900 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors min-h-[44px]"
                        disabled="@(SelectedItem?.IsGenerating == true || string.IsNullOrWhiteSpace(SelectedItem?.AvatarDescription))">
                    @if (SelectedItem?.IsGenerating == true)
                    {
                        <span class="flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Generating Avatar...
                        </span>
                    }
                    else
                    {
                        <span>Generate Avatar</span>
                    }
                </button>
            }

            <!-- Save Button (shown after generation) -->
            @if (SelectedItem?.HasGeneratedAvatar == true)
            {
                <button type="button"
                        @onclick="SaveAvatar"
                        class="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors min-h-[44px]"
                        disabled="@(SelectedItem?.IsSaving == true)">
                    @if (SelectedItem?.IsSaving == true)
                    {
                        <span class="flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Saving Avatar...
                        </span>
                    }
                    else
                    {
                        <span>Save Avatar</span>
                    }
                </button>

                <!-- Generate New Button -->
                <button type="button"
                        @onclick="GenerateNewAvatar"
                        class="w-full bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors min-h-[44px]"
                        disabled="@(SelectedItem?.IsGenerating == true || SelectedItem?.IsSaving == true)">
                    Generate New Avatar
                </button>
            }
        </div>
    </EditForm>
</div>