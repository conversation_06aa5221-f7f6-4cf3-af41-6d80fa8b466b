namespace DeepMessage.ServiceContracts.Features.Conversation;

/// <summary>
/// Result of media capture operation from camera or gallery
/// </summary>
public class MediaCaptureResult
{
    /// <summary>
    /// Original filename or generated filename
    /// </summary>
    public string FileName { get; set; } = null!;
    
    /// <summary>
    /// Original uncompressed image data
    /// </summary>
    public byte[] OriginalData { get; set; } = null!;
    
    /// <summary>
    /// Compressed image data for upload
    /// </summary>
    public byte[] CompressedData { get; set; } = null!;
    
    /// <summary>
    /// Thumbnail image data for quick preview
    /// </summary>
    public byte[] ThumbnailData { get; set; } = null!;
    
    /// <summary>
    /// MIME type of the image
    /// </summary>
    public string MimeType { get; set; } = null!;
    
    /// <summary>
    /// Size of original image data in bytes
    /// </summary>
    public long OriginalSizeBytes { get; set; }
    
    /// <summary>
    /// Size of compressed image data in bytes
    /// </summary>
    public long CompressedSizeBytes { get; set; }
    
    /// <summary>
    /// Original image width in pixels
    /// </summary>
    public int OriginalWidth { get; set; }
    
    /// <summary>
    /// Original image height in pixels
    /// </summary>
    public int OriginalHeight { get; set; }

    /// <summary>
    /// Upload progress percentage (0-100)
    /// </summary>
    public int UploadProgress { get; set; } = 0;

    /// <summary>
    /// Whether the file is currently being uploaded
    /// </summary>
    public bool IsUploading { get; set; } = false;

    /// <summary>
    /// Upload error message if upload failed
    /// </summary>
    public string? UploadError { get; set; }
}
