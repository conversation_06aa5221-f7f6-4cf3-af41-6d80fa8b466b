﻿using Microsoft.Maui.Platform;
#if ANDROID
using Android.Content.Res;
using AndroidX.Core.Content;
#endif

namespace ModelFury.Briefly.HybridApp
{
    public partial class MainPage : ContentPage
    {
        public MainPage()
        {
            InitializeComponent();
            ConfigurePage();
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            // Refresh status bar when page appears (handles theme changes)
            SetThemeAwareStatusBar();
        }

        
        private void ConfigurePage()
        {
            // Ensure no navigation bar or title bar is visible
            NavigationPage.SetHasNavigationBar(this, false);
            NavigationPage.SetTitleView(this, null);
            Shell.SetNavBarIsVisible(this, false);
            Shell.SetTabBarIsVisible(this, false);

            // Set theme-aware status bar colors
            SetThemeAwareStatusBar();

            // Configure page background to match Nothing Phone theme
            //BackgroundColor = Colors.White;
        }

        void SetThemeAwareStatusBar()
        {
#if ANDROID
            var window = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.Window;
            var context = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity;

            if (window != null && context != null)
            {
                window.ClearFlags(Android.Views.WindowManagerFlags.TranslucentStatus);
                window.AddFlags(Android.Views.WindowManagerFlags.DrawsSystemBarBackgrounds);

                // Check if dark mode is enabled
                var isDarkMode = (context.Resources?.Configuration?.UiMode & UiMode.NightMask) == UiMode.NightYes;

                if (isDarkMode)
                {
                    // Dark theme: Dark gray status bar with white text/icons
                    var darkColor = Android.Graphics.Color.ParseColor("#2A2A2A");
                    window.SetStatusBarColor(darkColor);
                    window.SetNavigationBarColor(darkColor);

                    // Set status bar icons to light (white) for dark background
                    var decorView = window.DecorView;
                    var flags = decorView.SystemUiVisibility;
                    flags &= ~Android.Views.SystemUiFlags.LightStatusBar;
                    flags &= ~Android.Views.SystemUiFlags.LightNavigationBar;
                    decorView.SystemUiVisibility = flags;
                }
                else
                {
                    // Light theme: White status bar with black text/icons
                    var lightColor = Android.Graphics.Color.ParseColor("#FFFFFF");
                    window.SetStatusBarColor(lightColor);
                    window.SetNavigationBarColor(lightColor);

                    // Set status bar icons to dark (black) for light background
                    var decorView = window.DecorView;
                    var flags = decorView.SystemUiVisibility;
                    flags |= Android.Views.SystemUiFlags.LightStatusBar;
                    flags |= Android.Views.SystemUiFlags.LightNavigationBar;
                    decorView.SystemUiVisibility = flags;
                }
            }
#elif IOS
            // iOS status bar configuration is handled in AppDelegate
            // But we can also configure it here if needed
            if (UIKit.UIApplication.SharedApplication != null)
            {
                var isDarkMode = UIKit.UITraitCollection.CurrentTraitCollection.UserInterfaceStyle == UIKit.UIUserInterfaceStyle.Dark;

                if (isDarkMode)
                {
                    UIKit.UIApplication.SharedApplication.SetStatusBarStyle(UIKit.UIStatusBarStyle.LightContent, false);
                }
                else
                {
                    UIKit.UIApplication.SharedApplication.SetStatusBarStyle(UIKit.UIStatusBarStyle.DarkContent, false);
                }
            }
#endif
        }

        void SetStatusBarColor(Color color)
        {
#if ANDROID
            var window = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.Window;

            if (window != null)
            {
                window.ClearFlags(Android.Views.WindowManagerFlags.TranslucentStatus);
                window.AddFlags(Android.Views.WindowManagerFlags.DrawsSystemBarBackgrounds);
                window.SetStatusBarColor(color.ToPlatform());

                // Also set navigation bar color to match
                window.SetNavigationBarColor(color.ToPlatform());
            }
#elif IOS
            // iOS status bar configuration is handled in AppDelegate
            // But we can also configure it here if needed
            if (UIKit.UIApplication.SharedApplication != null)
            {
                UIKit.UIApplication.SharedApplication.SetStatusBarStyle(UIKit.UIStatusBarStyle.LightContent, false);
            }
#endif
        }

        /// <summary>
        /// Test method to verify theme-aware status bar implementation
        /// Call this method to check if the status bar colors are applied correctly
        /// </summary>
        public void TestStatusBarTheme()
        {
#if ANDROID
            var context = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity;
            if (context != null)
            {
                var isDarkMode = (context.Resources?.Configuration?.UiMode & UiMode.NightMask) == UiMode.NightYes;
                System.Diagnostics.Debug.WriteLine($"[StatusBar Test] Dark mode detected: {isDarkMode}");
                System.Diagnostics.Debug.WriteLine($"[StatusBar Test] Expected status bar color: {(isDarkMode ? "#2A2A2A" : "#FFFFFF")}");
                System.Diagnostics.Debug.WriteLine($"[StatusBar Test] Expected icon color: {(isDarkMode ? "White" : "Black")}");

                // Refresh status bar to ensure current theme is applied
                SetThemeAwareStatusBar();
                System.Diagnostics.Debug.WriteLine("[StatusBar Test] Status bar refreshed with current theme");
            }
#endif
        }
    }
}
