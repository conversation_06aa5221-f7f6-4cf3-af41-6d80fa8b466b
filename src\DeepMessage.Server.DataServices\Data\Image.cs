using System.ComponentModel.DataAnnotations;

namespace DeepMessage.Server.DataServices.Data
{
    public class Image
    {
        [Key]
        [StringLength(450)]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Base64 encoded image data
        /// </summary>
        public string ImageContent { get; set; } = string.Empty;

        /// <summary>
        /// Type of image: "avatar", "general", etc.
        /// </summary>
        [StringLength(50)]
        public string ImageType { get; set; } = string.Empty;

        /// <summary>
        /// When the image was created/uploaded
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
