using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using System.Text.Json;

namespace Platform.Razor.Features.Friends.Form
{
    public partial class FriendsForm
    {
  
        public override async Task OnAfterSaveAsync(string key)
        {
           
            var friend = JsonSerializer.Deserialize<FriendFormResponseDto>(key, JsonSerializerOptions.Web);
            await CheckAndPerformSync(friend!);

            // Show success message and navigate
            Navigation.NavigateTo("/friends");
            
            
            // Navigate back to friends list after successful save
            await base.OnAfterSaveAsync(key);
        }

        private async Task CheckAndPerformSync(FriendFormResponseDto friend)
        { 
                var userId = await StorageService.GetValue(ClaimTypes.NameIdentifier);
                var localScope = ScopeFactory.CreateScope();
                var context = localScope.ServiceProvider.GetRequiredService<AppDbContext>();
                var friendship = await context.Friendships.FirstOrDefaultAsync(x => x.Id == friend.Id);
                if (friendship == null)
                {
                    friendship = new Friendship()
                    {
                        Id = friend.Id,
                        UserId = userId,
                        FriendId = friend.FriendId,
                        CreatedAt = DateTime.Now,

                    };
                    context.Friendships.Add(friendship);
                }
                friendship.Pub1 = friend.Pub1;
                friendship.Name = friend.Name;
                await context.SaveChangesAsync();

 

        }
        /// <summary>
        /// Navigates back to the friends list
        /// </summary>
        private void GoBack()
        {
            Navigation.NavigateTo("/friends");
        }
         
    }
}
