﻿using ModelFury.Briefly.MobileApp.Features.Account;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
using Platform.Client.Services.Features.Account;
using Microsoft.Extensions.DependencyInjection;
using System.Windows.Input;
using System.Collections.ObjectModel;
using System.Security.Claims;
using Platform.Framework.Core;

namespace ModelFury.Briefly.MobileApp.Features.Account;

public class ProfileFormViewBase : FormBaseMaui<ProfileFormBusinessObject, ProfileFormViewModel, string, IProfileFormDataService>
{
    public ProfileFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }
}
 
public partial class ProfileFormView : ProfileFormViewBase
{ 
    // UI State Properties - Focus management removed, using platform defaults

    private bool hasError;
    public bool HasError
    {
        get => hasError;
        set
        {
            hasError = value;
            OnPropertyChanged();
        }
    }

 

    private ObservableCollection<string> avatarOptions;
    public ObservableCollection<string> AvatarOptions
    {
        get => avatarOptions;
        set
        {
            avatarOptions = value;
            OnPropertyChanged();
        }
    }

    // Commands
    public ICommand BackCommand { get; }

    //public override Color TitleBarColor => Color.FromArgb("#004f98");

    public ProfileFormView(IServiceScopeFactory scopeFactory, string id) : base(scopeFactory, id!)
    {
        InitializeComponent(); 

        avatarOptions = new ObservableCollection<string>();
        for (int i = 1; i <= 12; i++)
        {
            avatarOptions.Add($"/avatars/{i}.png");

        }
        BindingContext = this;

        BackCommand = new Command(async () => await GoBack());
         
    } 
 

    private void HandleError(string error)
    {
        HasError = !string.IsNullOrEmpty(error); 
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            await DisplayAlert("Error", error, "OK");
        });
    }

    public override async Task OnAfterSaveAsync(string key)
    {
        try
        { 
            using var scope = ScopeFactory.CreateScope();
            var profileFormDataService = scope.ServiceProvider.GetRequiredKeyedService<IProfileFormDataService>("client");
            await profileFormDataService.SaveAsync(ConvertViewModelToBusinessModel(SelectedItem));
            await GoBack();
        }
        catch (Exception ex)
        {
            HandleError($"Profile saved but sync failed: {ex.Message}");
        }
    }

    private async Task GoBack()
    {
        try
        {
            await Navigation.PopModalAsync(false);
        }
        catch (Exception ex)
        {
            HandleError($"Failed to navigate back: {ex.Message}");
        }
    }

  

    // Focus event handlers removed - using platform default focus behavior

    protected override void OnAppearing()
    {
        base.OnAppearing();
         
        HasError = false;
    }

    private void Button_Clicked(object sender, EventArgs e)
    {
        Navigation.PopModalAsync(false);
    }
}
