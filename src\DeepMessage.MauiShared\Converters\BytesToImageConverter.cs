﻿using SkiaSharp;
using System.Globalization;

namespace MobileApp.MauiShared.Converters
{
    public class BytesToImageConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            byte[]? imageData = value == null ? null : (byte[])value;
            if (value == null)
            {
                imageData = Array.Empty<byte>();
            }
            return imageData == null ? Array.Empty<byte>() : ImageSource.FromStream(() => new MemoryStream(imageData));
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class BytesToImageInverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            byte[]? imageData = value == null ? null : (byte[])value;
            if (value == null)
            {
                imageData = Array.Empty<byte>(); // Constants.EmptyImageData
            }

            // Step 1: Convert byte array to SKBitmap
            SKBitmap bitmap;
            using (var image = SKImage.FromEncodedData(imageData))
            {
                bitmap = SKBitmap.FromImage(image);
            }

            // Step 2: Manipulate the pixels
            // Example: Inverting the colors
            for (int y = 0; y < bitmap.Height; y++)
            {
                for (int x = 0; x < bitmap.Width; x++)
                {
                    var color = bitmap.GetPixel(x, y);
                    color = new SKColor(color.Red == 0 ? (byte)255 : (byte)31,
                        color.Red == 0 ? (byte)255 : (byte)83,
                        color.Red == 0 ? (byte)255 : (byte)19);
                    bitmap.SetPixel(x, y, color);
                }
            }
            using (var image = SKImage.FromBitmap(bitmap))
            using (var data = image.Encode(SKEncodedImageFormat.Png, 100))
            using (var stream = new MemoryStream())
            {
                data.SaveTo(stream);
                var modifiedImageData = stream.ToArray();
                return ImageSource.FromStream(() => new MemoryStream(modifiedImageData));
            }
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
