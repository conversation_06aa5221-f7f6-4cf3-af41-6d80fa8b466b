using System.Text.RegularExpressions;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;

namespace DeepMessage.Server.DataServices.Services
{
    public interface ISecurityService
    {
        string SanitizeInput(string input);
        bool ValidateMessageContent(string content);
        bool ValidateEmail(string email);
        bool ValidatePhoneNumber(string phoneNumber);
        string HashPassword(string password);
        bool VerifyPassword(string password, string hash);
        string GenerateSecureToken();
        bool IsValidUserId(string userId);
        bool IsValidConversationId(string conversationId);
    }

    public class SecurityService : ISecurityService
    {
        private readonly ILogger<SecurityService> _logger;
        private static readonly Regex EmailRegex = new(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", RegexOptions.Compiled);
        private static readonly Regex PhoneRegex = new(@"^\+?[1-9]\d{1,14}$", RegexOptions.Compiled);
        private static readonly Regex GuidRegex = new(@"^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$", RegexOptions.Compiled);

        public SecurityService(ILogger<SecurityService> logger)
        {
            _logger = logger;
        }

        public string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // Remove potentially dangerous characters and HTML encode
            var sanitized = System.Net.WebUtility.HtmlEncode(input.Trim());
            
            // Remove null bytes and control characters except newlines and tabs
            sanitized = Regex.Replace(sanitized, @"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "");
            
            return sanitized;
        }

        public bool ValidateMessageContent(string content)
        {
            if (string.IsNullOrWhiteSpace(content))
                return false;

            if (content.Length > 4000) // Maximum message length
                return false;

            // Check for suspicious patterns
            var suspiciousPatterns = new[]
            {
                @"<script[^>]*>.*?</script>",
                @"javascript:",
                @"vbscript:",
                @"onload\s*=",
                @"onerror\s*=",
                @"onclick\s*="
            };

            foreach (var pattern in suspiciousPatterns)
            {
                if (Regex.IsMatch(content, pattern, RegexOptions.IgnoreCase))
                {
                    _logger.LogWarning("Suspicious content detected in message: {Pattern}", pattern);
                    return false;
                }
            }

            return true;
        }

        public bool ValidateEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            return EmailRegex.IsMatch(email) && email.Length <= 254;
        }

        public bool ValidatePhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            return PhoneRegex.IsMatch(phoneNumber);
        }

        public string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                throw new ArgumentException("Password cannot be null or empty");

            using var rng = RandomNumberGenerator.Create();
            var salt = new byte[32];
            rng.GetBytes(salt);

            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 100000, HashAlgorithmName.SHA256);
            var hash = pbkdf2.GetBytes(32);

            var combined = new byte[64];
            Array.Copy(salt, 0, combined, 0, 32);
            Array.Copy(hash, 0, combined, 32, 32);

            return Convert.ToBase64String(combined);
        }

        public bool VerifyPassword(string password, string hash)
        {
            if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hash))
                return false;

            try
            {
                var combined = Convert.FromBase64String(hash);
                if (combined.Length != 64)
                    return false;

                var salt = new byte[32];
                var storedHash = new byte[32];
                Array.Copy(combined, 0, salt, 0, 32);
                Array.Copy(combined, 32, storedHash, 0, 32);

                using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 100000, HashAlgorithmName.SHA256);
                var computedHash = pbkdf2.GetBytes(32);

                return CryptographicOperations.FixedTimeEquals(storedHash, computedHash);
            }
            catch
            {
                return false;
            }
        }

        public string GenerateSecureToken()
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[32];
            rng.GetBytes(bytes);
            return Convert.ToBase64String(bytes);
        }

        public bool IsValidUserId(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return false;

            return GuidRegex.IsMatch(userId) || userId.Length <= 450; // Support both GUID and string IDs
        }

        public bool IsValidConversationId(string conversationId)
        {
            if (string.IsNullOrWhiteSpace(conversationId))
                return false;

            return GuidRegex.IsMatch(conversationId) || conversationId.Length <= 450;
        }
    }
}
