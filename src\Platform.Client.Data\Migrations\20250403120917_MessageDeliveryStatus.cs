﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DeepMessage.Client.Common.Migrations
{
    /// <inheritdoc />
    public partial class MessageDeliveryStatus : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<byte>(
                name: "DeliveryStatus",
                table: "Messages",
                type: "INTEGER",
                nullable: false,
                defaultValue: (byte)0);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeliveryStatusTime",
                table: "Messages",
                type: "TEXT",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DeliveryStatus",
                table: "Messages");

            migrationBuilder.DropColumn(
                name: "DeliveryStatusTime",
                table: "Messages");
        }
    }
}
