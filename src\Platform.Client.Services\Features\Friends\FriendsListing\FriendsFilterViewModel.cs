﻿using DeepMessage.Framework.Core;
namespace Platform.Client.Services.Features.Friends;
public class FriendsFilterViewModel :  BaseFilterViewModel
{
	private string _searchText = string.Empty;

	/// <summary>
	/// Search text for filtering friends by name
	/// </summary>
	public string SearchText
	{
		get => _searchText;
		set
		{
			if (SetField(ref _searchText, value))
			{
				// Update the base SearchKey property when SearchText changes
				SearchKey = value;
			}
		}
	}
}
