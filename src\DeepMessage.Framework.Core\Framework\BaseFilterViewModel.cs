﻿namespace DeepMessage.Framework.Core
{
    public class BaseFilterViewModel : ObservableBase
    {

        public int CurrentIndex { get; set; } = 1;

        public int RowsPerPage { get; set; } = 15;

        public bool UsePagination { get; set; } = true;


        

        private string? _searchKey;

        public string? SearchKey
        {
            get { return _searchKey; }
            set
            {
                //reset current index to 1 if text search is applied because user can apply search being on
                //any page index and results will not be displayed in that case if resultant pages are less
                //than current page index
                if (!string.IsNullOrWhiteSpace(value))
                    CurrentIndex = 1;

                SetField(ref _searchKey, value);

            }
        }

    }
}
