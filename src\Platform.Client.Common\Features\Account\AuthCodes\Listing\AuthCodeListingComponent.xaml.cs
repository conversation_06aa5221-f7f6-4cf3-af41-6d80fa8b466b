﻿using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.AuthCodes;
using Platform.Client.Common.Features.AuthCodes;
using Platform.Client.Services.Features.AuthCodes;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
namespace Platform.Client.Common.Features.AuthCodes;
public class AuthCodeListingViewBase : ListingBaseMaui<AuthCodeListingViewModel, AuthCodeListingBusinessObject,
                AuthCodeFilterViewModel, AuthCodeFilterBusinessObject, IAuthCodeListingDataService>
{
    private ICommand? _syncDownItemsCommand;
    public ICommand? SyncDownItemsCommand
    {
        get
        {
            return _syncDownItemsCommand = _syncDownItemsCommand ?? new Command(() =>
            {
                Navigation.PushModalAsync(new AuthCodeFormView(ScopeFactory, string.Empty));
            });
        }
    }

    public AuthCodeListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }
}


public partial class AuthCodeListingView : AuthCodeListingViewBase
{
    public AuthCodeListingView(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
        InitializeComponent();
        BindingContext = this;

        // Initialize commands
        CopyCodeCommand = new AsyncRelayCommand<string>(CopyCodeToClipboard);
        ShareCodeCommand = new AsyncRelayCommand<string>(ShareCode);
    }

    #region Commands

    public ICommand CopyCodeCommand { get; private set; }
    public ICommand ShareCodeCommand { get; private set; }

    #endregion

    #region Command Implementations

    /// <summary>
    /// Copies the referral code to clipboard
    /// </summary>
    private async Task CopyCodeToClipboard(string? authCode)
    {
        try
        {
            if (string.IsNullOrEmpty(authCode))
            {
                await DisplayAlert("Error", "No code to copy", "OK");
                return;
            }

            await Clipboard.SetTextAsync(authCode);
            await DisplayAlert("Success", "Referral code copied to clipboard!", "OK");
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to copy code: {ex.Message}", "OK");
        }
    }

    /// <summary>
    /// Opens the device's native share dialog to share the referral code
    /// </summary>
    private async Task ShareCode(string? authCode)
    {
        try
        {
            if (string.IsNullOrEmpty(authCode))
            {
                await DisplayAlert("Error", "No code to share", "OK");
                return;
            }

            var shareRequest = new ShareTextRequest
            {
                Text = $"Join me using my referral code: {authCode}",
                Title = "Share Referral Code"
            };

            await Share.RequestAsync(shareRequest);
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to share code: {ex.Message}", "OK");
        }
    }

    #endregion
}


