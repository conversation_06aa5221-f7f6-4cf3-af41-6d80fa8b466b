﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.4" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.4" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\DeepMessage.Framework.Core\Platform.Framework.Core.csproj" />
		<ProjectReference Include="..\DeepMessage.ServiceContracts\DeepMessage.ServiceContracts.csproj" />
		<ProjectReference Include="..\Platform.Client.Data\Platform.Client.Data.csproj" />
		<!--<PackageReference Include="Plugin.Firebase.Crashlytics" Version="3.1.1" />-->
		<PackageReference Include="Plugin.Firebase.CloudMessaging" Version="3.1.2" />
	</ItemGroup>

</Project>
