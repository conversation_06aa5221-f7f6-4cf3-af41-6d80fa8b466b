﻿using DeepMessage.Framework.Core;
namespace Platform.Client.Services.Features.Conversation;
public class ChatMessagesFilterViewModel :  BaseFilterViewModel
{
    public string ConversationId { get; set; } = string.Empty;

    private string _searchText = string.Empty;

	/// <summary>
	/// Search text for filtering messages by content
	/// </summary>
	public string SearchText
	{
		get => _searchText;
		set
		{
			if (SetField(ref _searchText, value))
			{
				// Update the base SearchKey property when SearchText changes
				SearchKey = value;
			}
		}
	}
}
