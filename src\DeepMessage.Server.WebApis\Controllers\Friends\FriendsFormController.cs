﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Authorization;
namespace DeepMessage.Server.WebApis.Controller.Friends;
[ApiController, Authorize, Route("api/[controller]/[action]")]
public class FriendsFormController : ControllerBase, IFriendFormDataService
{

    private readonly IFriendFormDataService dataService;

    public FriendsFormController(IFriendFormDataService dataService)
    {
        this.dataService = dataService;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    [HttpPost]
    public async Task<string> SaveAsync([FromBody] FriendFormBusinessObject formBusinessObject)
    {
        return await dataService.SaveAsync(formBusinessObject);
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    [HttpGet]
    public async Task<FriendFormBusinessObject?> GetItemByIdAsync(string id)
    {
        return await dataService.GetItemByIdAsync(id);
    }
}
