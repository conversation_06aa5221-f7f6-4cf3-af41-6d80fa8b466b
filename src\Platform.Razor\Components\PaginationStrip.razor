﻿@using DeepMessage.Framework.Core 
@using Microsoft.AspNetCore.Components.Forms;
@using Microsoft.JSInterop
@inherits InputSelect<PaginationStripModel>
<style>
    a {
        cursor: pointer;
    }
</style>


@{
    int n = 7;

    var totalPages = Convert.ToInt32(Math.Ceiling(TotalRows / (double)CurrentValue.RowsPerPage));
    if (totalPages < TotalPages)
    {
        TotalPages = totalPages;
    }

    List<int> all = new List<int>(TotalPages);
    for (int i = 1; i <= TotalPages; i++)
    {
        if (i != CurrentIndex)
            all.Add(i);
    }

    List<int> pages = new List<int>(n);

    pages.Add(CurrentValue.RowsPerPage >= TotalRows ? 1 : CurrentIndex);
    for (int i = 1; i <= n / 2; i++)
    {
        var u = all.FirstOrDefault(x => x > CurrentIndex);
        if (u > 0)
        {
            pages.Add(u);
            all.Remove(u);
        }
        else
        {
            var l = all.LastOrDefault(x => x < CurrentIndex);
            if (l > 0)
            {
                if (!pages.Any(p => p == l))
                {
                    pages.Add(l);
                }

                all.Remove(l);
            }
        }


        var u1 = all.LastOrDefault(x => x < CurrentIndex);
        if (u1 > 0)
        {
            pages.Add(u1);
            all.Remove(u1);
        }
        else
        {
            var l1 = all.FirstOrDefault(x => x > CurrentIndex);
            if (l1 > 0)
            {
                pages.Add(l1);
                all.Remove(l1);
            }
        }
        pages.Sort();
    }
}
@if (TotalRows > 10)
{

    <div class="relative overflow-hidden bg-surface border border-border rounded-lg shadow-theme-sm">
        <nav class="flex items-center justify-between px-4 py-3" aria-label="Page navigation">
            <div class="text-sm text-secondary">
                @PaginationStats(CurrentIndex)
            </div>
            <div class="flex items-center space-x-1">
                <button @onclick="async () => await UpdatePage(Math.Max(CurrentIndex - 1, 1))"
                        disabled="@(CurrentIndex <= 1)"
                        class="inline-flex items-center px-2 py-1 text-sm font-medium text-primary bg-surface border border-border rounded-md hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed transition-theme focus-theme">
                    <span class="sr-only">Previous</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <button @onclick="async () => await UpdatePage(Math.Max(CurrentIndex - n - 1, 1))"
                        class="inline-flex items-center px-3 py-1 text-sm font-medium text-primary bg-surface border border-border rounded-md hover:bg-muted transition-theme focus-theme">...</button>

                @foreach (int i in pages)
                {
                    int c = i;
                    var isActive = CurrentIndex == c;

                    <button @onclick="async () => await UpdatePage(c)"
                            class="@(isActive ? "bg-primary-800 text-white" : "bg-surface text-primary hover:bg-muted") inline-flex items-center px-3 py-1 text-sm font-medium border border-border rounded-md transition-theme focus-theme"
                            @attributes="@(isActive ? areaCurrentAttribute : nonAttribute)">
                        @c
                    </button>
                }
                <button @onclick="async () => await UpdatePage(Math.Min(CurrentIndex + n - 1, TotalPages))"
                        class="inline-flex items-center px-3 py-1 text-sm font-medium text-primary bg-surface border border-border rounded-md hover:bg-muted transition-theme focus-theme">...</button>

                <button @onclick="async () => await UpdatePage(Math.Min(CurrentIndex + 1, TotalPages))"
                        disabled="@(CurrentIndex >= TotalPages)"
                        class="inline-flex items-center px-2 py-1 text-sm font-medium text-primary bg-surface border border-border rounded-md hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed transition-theme focus-theme">
                    <span class="sr-only">Next</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
            <div class="flex items-center space-x-2">
                <label class="text-sm text-secondary">Show:</label>
                <select @bind="CurrentValue.RowsPerPage" class="form-select text-sm py-1 px-2">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="@TotalRows">All</option>
                </select>
            </div>
        </nav>
    </div>


}
