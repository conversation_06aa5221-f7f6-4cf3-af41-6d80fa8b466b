<!-- Placeholder for captcha image - replace with actual captcha resource -->
<Image Source="https://via.placeholder.com/150x50/CCCCCC/666666?text=CAPTCHA"
       HeightRequest="32"
       HorizontalOptions="Center" />

This file serves as a placeholder for the captcha image used in FakeCaptchaScreen.xaml.

The actual captcha image (captcha_placeholder.png) should be placed in the Resources/Images folder.

For now, this text file ensures the build doesn't fail when referencing the image source.

If you want to add an actual captcha image:
1. Place a PNG file named "captcha_placeholder.png" in Resources/Images/
2. Make sure it's included in the project as a MauiImage
3. Replace this text file or delete it

Recommended image specs:
- Format: PNG
- Dimensions: 200x60 pixels
- Background: Light gray
- Content: Sample CAPTCHA text or pattern