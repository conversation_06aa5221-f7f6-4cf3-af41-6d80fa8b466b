using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Conversation;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace DeepMessage.Tests.Integration
{
    public class ChatMessageIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public ChatMessageIntegrationTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    // Remove the real database
                    var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<AppDbContext>));
                    if (descriptor != null)
                        services.Remove(descriptor);

                    // Add in-memory database
                    services.AddDbContext<AppDbContext>(options =>
                    {
                        options.UseInMemoryDatabase("TestDb");
                    });
                });
            });

            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task SendMessage_WithValidData_ShouldSucceed()
        {
            // Arrange
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            
            // Seed test data
            var user = new ApplicationUser { Id = "test-user-id", UserName = "testuser" };
            var conversation = new Conversation 
            { 
                Id = "test-conversation-id", 
                Title = "Test Chat",
                Type = ConversationType.Direct
            };
            var participant = new ConversationParticipant
            {
                Id = "test-participant-id",
                ConversationId = conversation.Id,
                UserId = user.Id,
                IsAdmin = false
            };

            context.Users.Add(user);
            context.Conversations.Add(conversation);
            context.ConversationParticipants.Add(participant);
            await context.SaveChangesAsync();

            var messageRequest = new ChatMessageFormBusinessObject
            {
                ConversationId = conversation.Id,
                Content = "Hello, World!"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/chatmessages", messageRequest);

            // Assert
            response.Should().BeSuccessful();
            
            var messages = await context.Messages.ToListAsync();
            messages.Should().HaveCount(1);
            messages[0].PlainContent.Should().Be("Hello, World!");
            messages[0].SenderId.Should().Be(user.Id);
        }

        [Fact]
        public async Task SendMessage_WithInvalidContent_ShouldFail()
        {
            // Arrange
            var messageRequest = new ChatMessageFormBusinessObject
            {
                ConversationId = "test-conversation-id",
                Content = "<script>alert('xss')</script>"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/chatmessages", messageRequest);

            // Assert
            response.Should().HaveClientError();
        }

        [Fact]
        public async Task SendMessage_WithoutAuthentication_ShouldFail()
        {
            // Arrange
            var messageRequest = new ChatMessageFormBusinessObject
            {
                ConversationId = "test-conversation-id",
                Content = "Hello, World!"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/chatmessages", messageRequest);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.Unauthorized);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public async Task SendMessage_WithEmptyContent_ShouldFail(string content)
        {
            // Arrange
            var messageRequest = new ChatMessageFormBusinessObject
            {
                ConversationId = "test-conversation-id",
                Content = content
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/chatmessages", messageRequest);

            // Assert
            response.Should().HaveClientError();
        }

        [Fact]
        public async Task SendMessage_WithTooLongContent_ShouldFail()
        {
            // Arrange
            var longContent = new string('a', 5000); // Exceeds 4000 character limit
            var messageRequest = new ChatMessageFormBusinessObject
            {
                ConversationId = "test-conversation-id",
                Content = longContent
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/chatmessages", messageRequest);

            // Assert
            response.Should().HaveClientError();
        }
    }
}
