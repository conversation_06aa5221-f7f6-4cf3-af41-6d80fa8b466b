﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
namespace DeepMessage.Server.WebApis.Controller.Conversation;
[ApiController, Route("api/[controller]/[action]")]
public class ChatThreadSyncsFormController : ControllerBase, IChatThreadSyncFormDataService
{

	private readonly IChatThreadSyncFormDataService dataService;

	public ChatThreadSyncsFormController(IChatThreadSyncFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] ChatThreadSyncFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}

	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<ChatThreadSyncFormBusinessObject> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}

    [SystemClaim(SystemClaimType.SystemDefault)]
    [HttpGet]
    public async Task<ChatThreadSyncFormBusinessObject[]> GetItemsAsync()
    {
       return await dataService.GetItemsAsync();
    }
}
