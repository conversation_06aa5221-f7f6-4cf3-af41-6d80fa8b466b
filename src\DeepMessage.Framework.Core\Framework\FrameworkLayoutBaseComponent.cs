﻿using KPlatform.Framework.Core;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using System.Security.Claims;

namespace Platform.Framework.Core
{
    public class FrameworkLayoutBaseComponent : LayoutComponentBase
    {
        [Inject]
        public DialogService? DialogService { get; set; }

        
        [Inject]
        public IJSRuntime? JsRuntime { get; set; }
         

        [CascadingParameter]
        public ClaimsPrincipal? ClaimsPrinciple
        {
            get
            {
                return _user;
            }
            set
            {
                _user = value;
                if (AuthenticatedUser != null)
                {
                    AuthenticatedUser.UserId = _user.GetUserId();
                    AuthenticatedUser.Username = _user.GetUserName();
                    AuthenticatedUser.ProfileName = _user.GetProfileName();
                    AuthenticatedUser.ImageUrl = _user.GetImage(); 
                }
            }
        }

        private ClaimsPrincipal? _user;

        [Inject]
        public IAuthenticatedUser? AuthenticatedUser { get; set; }

        [Inject]
        public ILocalStorageService? StorageService { get; set; }
        [Inject]
        public AuthenticationStateProvider? AuthStateProvider { get; set; }

        public int TimeZoneOffset { get; set; } = 0;

        protected override async Task OnInitializedAsync()
        {
            if (AuthStateProvider != null)
            {

                var state = await AuthStateProvider.GetAuthenticationStateAsync();
                if (state != null)
                {
                    if (state.User.Identity.IsAuthenticated)
                    {
                        ClaimsPrinciple = state.User;

                    }
                }

            }
            DialogService.Dialogs.CollectionChanged += (p, q) =>
            {
                StateHasChanged();
            };
            await base.OnInitializedAsync();
        }

        //protected override async Task OnAfterRenderAsync(bool firstRender)
        //{
        //    if (firstRender && JsRuntime is not null)
        //    {
        //        var timezoneOffset = await JsRuntime.InvokeAsync<string>("getCookie", "timezoneoffset");
        //        if (!string.IsNullOrEmpty(timezoneOffset) && double.TryParse(timezoneOffset, out double offset))
        //        {
        //            TimeZoneOffset = (int)offset;
        //        }
        //    }
        //}

         

        public void CloseMe(ModalDialogConfig dialogConfig)
        {
            DialogService?.Dialogs.Remove(dialogConfig);
        }

        public async Task ClientLog(string Text)
        {
            await JsRuntime.InvokeVoidAsync("browserLog", Text);
        }
    }
}
