﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DeepMessage.Server.DataServices.Migrations
{
    /// <inheritdoc />
    public partial class MessageDeliveryStatuses : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "DeliveredAt",
                table: "MessageRecipients",
                newName: "DeliveryStatusTime");

            migrationBuilder.AddColumn<byte>(
                name: "DeliveryStatus",
                table: "Messages",
                type: "tinyint",
                nullable: false,
                defaultValue: (byte)0);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeliveryStatusTime",
                table: "Messages",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "DeliveryStatusNotified",
                table: "MessageRecipients",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DeliveryStatus",
                table: "Messages");

            migrationBuilder.DropColumn(
                name: "DeliveryStatusTime",
                table: "Messages");

            migrationBuilder.DropColumn(
                name: "DeliveryStatusNotified",
                table: "MessageRecipients");

            migrationBuilder.RenameColumn(
                name: "DeliveryStatusTime",
                table: "MessageRecipients",
                newName: "DeliveredAt");
        }
    }
}
