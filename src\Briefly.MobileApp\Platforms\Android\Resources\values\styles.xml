﻿<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
	<!-- Base Application Theme - Light Mode -->
	<style name="MainTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar">
		<!-- Primary App Colors -->
		<item name="android:colorPrimary">#444444</item>
		<item name="android:colorPrimaryDark">#222222</item>
		<item name="android:colorAccent">#333333</item>

		<!-- Enable theme-aware dark mode support -->
		<item name="android:forceDarkAllowed">true</item>

		<!-- Light Theme Status Bar Configuration -->
		<item name="android:statusBarColor">@color/status_bar_light</item>
		<item name="android:windowLightStatusBar">true</item>

		<!-- Light Theme Navigation Bar Configuration -->
		<item name="android:navigationBarColor">@color/navigation_bar_light</item>
		<item name="android:windowLightNavigationBar">true</item>

		<!-- Window Configuration -->
		<item name="android:windowDrawsSystemBarBackgrounds">true</item>
		<item name="android:fitsSystemWindows">false</item>

	</style>
</resources>
