using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DeepMessage.Server.DataServices.Data
{
    public class ImageSeeder
    {
        private readonly AppDbContext _context;
        private readonly ILogger<ImageSeeder> _logger;

        public ImageSeeder(AppDbContext context, ILogger<ImageSeeder> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task SeedAvatarImagesAsync()
        {
            try
            {
                // Check if we already have avatar images
                var existingAvatars = await _context.Images
                    .Where(i => i.ImageType == "avatar")
                    .CountAsync();

                if (existingAvatars > 0)
                {
                    _logger.LogInformation("Avatar images already exist, skipping seeding");
                    return;
                }

                var avatarImages = GetSampleAvatarImages();
                
                await _context.Images.AddRangeAsync(avatarImages);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully seeded {Count} avatar images", avatarImages.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error seeding avatar images");
                throw;
            }
        }

        private List<Image> GetSampleAvatarImages()
        {
            // Sample 512x512 PNG avatars as base64 (placeholder data)
            // In a real implementation, you would have actual avatar images
            var sampleAvatars = new List<Image>
            {
                new Image
                {
                    Id = Guid.NewGuid().ToString(),
                    ImageContent = GetPlaceholderAvatar("1", "#FF6B6B"), // Red
                    ImageType = "avatar",
                    CreatedAt = DateTime.UtcNow
                },
                new Image
                {
                    Id = Guid.NewGuid().ToString(),
                    ImageContent = GetPlaceholderAvatar("2", "#4ECDC4"), // Teal
                    ImageType = "avatar",
                    CreatedAt = DateTime.UtcNow
                },
                new Image
                {
                    Id = Guid.NewGuid().ToString(),
                    ImageContent = GetPlaceholderAvatar("3", "#45B7D1"), // Blue
                    ImageType = "avatar",
                    CreatedAt = DateTime.UtcNow
                },
                new Image
                {
                    Id = Guid.NewGuid().ToString(),
                    ImageContent = GetPlaceholderAvatar("4", "#96CEB4"), // Green
                    ImageType = "avatar",
                    CreatedAt = DateTime.UtcNow
                },
                new Image
                {
                    Id = Guid.NewGuid().ToString(),
                    ImageContent = GetPlaceholderAvatar("5", "#FFEAA7"), // Yellow
                    ImageType = "avatar",
                    CreatedAt = DateTime.UtcNow
                },
                new Image
                {
                    Id = Guid.NewGuid().ToString(),
                    ImageContent = GetPlaceholderAvatar("6", "#DDA0DD"), // Purple
                    ImageType = "avatar",
                    CreatedAt = DateTime.UtcNow
                },
                new Image
                {
                    Id = Guid.NewGuid().ToString(),
                    ImageContent = GetPlaceholderAvatar("7", "#98D8C8"), // Mint
                    ImageType = "avatar",
                    CreatedAt = DateTime.UtcNow
                },
                new Image
                {
                    Id = Guid.NewGuid().ToString(),
                    ImageContent = GetPlaceholderAvatar("8", "#F7DC6F"), // Gold
                    ImageType = "avatar",
                    CreatedAt = DateTime.UtcNow
                },
                new Image
                {
                    Id = Guid.NewGuid().ToString(),
                    ImageContent = GetPlaceholderAvatar("9", "#BB8FCE"), // Lavender
                    ImageType = "avatar",
                    CreatedAt = DateTime.UtcNow
                },
                new Image
                {
                    Id = Guid.NewGuid().ToString(),
                    ImageContent = GetPlaceholderAvatar("10", "#85C1E9"), // Sky Blue
                    ImageType = "avatar",
                    CreatedAt = DateTime.UtcNow
                },
                new Image
                {
                    Id = Guid.NewGuid().ToString(),
                    ImageContent = GetPlaceholderAvatar("11", "#F8C471"), // Orange
                    ImageType = "avatar",
                    CreatedAt = DateTime.UtcNow
                },
                new Image
                {
                    Id = Guid.NewGuid().ToString(),
                    ImageContent = GetPlaceholderAvatar("12", "#82E0AA"), // Light Green
                    ImageType = "avatar",
                    CreatedAt = DateTime.UtcNow
                }
            };

            return sampleAvatars;
        }

        private string GetPlaceholderAvatar(string number, string color)
        {
            // This creates a simple SVG avatar placeholder
            // In a real implementation, you would use actual avatar images
            var svg = $@"<svg width=""512"" height=""512"" xmlns=""http://www.w3.org/2000/svg"">
                <rect width=""512"" height=""512"" fill=""{color}""/>
                <circle cx=""256"" cy=""200"" r=""80"" fill=""white"" opacity=""0.8""/>
                <circle cx=""256"" cy=""350"" r=""120"" fill=""white"" opacity=""0.8""/>
                <text x=""256"" y=""280"" font-family=""Arial, sans-serif"" font-size=""48"" font-weight=""bold"" text-anchor=""middle"" fill=""white"">{number}</text>
            </svg>";

            // Convert SVG to base64
            var svgBytes = System.Text.Encoding.UTF8.GetBytes(svg);
            return Convert.ToBase64String(svgBytes);
        }
    }
}
