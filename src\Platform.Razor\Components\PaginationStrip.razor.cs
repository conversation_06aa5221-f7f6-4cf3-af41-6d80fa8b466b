﻿using Microsoft.AspNetCore.Components;
using System.Text;

namespace Platform.Razor.Components
{
	public partial class PaginationStrip
	{
		[Parameter] public int TotalPages { get; set; }

		[Parameter] public int TotalRows { get; set; }

		[Parameter] public EventCallback<int> OnPageChange { get; set; }

        private static Dictionary<string, object> areaCurrentAttribute = new Dictionary<string, object> { { "aria-current", "page" } };

        private static Dictionary<string, object> nonAttribute = new Dictionary<string, object>();

        public int CurrentIndex
		{
			get { return CurrentValue.CurrentIndex; }
		}

		public async Task UpdatePage(int currentIndex)
		{
			CurrentValue.CurrentIndex = currentIndex;
			await OnPageChange.InvokeAsync(currentIndex);
		}

		private string _SHOWING_RESULT = "Showing Results";
		public string ShowingResultClause
		{
			get { return _SHOWING_RESULT; }
			set { _SHOWING_RESULT = value; }
		}

		private string _SHOW_RESULT = "Show Result";
		public string ShowResultClause
		{
			get { return _SHOW_RESULT; }
			set { _SHOW_RESULT = value; }
		}

		private string _TO = "to";
		public string ToClause
		{
			get { return _TO; }
			set { _TO = value; }
		}

		private string _OF = "of";
		public string OfClause
		{
			get { return _OF; }
			set { _OF = value; }
		}

		private string PaginationStats(int pageNumber)
		{
			StringBuilder altGen = new StringBuilder();
			altGen.Append(pageNumber == CurrentIndex ? ShowingResultClause : ShowResultClause);
			altGen.Append(" ");
			altGen.Append(((pageNumber - 1) * (CurrentValue.RowsPerPage > TotalRows ? 0 : CurrentValue.RowsPerPage)) + 1);
			altGen.Append(" ");
			altGen.Append(ToClause);
			altGen.Append(" ");
			altGen.Append(pageNumber == TotalPages ? TotalRows : (pageNumber * CurrentValue.RowsPerPage > TotalRows ? TotalRows : pageNumber * CurrentValue.RowsPerPage));
			altGen.Append(" ");
			altGen.Append(OfClause);
			altGen.Append(" ");
			altGen.Append(TotalRows);

			return altGen.ToString();
		}


	}


}
