/* Custom CSS for FriendsListing component */

/* Line clamp utilities for text truncation */
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Friend card hover effects */
.friend-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.friend-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .friend-card:hover {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Avatar styles */
.avatar-container {
    position: relative;
}

.status-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
    border: 2px solid white;
    border-radius: 50%;
}

.dark .status-indicator {
    border-color: #1f2937;
}

.status-online {
    background-color: #10b981;
}

.status-offline {
    background-color: #6b7280;
}

.status-away {
    background-color: #f59e0b;
}

/* Button hover effects */
.btn-chat {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    transition: all 0.2s ease;
}

.btn-chat:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-1px);
}

.btn-profile {
    transition: all 0.2s ease;
}

.btn-profile:hover {
    transform: translateY(-1px);
}

/* Search input enhancements */
.search-input {
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Grid responsive adjustments */
@media (max-width: 640px) {
    .friends-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

@media (min-width: 641px) and (max-width: 1024px) {
    .friends-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (min-width: 1025px) {
    .friends-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
    }
}

/* Loading animation enhancement */
@keyframes pulse-slow {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse-slow {
    animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Focus styles for accessibility */
.focus-ring:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #3b82f6;
}

/* Custom scrollbar for better UX */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Dark mode scrollbar */
.dark .custom-scrollbar::-webkit-scrollbar-track {
    background: #374151;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #6b7280;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Skeleton loading for better UX */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.dark .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Responsive text sizing */
@media (max-width: 640px) {
    .responsive-title {
        font-size: 1.25rem;
        line-height: 1.75rem;
    }
    
    .responsive-subtitle {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
}

@media (min-width: 641px) {
    .responsive-title {
        font-size: 1.5rem;
        line-height: 2rem;
    }
    
    .responsive-subtitle {
        font-size: 1rem;
        line-height: 1.5rem;
    }
}
