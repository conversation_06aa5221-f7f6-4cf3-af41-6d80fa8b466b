// Chat-related JavaScript functions for Blazor components

// Legacy scroll function - kept for compatibility
function scrollIntoView(element) {
    if (element) {
        element.scroll({
            top: element.scrollHeight,
            behavior: 'smooth'
        });
    }
}

// WhatsApp-style scroll initialization
window.initWhatsAppScroll = (element, isFirstRender) => {
    if (!element) return;

    // Mark element as WhatsApp-style scroll container
    element.setAttribute('data-whatsapp-scroll', 'true');

    // For first render, position at bottom (which is top in reverse flex)
    if (isFirstRender) {
        // In flex-col-reverse, scrollTop: 0 shows the newest messages
        element.scrollTop = 0;
    }

    // Add scroll momentum for iOS
    element.style.webkitOverflowScrolling = 'touch';

    // Prevent scroll restoration on page reload
    if ('scrollRestoration' in history) {
        history.scrollRestoration = 'manual';
    }
};

// Check if scrolled to top (for loading older messages in reverse mode)
window.isScrolledToTop = (element) => {
    if (!element) return false;

    // In reverse flex mode, "top" means scrolled down to the actual top
    // We need to check if we're near the bottom of the scroll area
    //element.scrollTop - 640
    //element.scrollHeight  1211
    const scrollBottom = element.scrollHeight - element.clientHeight + element.scrollTop;
    return scrollBottom ;
};

// Scroll to bottom in WhatsApp mode (newest messages)
window.scrollToBottomWhatsApp = (element) => {
    if (!element) return;

    // In flex-col-reverse, scrollTop: 0 shows newest messages
    element.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
};

// Get scroll height for position maintenance
window.getScrollHeight = (element) => {
    return element ? element.scrollHeight : 0;
};

// Maintain scroll position after loading older messages
window.maintainScrollPosition = (element, previousScrollHeight) => {
    if (!element) return;

    // Calculate the difference in scroll height
    const newScrollHeight = element.scrollHeight;
    const heightDifference = newScrollHeight - previousScrollHeight;

    // Adjust scroll position to maintain visual position
    if (heightDifference > 0) {
        element.scrollTop = element.scrollTop + heightDifference;
    }
};

// Performance optimization: Throttled scroll event handler
window.createThrottledScrollHandler = (callback, delay = 16) => {
    let timeoutId;
    let lastExecTime = 0;

    return function(...args) {
        const currentTime = Date.now();

        if (currentTime - lastExecTime > delay) {
            callback.apply(this, args);
            lastExecTime = currentTime;
        } else {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => {
                callback.apply(this, args);
                lastExecTime = Date.now();
            }, delay - (currentTime - lastExecTime));
        }
    };
};

// Smooth scroll with easing for better UX
window.smoothScrollToPosition = (element, targetScrollTop, duration = 300) => {
    if (!element) return;

    const startScrollTop = element.scrollTop;
    const distance = targetScrollTop - startScrollTop;
    const startTime = performance.now();

    function easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    }

    function animateScroll(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easedProgress = easeInOutCubic(progress);

        element.scrollTop = startScrollTop + (distance * easedProgress);

        if (progress < 1) {
            requestAnimationFrame(animateScroll);
        }
    }

    requestAnimationFrame(animateScroll);
};

window.initStickyBadges = (messagesContainer) => {
    if (messagesContainer) {
        const badges = messagesContainer.querySelectorAll('.badge');
        let scrollTimeout;

        // Function to handle scroll
        const handleScroll = () => {
            badges.forEach(badge => {
                badge.classList.add('sticky');
                badge.classList.add('shadow');
            });

            // Clear the timeout if there's already one
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }

            // Remove the sticky class after 1 second of no scroll
            scrollTimeout = setTimeout(() => {
                badges.forEach(badge => {
                    badge.classList.remove('sticky');
                    badge.classList.remove('shadow');
                });
            }, 1000);
        };

        // Attach the scroll event listener to the messages container
        messagesContainer.addEventListener('scroll', handleScroll);
    }
};


window.scrollToBottom = (element) => {
    if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
};

window.getScrollTop = (element) => {
    return element ? element.scrollTop : 0;
};

window.focusElement = (element) => {
    if (element) {
        element.focus();
    }
};

window.triggerFileInput = (element) => {
    if (element) {
        element.click();
    }
};

window.autoResizeTextarea = (element) => {
    if (element) {
        element.style.height = 'auto';
        element.style.height = Math.min(element.scrollHeight, 120) + 'px';
    }
};

// Auto-resize textarea on input
window.setupAutoResize = (element) => {
    if (element) {
        element.addEventListener('input', () => {
            window.autoResizeTextarea(element);
        });
    }
};

// Scroll to bottom when new messages arrive
window.scrollToBottomIfNearBottom = (container, threshold = 100) => {
    if (container) {
        const isNearBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - threshold;
        if (isNearBottom) {
            container.scrollTop = container.scrollHeight;
        }
    }
};

// Handle emoji picker outside click
window.setupEmojiPickerOutsideClick = (emojiPicker, toggleButton, callback) => {
    const handleOutsideClick = (event) => {
        if (emojiPicker && !emojiPicker.contains(event.target) && 
            toggleButton && !toggleButton.contains(event.target)) {
            callback();
        }
    };
    
    document.addEventListener('click', handleOutsideClick);
    
    // Return cleanup function
    return () => {
        document.removeEventListener('click', handleOutsideClick);
    };
};

// Copy text to clipboard
window.copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        console.error('Failed to copy text: ', err);
        return false;
    }
};

// Format file size
window.formatFileSize = (bytes) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

// Detect if user is typing
window.setupTypingDetection = (inputElement, onTypingStart, onTypingStop, timeout = 2000) => {
    let typingTimer;
    let isTyping = false;
    
    const startTyping = () => {
        if (!isTyping) {
            isTyping = true;
            onTypingStart();
        }
        clearTimeout(typingTimer);
        typingTimer = setTimeout(() => {
            isTyping = false;
            onTypingStop();
        }, timeout);
    };
    
    if (inputElement) {
        inputElement.addEventListener('input', startTyping);
        inputElement.addEventListener('keydown', startTyping);
    }
    
    // Return cleanup function
    return () => {
        clearTimeout(typingTimer);
        if (inputElement) {
            inputElement.removeEventListener('input', startTyping);
            inputElement.removeEventListener('keydown', startTyping);
        }
    };
};

// Handle drag and drop for file uploads
window.setupDragAndDrop = (dropZone, onFilesDropped) => {
    if (!dropZone) return;
    
    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
        dropZone.classList.add('drag-over');
    };
    
    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        dropZone.classList.remove('drag-over');
    };
    
    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        dropZone.classList.remove('drag-over');
        
        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            onFilesDropped(files);
        }
    };
    
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleDrop);
    
    // Return cleanup function
    return () => {
        dropZone.removeEventListener('dragover', handleDragOver);
        dropZone.removeEventListener('dragleave', handleDragLeave);
        dropZone.removeEventListener('drop', handleDrop);
    };
};

// Smooth scroll to element
window.smoothScrollToElement = (element, behavior = 'smooth') => {
    if (element) {
        element.scrollIntoView({ behavior, block: 'nearest' });
    }
};

// Get element dimensions
window.getElementDimensions = (element) => {
    if (element) {
        const rect = element.getBoundingClientRect();
        return {
            width: rect.width,
            height: rect.height,
            top: rect.top,
            left: rect.left
        };
    }
    return null;
};

// Check if element is in viewport
window.isElementInViewport = (element) => {
    if (!element) return false;
    
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
};

// Debounce function
window.debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

// Throttle function
window.throttle = (func, limit) => {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};
