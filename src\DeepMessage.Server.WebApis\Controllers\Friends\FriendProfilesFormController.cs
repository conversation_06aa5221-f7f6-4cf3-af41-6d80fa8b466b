using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Authorization;

namespace DeepMessage.Server.WebApis.Controllers.Friends;

[ApiController, Authorize, Route("api/[controller]/[action]")]
public class FriendProfilesFormController : ControllerBase, IFriendProfileFormDataService
{
    private readonly IFriendProfileFormDataService dataService;

    public FriendProfilesFormController(IFriendProfileFormDataService dataService)
    {
        this.dataService = dataService;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    [HttpPost]
    public async Task<string> SaveAsync([FromBody] FriendProfileFormBusinessObject formBusinessObject)
    {
        return await dataService.SaveAsync(formBusinessObject);
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    [HttpGet]
    public async Task<FriendProfileFormBusinessObject> GetItemByIdAsync(string id)
    {
        return await dataService.GetItemByIdAsync(id);
    }
}
