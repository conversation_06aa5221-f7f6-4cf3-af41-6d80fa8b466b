@page "/settings"
@using Platform.Framework.Core
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject ILocalStorageService StorageService

<!-- Settings Management - WhatsApp Style -->
<div class="flex flex-col animate-fade-in h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Compact Header -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div class="flex items-center justify-between">
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">Settings</h1>
            <button @onclick="SaveSettings" 
                    class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                Save
            </button>
        </div>
    </div>

    <!-- Settings Content -->
    <div class="flex-1 overflow-y-auto">
        <!-- Account Section -->
        <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div class="px-4 py-3">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Account</h2>
                
                <!-- Profile Settings -->
                <div @onclick='() => Navigation.NavigateTo("/profile")'
                     class="flex items-center py-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 -mx-4 px-4 rounded-lg transition-colors duration-200">
                    <div class="flex-shrink-0 mr-3">
                        <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Profile</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Manage your profile information</p>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>

                <!-- Privacy Settings -->
                <div @onclick="ShowPrivacySettings" 
                     class="flex items-center py-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 -mx-4 px-4 rounded-lg transition-colors duration-200">
                    <div class="flex-shrink-0 mr-3">
                        <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Privacy</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Control your privacy settings</p>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Appearance Section -->
        <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 mt-4">
            <div class="px-4 py-3">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Appearance</h2>
                
                <!-- Dark Mode Toggle -->
                <div class="flex items-center justify-between py-3">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-3">
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 dark:text-white">Dark Mode</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Switch between light and dark themes</p>
                        </div>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" @bind="darkModeEnabled"  class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <!-- Font Size -->
                <div class="py-3">
                    <div class="flex items-center mb-3">
                        <div class="flex-shrink-0 mr-3">
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 dark:text-white">Font Size</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Adjust text size for better readability</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-xs text-gray-500 dark:text-gray-400">Small</span>
                        <input type="range" @bind="fontSize"  min="12" max="20" step="1" 
                               class="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                        <span class="text-lg text-gray-500 dark:text-gray-400">Large</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notifications Section -->
        <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 mt-4">
            <div class="px-4 py-3">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Notifications</h2>
                
                <!-- Message Notifications -->
                <div class="flex items-center justify-between py-3">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-3">
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 17H6l5 5v-5zM12 3v9l4-4m-4 4L8 8"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 dark:text-white">Message Notifications</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Get notified about new messages</p>
                        </div>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" @bind="messageNotificationsEnabled" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <!-- Sound -->
                <div class="flex items-center justify-between py-3">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-3">
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 dark:text-white">Sound</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Play sound for notifications</p>
                        </div>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" @bind="soundEnabled" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>
        </div>

        <!-- Stealth Mode Section -->
        <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 mt-4">
            <div class="px-4 py-3">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Stealth Mode</h2>
                
                <!-- Stealth Mode Toggle -->
                <div class="flex items-center justify-between py-3">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 mr-3">
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 dark:text-white">Enable Stealth Mode</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Show news screen on app startup</p>
                        </div>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" @bind="stealthModeEnabled" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <!-- Stealth Code -->
                @if (stealthModeEnabled)
                {
                    <div class="py-3">
                        <label for="stealthCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Stealth Code
                        </label>
                        <input type="text" @bind="stealthCode" id="stealthCode" 
                               placeholder="Enter stealth activation code"
                               class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Enter this code in the news search to access authentication
                        </p>
                    </div>
                }
            </div>
        </div>

        <!-- About Section -->
        <div class="bg-white dark:bg-gray-800 mt-4">
            <div class="px-4 py-3">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-3">About</h2>
                
                <!-- App Version -->
                <div class="flex items-center py-3">
                    <div class="flex-shrink-0 mr-3">
                        <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Version</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">1.0.0</p>
                    </div>
                </div>

                <!-- Sign Out -->
                <div @onclick="SignOut" 
                     class="flex items-center py-3 cursor-pointer hover:bg-red-50 dark:hover:bg-red-900/20 -mx-4 px-4 rounded-lg transition-colors duration-200">
                    <div class="flex-shrink-0 mr-3">
                        <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-red-600 dark:text-red-400">Sign Out</h3>
                        <p class="text-sm text-red-500 dark:text-red-400">Sign out of your account</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
