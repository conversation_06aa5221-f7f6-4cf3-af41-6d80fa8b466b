using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using DeepMessage.ServiceContracts.Features.AuthCodes;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.Framework.Core;
using Platform.Client.Services.Features.AuthCodes;

namespace Platform.Razor.Features.ReferralCodes
{
    public partial class ReferralCodes : ListingBase<AuthCodeListingViewModel, AuthCodeListingBusinessObject, AuthCodeFilterViewModel, AuthCodeFilterBusinessObject, IAuthCodeListingDataService>
    {
        [Inject] private IAuthCodeFormDataService FormDataService { get; set; } = null!;
        [Inject] private IJSRuntime JSRuntime { get; set; } = null!;

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
        }

        /// <summary>
        /// Navigates back to the profile page
        /// </summary>
        private void NavigateBack()
        {
            Navigation.NavigateTo("/profile");
        }

        /// <summary>
        /// Generates a new referral code
        /// </summary>
        private async Task GenerateNewCode()
        {
            try
            {
                IsWorking = true;
                StateHasChanged();

                var formBusinessObject = new AuthCodeFormBusinessObject();
                var newCode = await FormDataService.SaveAsync(formBusinessObject);

                if (!string.IsNullOrEmpty(newCode))
                {
                    await ShowToast($"New referral code generated: {newCode}");
                    await LoadItems(); // Refresh the list
                }
                else
                {
                    await ShowToast("Failed to generate referral code");
                }
            }
            catch (Exception ex)
            {
                await ShowToast($"Error generating code: {ex.Message}");
            }
            finally
            {
                IsWorking = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Copies the referral code to clipboard
        /// </summary>
        private async Task CopyToClipboard(string? code)
        {
            if (string.IsNullOrEmpty(code))
                return;

            try
            {
                await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", code);
                await ShowToast($"Code {code} copied to clipboard");
            }
            catch
            {
                // Fallback for older browsers
                try
                {
                    await JSRuntime.InvokeVoidAsync("eval", $@"
                        const textArea = document.createElement('textarea');
                        textArea.value = '{code}';
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                    ");
                    await ShowToast($"Code {code} copied to clipboard");
                }
                catch
                {
                    await ShowToast("Failed to copy code to clipboard");
                }
            }
        }

        /// <summary>
        /// Gets the CSS class for the status badge
        /// </summary>
        private string GetStatusClass(AuthCodeStatus status)
        {
            return status switch
            {
                AuthCodeStatus.Unused => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
                AuthCodeStatus.Consumed => "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
                AuthCodeStatus.Locked => "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400",
                AuthCodeStatus.Blocked => "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
                _ => "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
            };
        }

        /// <summary>
        /// Gets the display text for the status
        /// </summary>
        private string GetStatusText(AuthCodeStatus status)
        {
            return status switch
            {
                AuthCodeStatus.Unused => "Available",
                AuthCodeStatus.Consumed => "Used",
                AuthCodeStatus.Locked => "Locked",
                AuthCodeStatus.Blocked => "Blocked",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Shows a toast message
        /// </summary>
        private async Task ShowToast(string message)
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("eval", $@"
                    const toast = document.createElement('div');
                    toast.className = 'fixed top-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg z-50';
                    toast.textContent = '{message}';
                    document.body.appendChild(toast);
                    setTimeout(() => {{
                        toast.remove();
                    }}, 3000);
                ");
            }
            catch
            {
                // Ignore errors
            }
        }
    }
}
