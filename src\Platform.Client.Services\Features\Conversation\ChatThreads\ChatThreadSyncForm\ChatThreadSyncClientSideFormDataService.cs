﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
namespace Platform.Client.Services.Features.Conversation;
public class ChatThreadSyncClientSideFormDataService : IChatThreadSyncFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public ChatThreadSyncClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(ChatThreadSyncFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/ChatThreadSyncsForm/Save", formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<ChatThreadSyncFormBusinessObject> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<ChatThreadSyncFormBusinessObject>($"api/ChatThreadSyncsForm/GetItemById?id=" + id);
	}

    public async Task<ChatThreadSyncFormBusinessObject[]> GetItemsAsync()
    {
        return await _httpClient.GetFromJsonAsync<ChatThreadSyncFormBusinessObject[]>($"api/ChatThreadSyncsForm/GetItems");
    }
}
