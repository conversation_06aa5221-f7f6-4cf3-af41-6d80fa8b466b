@page "/profile"
@inherits ListingBase<ProfileListingViewModel,ProfileListingBusinessObject,
ProfileFilterViewModel, ProfileFilterBusinessObject, IProfileListingDataService>
@using DeepMessage.ServiceContracts.Features.Account
@using DeepMessage.ServiceContracts.Features.Profile
@using ModelFury.Briefly.MobileApp.Features.Account
@using Platform.Client.Services.Features.Profile
@using Platform.Razor.Components.Avatar
@using System.Security.Claims
@{
    var item = Items.FirstOrDefault();
}

<!-- Profile Management - Sleek Minimal Design -->
<div class="h-full bg-background">
    <!-- Header -->
    <div class="nav-header">
        <div class="px-4 py-4">
            <div class="flex items-center justify-between">
                <h1 class="text-responsive-xl font-semibold text-primary">My Profile</h1>
            </div>
        </div>
    </div>

    @if (item != null)
    {
        <!-- Profile Content -->
        <div class="flex-1 overflow-y-auto mobile-content fade-in">
            <!-- Profile Display Section -->
            <div class="bg-surface border-b border-border touch-spacing">
                <div class="flex flex-col items-center space-y-4">
                    <!-- Avatar and Display Name -->
                    <div class="text-center">
                        <AvatarDisplay AvatarData="@item.AvatarData"
                                       DisplayName="@item.DisplayName"
                                       Size="AvatarDisplay.AvatarSize.ExtraLarge"
                                       IsClickable="false" />
                        <h2 class="text-lg font-semibold text-primary mt-3">@(item.DisplayName ?? "No Name")</h2>
                        <p class="text-sm text-secondary mt-2">This name will be visible to your friends when you add them as your friend, they can change your name in their contact list also</p>
                    </div>
                </div>
            </div>

            <!-- Navigation Links Section -->
            <div class="bg-surface border-b border-border mt-2">
                <div class="touch-spacing">
                    <!-- Update Profile -->
                    <div @onclick="UpdateProfile"
                         class="flex items-center py-3 cursor-pointer hover:bg-muted -mx-4 px-4 rounded-lg transition-theme touch-target">
                        <div class="flex-shrink-0 mr-3">
                            <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-primary">Update Profile</h4>
                            <p class="text-sm text-secondary">Edit your profile information and avatar</p>
                        </div>
                        <svg class="w-5 h-5 text-secondary ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Password Change Section -->
            <div class="bg-surface border-b border-border mt-2">
                <div class="touch-spacing">
                    <!-- Change Password -->
                    <div @onclick="ChangePassword"
                         class="flex items-center py-3 cursor-pointer hover:bg-muted -mx-4 px-4 rounded-lg transition-theme touch-target">
                        <div class="flex-shrink-0 mr-3">
                            <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-6 6h-2.5a2.5 2.5 0 01-2.5-2.5 2.5 2.5 0 012.5-2.5H16a2 2 0 002-2m-7 4v4m0 0H9m2 0h2m-2-4a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-primary">Change Password</h4>
                            <p class="text-sm text-secondary">Update your account password</p>
                        </div>
                        <svg class="w-5 h-5 text-secondary ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Referral Codes Section -->
            <div class="bg-surface mt-2">
                <div class="touch-spacing">
                    <!-- Referral Codes -->
                    <div @onclick="NavigateToReferralCodes"
                         class="flex items-center py-3 cursor-pointer hover:bg-muted -mx-4 px-4 rounded-lg transition-theme touch-target">
                        <div class="flex-shrink-0 mr-3">
                            <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-primary">Manage Referral Codes</h4>
                            <p class="text-sm text-secondary">Create and share friend invitation codes</p>
                        </div>
                        <svg class="w-5 h-5 text-secondary ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    /// <summary>
    /// Navigates to update profile page
    /// </summary>
    private async Task UpdateProfile()
    {
        var userId = await StorageService.GetValue(ClaimTypes.NameIdentifier);
        Navigation.NavigateTo($"/profile/edit/{userId}");
    }

    /// <summary>
    /// Changes password
    /// </summary>
    private void ChangePassword()
    {
        Navigation.NavigateTo("/change-password");
    }

    /// <summary>
    /// Navigates to referral codes page
    /// </summary>
    private void NavigateToReferralCodes()
    {
        Navigation.NavigateTo("/referral-codes");
    }
}