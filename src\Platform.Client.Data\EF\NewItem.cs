﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Client.Data.EF
{
    public class NewsItem
    {
        [Key, StringLength(450)]
        public string? Id { get; set; }

        public string Title { get; set; } = null!;
        
        public string Description { get; set; } = null!;

        public string Link { get; set; } = null!;
        
        public string? ImageUrl { get; set; }
        
        public string? JsonData { get; set; }

        public DateTime PubDate { get; set; } 
    }
}
