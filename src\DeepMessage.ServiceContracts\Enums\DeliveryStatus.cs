﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DeepMessage.ServiceContracts.Enums
{
    public enum MessageDeliveryStatus : byte
    {
        Pending = 0,
        QueuedToUpSync = 1,
        DeliveryFailed = 8,
        SentToMessageServer = 16,
        SentToEndUserViaPushNotification = 32,
        SentToEndUserViaSignalR = 64,
        DeliveredToEndUser = 128,
        ReadByEndUser = 255,
    }

    // Type alias for backward compatibility with UI components
    public enum DeliveryStatus : byte
    {
        Pending = 0,
        QueuedToUpSync = 1,
        DeliveryFailed = 8,
        SentToMessageServer = 16,
        SentToEndUserViaPushNotification = 32,
        SentToEndUserViaSignalR = 64,
        DeliveredToEndUser = 128,
        ReadByEndUser = 255,
    }

    public enum AcknowledgementStatus : byte
    {
        Pending = 0,
        QueuedToUpSync = 1,
        SentToMessageServer = 4,
        SentToEndUserViaSignalR = 8,
        DeliveredToEndUser = 16,
    }
}
