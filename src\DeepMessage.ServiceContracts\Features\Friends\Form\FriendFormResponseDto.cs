using System.ComponentModel.DataAnnotations;

namespace DeepMessage.ServiceContracts.Features.Friends
{
    /// <summary>
    /// Response DTO for friend form operations that includes essential friend data
    /// with RSA public key for encrypted messaging
    /// </summary>
    public class FriendFormResponseDto
    {
        /// <summary>
        /// Unique identifier for the friendship record
        /// </summary>
        [Required]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Friend's user ID
        /// </summary>
        [Required]
        public string FriendId { get; set; } = string.Empty;

        /// <summary>
        /// Friend's username/nickname
        /// </summary>
        [Required]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Friend's display name as set by the current user
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Friend's RSA public key for message encryption
        /// </summary>
        [Required]
        public string Pub1 { get; set; } = string.Empty;

        /// <summary>
        /// Optional display picture URL
        /// </summary>
        public string? DisplayPictureUrl { get; set; }

        /// <summary>
        /// Timestamp when the friendship was created
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }
}
