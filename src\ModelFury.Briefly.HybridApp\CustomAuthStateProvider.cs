﻿using Microsoft.AspNetCore.Components.Authorization;
using Platform.Framework.Core;
using System.Security.Claims;
using System.Text.Json;

public class CustomAuthStateProvider : AuthenticationStateProvider
{
    private readonly ILocalStorageService localStorage;
    private ClaimsPrincipal? currentUser = null;

    public static string? auth_token;
    public CustomAuthStateProvider(ILocalStorageService localStorage)
    {
        this.localStorage = localStorage;
    }

    public override Task<AuthenticationState> GetAuthenticationStateAsync() => LogInAsyncCore();
    //Task.FromResult(new AuthenticationState(currentUser));

    public Task LogInAsync()
    {
        var loginTask = LogInAsyncCore();
        NotifyAuthenticationStateChanged(loginTask); 
        return loginTask; 
    }

    async Task<AuthenticationState> LogInAsyncCore()
    {
        var userId = await localStorage.GetValue(ClaimTypes.NameIdentifier);
        if (string.IsNullOrEmpty(userId))
        {
            currentUser = new ClaimsPrincipal(new ClaimsIdentity());
        }
        else
        {  
            var identity = new ClaimsIdentity("custom");
            identity.AddClaim(new Claim(ClaimTypes.NameIdentifier, userId));
            identity.AddClaim(new Claim(ClaimTypes.Name, await localStorage.GetValue(ClaimTypes.Name) ?? string.Empty));
            identity.AddClaim(new Claim(ClaimTypes.GivenName, await localStorage.GetValue(ClaimTypes.GivenName) ?? string.Empty)); 
            identity.AddClaim(new Claim(ClaimTypes.UserData, await localStorage.GetValue(ClaimTypes.UserData) ?? string.Empty));
            try
            {
                var roles = await localStorage.GetValue(ClaimTypes.Role);
                if (roles != null)
                {
                    foreach (var role in JsonSerializer.Deserialize<string[]>(roles)!)
                    {
                        identity.AddClaim(new Claim(ClaimTypes.Role, role));
                    }
                }
            }
            catch { }
            currentUser = new ClaimsPrincipal(identity);
        }
        return new AuthenticationState(currentUser);
    }

    public async Task Logout()
    {
        await localStorage.RemoveValue(ClaimTypes.NameIdentifier); 
        await localStorage.RemoveValue(ClaimTypes.Name);
        await localStorage.RemoveValue(ClaimTypes.Role);
        await localStorage.RemoveValue(ClaimTypes.GivenName); 
        currentUser = new ClaimsPrincipal(new ClaimsIdentity());
        NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(currentUser)));
    }

    public async Task<AuthenticationState> GetUpdatedClaimsFromServer(Dictionary<string, string> dictionary)
    {
        var userId = await localStorage.GetValue(ClaimTypes.NameIdentifier);
        if (!string.IsNullOrEmpty(userId))
        {
            ClaimsIdentity identity = currentUser.Identity as ClaimsIdentity;
            foreach (var kvp in dictionary)
            {
                Claim _removeclaim = identity.FindFirst(kvp.Key.ToString());
                if (_removeclaim != null)
                {
                    identity.RemoveClaim(_removeclaim);
                    identity.AddClaim(new Claim(kvp.Key.ToString(), kvp.Value.ToString()));
                    await localStorage.SetValue(kvp.Value.ToString(), kvp.Key.ToString());
                }
            }

            currentUser = new ClaimsPrincipal(identity);
            NotifyAuthenticationStateChanged(
          Task.FromResult(new AuthenticationState(currentUser)));
        }

        return new AuthenticationState(currentUser);
    }
}
