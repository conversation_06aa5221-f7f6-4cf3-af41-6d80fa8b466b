﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\DeepMessage.ServiceContracts\DeepMessage.ServiceContracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Features\Account\Form\" />
  </ItemGroup>

  <ItemGroup>
	  <PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
	  <PackageReference Include="FirebaseAdmin" Version="3.2.0" />
	  <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Core" Version="1.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Caching.Hybrid" Version="9.6.0" />
	<PackageReference Include="Microsoft.Extensions.Caching.SqlServer" Version="9.0.6" />
  </ItemGroup>
 
</Project>
