﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
namespace DeepMessage.Server.WebApis.Controller.Conversation;
[ApiController, Authorize, Route("api/[controller]/[action]")]
public class ChatThreadsListingController : ControllerBase, IChatThreadsListingDataService
{

	private readonly IChatThreadsListingDataService dataService;

	public ChatThreadsListingController(IChatThreadsListingDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<PagedDataList<ChatThreadsListingBusinessObject>> GetPaginatedItems([FromQuery] ChatThreadsFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
