@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Account
@using ModelFury.Briefly.MobileApp.Features.Account
@using Platform.Framework.Core
@page "/signin"
@page "/login"
@inherits FormBase<SignInFormBusinessObject, SignInFormViewModel, string, ISignInFormDataService>

<!-- Main Container with Nothing Phone Aesthetic -->
<div class="min-h-screen bg-background flex items-center justify-center touch-spacing">
    <div class="mobile-container w-full space-y-6 sm:space-y-8">
        <!-- Header with Nothing Phone Black Icon -->
        <div class="text-center">
            <div class="auth-header-icon">
                <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
            </div>
            <h2 class="text-responsive-xl font-bold text-primary">Welcome back</h2>
            <p class="mt-2 text-responsive-sm text-secondary">
                Sign in to your account to continue
            </p>
        </div>

        <!-- Error Display -->
        @if (!string.IsNullOrEmpty(Error))
        {
            <div class="auth-error-container">
                <div class="flex">
                    <svg class="h-5 w-5 text-secondary-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <h3 class="text-sm font-medium text-secondary-600">Sign in failed</h3>
                        <p class="mt-1 text-sm text-secondary">@Error</p>
                    </div>
                </div>
            </div>
        }

        <!-- Sign In Form -->
        <div class="auth-form-container">
            <EditForm Model="SelectedItem" OnValidSubmit="HandleFormSubmit">
                <DataAnnotationsValidator />
                
                <!-- Username Field -->
                <div class="form-group">
                    <label for="nickname-input" class="form-label">
                        Username
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" aria-hidden="true">
                            <svg class="h-5 w-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                            </svg>
                        </div>
                        <InputText @bind-Value="SelectedItem.NickName"
                                   @onfocus="@(() => UsernameFieldFocused = true)"
                                   @onblur="@(() => UsernameFieldFocused = false)"
                                   id="nickname-input"
                                   placeholder="Enter your username"
                                   class="form-control pl-10 @(UsernameFieldFocused ? "ring-2 ring-primary-500 border-primary-500" : "")" 
                                   autocomplete="username"  />
                    </div>
                    <ValidationMessage For="@(() => SelectedItem!.NickName)" class="form-error" id="nickname-error" />
                </div>

                <!-- Password Field -->
                <div class="form-group mt-4">
                    <label for="password-input" class="form-label">
                        Password
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" aria-hidden="true">
                            <svg class="h-5 w-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <InputText @bind-Value="SelectedItem!.PassKey"
                                   @onfocus="@(() => PasswordFieldFocused = true)"
                                   @onblur="@(() => PasswordFieldFocused = false)"
                                   id="password-input"
                                   type="@(SelectedItem!.ShowPassword ? "text" : "password")"
                                   placeholder="Enter your password"
                                   class="form-control pl-10 pr-12 @(PasswordFieldFocused ? "ring-2 ring-primary-500 border-primary-500" : "")"
                                   autocomplete="current-password"
                                  />
                        <button type="button" @onclick="TogglePasswordVisibility"
                                id="password-toggle"
                                class="password-toggle"
                                aria-label="@(SelectedItem!.ShowPassword ? "Hide password" : "Show password")"
                                aria-pressed="@SelectedItem!.ShowPassword.ToString().ToLower()">
                            @if (SelectedItem!.ShowPassword)
                            {
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                </svg>
                            }
                            else
                            {
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            }
                        </button>
                    </div>
                    <ValidationMessage For="@(() => SelectedItem!.PassKey)" class="form-error" id="password-error" />
                </div>

                <!-- Sign In Button -->
                <div>
                    <button type="submit"
                            disabled="@(IsWorking)"
                            class="auth-form-button rounded-full mt-4">
                        @if (IsWorking)
                        {
                            <div class="auth-loading-spinner"></div>
                            <span>Signing in...</span>
                        }
                        else
                        {
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <svg class="h-5 w-5 text-white/80 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                            <span>Sign in</span>
                        }
                    </button>
                </div>

            </EditForm>

            <!-- Sign Up Link -->
            <div class="text-center mt-6">
                <p class="text-sm text-secondary">
                    Don't have an account?
                    <button type="button" @onclick="GoToSignup" class="text-primary hover:text-primary-700 font-medium transition-colors ml-1">
                        Sign up here
                    </button>
                </p>
            </div>
        </div>
    </div>
</div>