using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Platform.Razor.Features.Messages.Listing
{
    public partial class MessagesListing
    {
        [Inject] private NavigationManager Navigation { get; set; } = null!;
        [Inject] private IJSRuntime JSRuntime { get; set; } = null!;

        private bool isLoading = true;
        private bool showSearch = false;
        private string searchQuery = string.Empty;
        private List<ChatThread> chats = new();
        private List<ChatThread> filteredChats = new();

        protected override async Task OnInitializedAsync()
        {
            await LoadChatThreads();
        }

        private async Task LoadChatThreads()
        {
            isLoading = true;
            StateHasChanged();

            // Simulate loading delay
            await Task.Delay(1000);

            // Mock data - replace with actual service call
            chats = new List<ChatThread>
            {
                new ChatThread
                {
                    Id = "1",
                    Name = "Alice Johnson",
                    LastMessage = "Hey! How are you doing?",
                    LastMessageTime = DateTime.Now.AddMinutes(-5),
                    UnreadCount = 2,
                    IsTyping = false,
                    AvatarUrl = ""
                },
                new ChatThread
                {
                    Id = "2",
                    Name = "<PERSON>",
                    LastMessage = "Thanks for the help earlier!",
                    LastMessageTime = DateTime.Now.AddHours(-2),
                    UnreadCount = 0,
                    IsTyping = false,
                    AvatarUrl = ""
                },
                new ChatThread
                {
                    Id = "3",
                    Name = "Team Chat",
                    LastMessage = "Meeting at 3 PM today",
                    LastMessageTime = DateTime.Now.AddHours(-4),
                    UnreadCount = 5,
                    IsTyping = true,
                    AvatarUrl = ""
                },
                new ChatThread
                {
                    Id = "4",
                    Name = "Sarah Wilson",
                    LastMessage = "See you tomorrow!",
                    LastMessageTime = DateTime.Now.AddDays(-1),
                    UnreadCount = 0,
                    IsTyping = false,
                    AvatarUrl = ""
                }
            };

            FilterChats();
            isLoading = false;
            StateHasChanged();
        }

        private void FilterChats()
        {
            if (string.IsNullOrWhiteSpace(searchQuery))
            {
                filteredChats = chats.OrderByDescending(c => c.LastMessageTime).ToList();
            }
            else
            {
                filteredChats = chats
                    .Where(c => c.Name.Contains(searchQuery, StringComparison.OrdinalIgnoreCase) ||
                               c.LastMessage.Contains(searchQuery, StringComparison.OrdinalIgnoreCase))
                    .OrderByDescending(c => c.LastMessageTime)
                    .ToList();
            }
        }

        private void ToggleSearch()
        {
            showSearch = !showSearch;
            if (!showSearch)
            {
                searchQuery = string.Empty;
                FilterChats();
            }
        }

        private void StartNewChat()
        {
            Navigation.NavigateTo("/friends");
        }

        private void OpenChat(string chatId)
        {
            Navigation.NavigateTo($"/chat/{chatId}");
        }

        private string GetInitials(string name)
        {
            if (string.IsNullOrEmpty(name))
                return "?";

            var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length >= 2)
                return $"{parts[0][0]}{parts[1][0]}".ToUpper();
            
            return name[0].ToString().ToUpper();
        }

        private string GetFormattedTime(DateTime time)
        {
            var now = DateTime.Now;
            var timeSpan = now - time;

            if (timeSpan.TotalMinutes < 1)
                return "now";
            
            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes}m";
            
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours}h";
            
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays}d";
            
            if (time.Year == now.Year)
                return time.ToString("MMM dd");
            
            return time.ToString("MM/dd/yy");
        }
    }

    public class ChatThread
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string LastMessage { get; set; } = string.Empty;
        public DateTime LastMessageTime { get; set; }
        public int UnreadCount { get; set; }
        public bool IsTyping { get; set; }
        public string AvatarUrl { get; set; } = string.Empty;
    }
}
