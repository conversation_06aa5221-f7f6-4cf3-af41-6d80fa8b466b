﻿using DeepMessage.Framework.Core;
using System.ComponentModel.DataAnnotations;
namespace DeepMessage.ServiceContracts.Features.Profile;
public class AvatarFormBusinessObject
{
    [Required]
    public string? Id { get; set; }

    public string? AvatarDescription { get; set; }

    /// <summary>
    /// Generated avatar image data (temporary storage before save)
    /// </summary>
    public string? GeneratedAvatarData { get; set; }

    /// <summary>
    /// Operation type: "generate" or "save"
    /// </summary>
    public string Operation { get; set; } = "generate";

}
