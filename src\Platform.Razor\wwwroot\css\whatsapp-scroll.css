/* WhatsApp-style scroll optimizations */
[data-whatsapp-scroll="true"] {
    /* Smooth scrolling with momentum on iOS */
    -webkit-overflow-scrolling: touch;
    
    /* Optimize scroll performance */
    will-change: scroll-position;
    
    /* Hide scrollbar on mobile for cleaner look */
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

[data-whatsapp-scroll="true"]::-webkit-scrollbar {
    width: 4px;
}

[data-whatsapp-scroll="true"]::-webkit-scrollbar-track {
    background: transparent;
}

[data-whatsapp-scroll="true"]::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

[data-whatsapp-scroll="true"]::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
}

/* Optimize message bubble rendering */
.message-bubble {
    contain: layout style paint;
    transform: translateZ(0);
}

/* Loading indicator for older messages */
.loading-older-messages {
    position: sticky;
    top: 0;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    border-radius: 0.5rem;
    margin: 0.5rem;
    padding: 0.75rem;
    text-align: center;
    font-size: 0.875rem;
    color: #6b7280;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Dark mode support for loading indicator */
@media (prefers-color-scheme: dark) {
    .loading-older-messages {
        background: rgba(31, 41, 55, 0.9);
        color: #d1d5db;
    }
}

/* Smooth transitions for message appearance */
.message-enter {
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive optimizations */
@media (max-width: 768px) {
    [data-whatsapp-scroll="true"] {
        /* Better touch scrolling on mobile */
        overscroll-behavior: contain;
    }
    
    /* Optimize scrollbar for mobile */
    [data-whatsapp-scroll="true"]::-webkit-scrollbar {
        width: 2px;
    }
}

/* Performance optimizations for large message lists */
.message-bubble {
    /* Enable hardware acceleration */
    transform: translateZ(0);
    backface-visibility: hidden;
    
    /* Optimize repaints */
    contain: layout style paint;
}

/* Smooth scroll behavior for the entire container */
[data-whatsapp-scroll="true"] {
    scroll-behavior: smooth;
}

/* Prevent scroll restoration issues */
html {
    scroll-behavior: auto;
}

/* Optimize for touch devices */
@media (pointer: coarse) {
    [data-whatsapp-scroll="true"] {
        /* Improve touch scrolling */
        touch-action: pan-y;
        
        /* Better momentum scrolling */
        -webkit-overflow-scrolling: touch;
    }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    [data-whatsapp-scroll="true"]::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.15);
    }
    
    [data-whatsapp-scroll="true"]::-webkit-scrollbar-thumb:hover {
        background-color: rgba(0, 0, 0, 0.25);
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .message-enter {
        animation: none;
    }
    
    [data-whatsapp-scroll="true"] {
        scroll-behavior: auto;
    }
}

/* Focus indicators for keyboard navigation */
.message-bubble:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 0.375rem;
}

/* Loading state optimizations */
.loading-older-messages {
    /* Prevent layout shifts */
    min-height: 3rem;
    
    /* Smooth appearance */
    transition: opacity 0.2s ease-in-out;
}

/* Ensure proper stacking context */
[data-whatsapp-scroll="true"] {
    position: relative;
    z-index: 1;
}

/* Optimize for different screen sizes */
@media (min-width: 1024px) {
    [data-whatsapp-scroll="true"]::-webkit-scrollbar {
        width: 6px;
    }
    
    .loading-older-messages {
        margin: 1rem;
        padding: 1rem;
    }
}
