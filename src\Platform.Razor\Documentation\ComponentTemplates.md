# Component Template Examples

## Optimized FormBase Component Template

### MyForm.razor
```razor
@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.MyFeature
@using Platform.Client.Common.Features.MyFeature
@page "/my-feature/form"
@page "/my-feature/form/{Id}"
@inherits FormBase<MyFormBusinessObject, MyFormViewModel, string, IMyFormDataService>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Compact Header - WhatsApp Style -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div class="flex items-center justify-between">
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
                @(string.IsNullOrEmpty(Id) ? "Add Item" : "Edit Item")
            </h1>
            <div class="flex items-center space-x-3">
                <button @onclick="() => NavigationManager.NavigateTo(\"/my-feature\")" 
                        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    Cancel
                </button>
                <button @onclick="SaveAsync" 
                        disabled="@IsWorking"
                        class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    @(IsWorking ? "Saving..." : "Save")
                </button>
            </div>
        </div>
    </div>

    <!-- Form Content -->
    <div class="max-w-2xl mx-auto px-4 py-6">
        @if (!string.IsNullOrEmpty(Error))
        {
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                <p class="text-red-800 dark:text-red-200">@Error</p>
            </div>
        }

        @if (SelectedItem != null)
        {
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <!-- Form Fields -->
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Name
                        </label>
                        <input @bind="SelectedItem.Name" 
                               class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Description
                        </label>
                        <textarea @bind="SelectedItem.Description" rows="4"
                                  class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>
                </div>
            </div>
        }
    </div>
</div>
```

### MyForm.razor.cs
```csharp
using Microsoft.AspNetCore.Components;
using DeepMessage.ServiceContracts.Features.MyFeature;
using Platform.Client.Common.Features.MyFeature;
using Platform.Razor.Services;

namespace Platform.Razor.Features.MyFeature.Form
{
    public partial class MyForm
    {
        // ✅ CORRECT: Only inject services not available in FormBase
        [Inject] private ICustomBusinessService CustomService { get; set; } = null!;
        
        // ❌ REMOVED: These are already available through FormBase inheritance:
        // - NavigationManager (use NavigationManager property)
        // - IJSRuntime (use JsRuntime property)
        // - ILocalStorageService (use StorageService property)
        // - IServiceScopeFactory (use ScopeFactory property)

        protected override async Task OnInitializedAsync()
        {
            // Use inherited services directly
            await StorageService.SetValue("last_visited", "/my-feature/form");
            await base.OnInitializedAsync();
        }

        protected override async Task OnAfterSaveAsync()
        {
            // Custom post-save logic
            await CustomService.NotifyItemSaved(SelectedItem.Id);
            
            // Use inherited NavigationManager
            NavigationManager.NavigateTo("/my-feature");
        }

        private async Task HandleCustomAction()
        {
            // Use inherited JsRuntime
            await JsRuntime.InvokeVoidAsync("console.log", "Custom action executed");
        }
    }
}
```

## Optimized ListingBase Component Template

### MyListing.razor
```razor
@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.MyFeature
@using Platform.Client.Common.Features.MyFeature
@page "/my-feature"
@inherits ListingBase<MyListingViewModel, MyListingBusinessObject, MyFilterViewModel, MyFilterBusinessObject, IMyListingDataService>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Compact Header - WhatsApp Style -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div class="flex items-center justify-between">
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">My Feature</h1>
            <div class="flex items-center space-x-3">
                <!-- Search -->
                <div class="relative">
                    <input type="text" 
                           @bind="FilterViewModel.SearchKey" 
                           @bind:event="oninput"
                           placeholder="Search..." 
                           class="block w-full pl-8 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white" />
                    <svg class="absolute left-2 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <!-- Add Button -->
                <button @onclick="() => NavigationManager.NavigateTo(\"/my-feature/form\")" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Add Item
                </button>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 py-6">
        @if (IsWorking)
        {
            <div class="flex items-center justify-center py-12">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        }
        else if (!string.IsNullOrEmpty(Error))
        {
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <p class="text-red-800 dark:text-red-200">@Error</p>
            </div>
        }
        else if (Items?.Count == 0)
        {
            <div class="text-center py-12">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">No items found</h3>
                <p class="text-gray-500 dark:text-gray-400 mt-2">Get started by adding your first item.</p>
                <button @onclick="() => NavigationManager.NavigateTo(\"/my-feature/form\")" 
                        class="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                    Add First Item
                </button>
            </div>
        }
        else
        {
            <!-- Items Grid -->
            <div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                @foreach (var item in Items)
                {
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                        <h3 class="font-medium text-gray-900 dark:text-white">@item.Name</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">@item.Description</p>
                        <div class="mt-4 flex space-x-2">
                            <button @onclick="() => NavigationManager.NavigateTo($\"/my-feature/form/{item.Id}\")" 
                                    class="text-blue-600 hover:text-blue-700 text-sm">
                                Edit
                            </button>
                            <button @onclick="() => HandleItemAction(item)" 
                                    class="text-green-600 hover:text-green-700 text-sm">
                                Action
                            </button>
                        </div>
                    </div>
                }
            </div>
        }
    </div>
</div>
```

### MyListing.razor.cs
```csharp
using Microsoft.AspNetCore.Components;
using DeepMessage.ServiceContracts.Features.MyFeature;
using Platform.Client.Common.Features.MyFeature;
using Platform.Razor.Services;

namespace Platform.Razor.Features.MyFeature.Listing
{
    public partial class MyListing
    {
        // ✅ CORRECT: Only inject services not available in ListingBase
        [Inject] private IDataSynchronizationService SyncService { get; set; } = null!;
        [Inject] private ICustomNotificationService NotificationService { get; set; } = null!;
        
        // ❌ REMOVED: These are already available through ListingBase inheritance:
        // - NavigationManager (use NavigationManager property)
        // - IJSRuntime (use JsRuntime property)
        // - ILocalStorageService (use StorageService property)
        // - IServiceScopeFactory (use ScopeFactory property)

        protected override async Task OnInitializedAsync()
        {
            // Use inherited services directly
            await StorageService.SetValue("last_visited", "/my-feature");
            
            // LoadItems() is automatically called by base class if LoadItemsOnInit is true
            await base.OnInitializedAsync();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await CheckAndPerformSync();
            }
            await base.OnAfterRenderAsync(firstRender);
        }

        private async Task HandleItemAction(MyListingViewModel item)
        {
            // Use inherited JsRuntime
            var confirmed = await JsRuntime.InvokeAsync<bool>("confirm", $"Perform action on {item.Name}?");
            if (confirmed)
            {
                await NotificationService.ShowSuccess($"Action performed on {item.Name}");
                await LoadItems(); // Refresh the list
            }
        }

        private async Task CheckAndPerformSync()
        {
            try
            {
                var userId = await StorageService.GetValue("user_id");
                if (!string.IsNullOrEmpty(userId))
                {
                    var syncRequired = await SyncService.IsSyncRequiredAsync(userId);
                    if (syncRequired)
                    {
                        var result = await SyncService.SynchronizeUserDataAsync(userId);
                        if (result.Success)
                        {
                            await LoadItems(); // Refresh after sync
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't break the UI
                Error = $"Sync error: {ex.Message}";
            }
        }
    }
}
```

## Key Optimization Points

### ✅ DO Use Inherited Services
```csharp
// Use these inherited properties directly:
NavigationManager.NavigateTo("/some-route");
await JsRuntime.InvokeVoidAsync("console.log", "message");
await StorageService.SetValue("key", "value");
using var scope = ScopeFactory.CreateScope();
```

### ❌ DON'T Inject Already Available Services
```csharp
// ❌ REDUNDANT - Remove these injections:
[Inject] private NavigationManager Navigation { get; set; }
[Inject] private IJSRuntime JSRuntime { get; set; }
[Inject] private ILocalStorageService StorageService { get; set; }
[Inject] private IServiceScopeFactory ScopeFactory { get; set; }
```

### ✅ DO Inject Component-Specific Services
```csharp
// ✅ CORRECT - Only inject services not in base classes:
[Inject] private IDataSynchronizationService SyncService { get; set; }
[Inject] private ICustomBusinessService BusinessService { get; set; }
[Inject] private IAuthenticationStateProvider AuthStateProvider { get; set; }
```

This template approach ensures optimal service injection patterns while maintaining consistency with the XAML application architecture and WhatsApp Business design patterns.
