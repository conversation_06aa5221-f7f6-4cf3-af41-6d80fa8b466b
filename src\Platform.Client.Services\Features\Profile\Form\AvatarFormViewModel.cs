﻿using DeepMessage.Framework.Core;
using System.ComponentModel.DataAnnotations;

namespace Platform.Client.Services.Features.Profile;
public class AvatarFormViewModel : ObservableBase
{
    public string Id { get; set; } = string.Empty;

    [Required(ErrorMessage = "Please describe your desired avatar")]
    public string? AvatarDescription { get; set; }

    /// <summary>
    /// Generated avatar image data (temporary storage)
    /// </summary>
    public string? GeneratedAvatarData { get; set; }

    /// <summary>
    /// Indicates if AI generation is in progress
    /// </summary>
    public bool IsGenerating { get; set; }

    /// <summary>
    /// Indicates if an avatar has been generated but not yet saved
    /// </summary>
    public bool HasGeneratedAvatar { get; set; }

    /// <summary>
    /// Indicates if saving the avatar to database is in progress
    /// </summary>
    public bool IsSaving { get; set; }

    public string? SuccessMessage { get; set; }
}
