﻿using System.Security.Claims;

namespace KPlatform.Framework.Core
{
    public interface IAuthenticatedUser
    {

        public string UserId { get; set; }
        public string ProfileName { get; set; }
        public string ImageUrl { get; set; }
      
        public string? Username { get; set; }

    }

    //public interface IAuthenticatedUserId
    //{ 
    //    public string UserId { get;}

    //    Task<string> GetCurrentUserIdAsync()
    //    {
    //        return Task.FromResult(UserId);
    //    }

    //}

    public static class ClaimsPrincipleExtensions
    {
        public static string? GetUserId(this ClaimsPrincipal principal)
        {
            var claim = principal.FindFirst(ClaimTypes.NameIdentifier);
            return claim?.Value;
        }

        public static string GetImage(this ClaimsPrincipal principal)
        {
            var claim = principal.FindFirst(ClaimTypes.UserData);
            if (claim is not null && !string.IsNullOrEmpty(claim.Value))
            {
                return claim.Value;
            }
            return string.Empty;
        }

        public static string GetUserName(this ClaimsPrincipal principal)
        {
            var claim = principal.FindFirst(ClaimTypes.Name);
            if (claim is not null && !string.IsNullOrEmpty(claim.Value))
            {
                return claim.Value;
            }
            return string.Empty;
        }  

        public static string? GetProfileName(this ClaimsPrincipal principal)
        {
            var claim = principal.FindFirst(ClaimTypes.GivenName);
            if (claim is not null && !string.IsNullOrEmpty(claim.Value))
            {
                return claim.Value;
            }
            return string.Empty;
        }
    }
}
