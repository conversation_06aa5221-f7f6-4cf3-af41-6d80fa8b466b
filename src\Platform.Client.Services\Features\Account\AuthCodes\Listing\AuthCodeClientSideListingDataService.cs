﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.MauiApp.Helpers;
using DeepMessage.ServiceContracts.Features.AuthCodes;
namespace Platform.Client.Services.Features.AuthCodes;
public class AuthCodeClientSideListingDataService : IAuthCodeListingDataService
{

	private readonly BaseHttpClient _httpClient;

	public AuthCodeClientSideListingDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<PagedDataList<AuthCodeListingBusinessObject>> GetPaginatedItems(AuthCodeFilterBusinessObject filterBusinessObject)
	{
		return await _httpClient.GetFromJsonAsync<PagedDataList<AuthCodeListingBusinessObject>>($"api/AuthCodeListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());
	}
}
