@page "/forgot-password"
@page "/reset-password-request"
@using System.ComponentModel.DataAnnotations

<!-- Main Container -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-6">
                <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-6 6h-2.5a2.5 2.5 0 01-2.5-2.5 2.5 2.5 0 012.5-2.5H16a2 2 0 002-2m-7 4v4m0 0H9m2 0h2m-2-4a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Forgot your password?</h2>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                No worries! Enter your email address and we'll send you a link to reset your password.
            </p>
        </div>

        @if (!emailSent)
        {
            <!-- Forgot Password Form -->
            <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg px-8 py-8 space-y-6">
                <EditForm Model="forgotPasswordModel" OnValidSubmit="HandleSubmit">
                    <DataAnnotationsValidator />
                    
                    <!-- Email Field -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Email Address
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                </svg>
                            </div>
                            <InputText @bind-Value="forgotPasswordModel.Email" 
                                       id="email"
                                       type="email"
                                       placeholder="Enter your email address"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" />
                        </div>
                        <ValidationMessage For="@(() => forgotPasswordModel.Email)" class="mt-1 text-sm text-red-600 dark:text-red-400" />
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit" 
                                disabled="@(isWorking || string.IsNullOrWhiteSpace(forgotPasswordModel.Email))"
                                class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                            @if (isWorking)
                            {
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span>Sending reset link...</span>
                            }
                            else
                            {
                                <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                    <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                </span>
                                <span>Send Reset Link</span>
                            }
                        </button>
                    </div>

                    <!-- Error Display -->
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                            <div class="flex">
                                <svg class="h-5 w-5 text-red-600 dark:text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                                    <p class="mt-1 text-sm text-red-700 dark:text-red-300">@errorMessage</p>
                                </div>
                            </div>
                        </div>
                    }
                </EditForm>
            </div>
        }
        else
        {
            <!-- Success Message -->
            <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg px-8 py-8 text-center">
                <div class="mx-auto h-16 w-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-6">
                    <svg class="h-8 w-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Check your email
                </h3>
                
                <p class="text-gray-600 dark:text-gray-400 mb-6">
                    We've sent a password reset link to <strong>@forgotPasswordModel.Email</strong>
                </p>
                
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <svg class="h-5 w-5 text-blue-600 dark:text-blue-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div class="text-sm text-blue-800 dark:text-blue-200">
                            <p class="font-medium">Didn't receive the email?</p>
                            <ul class="mt-2 list-disc list-inside space-y-1">
                                <li>Check your spam or junk folder</li>
                                <li>Make sure you entered the correct email address</li>
                                <li>The link will expire in 24 hours</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="space-y-3">
                    <button @onclick="ResendEmail" 
                            disabled="@(isWorking || resendCooldown > 0)"
                            class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                        @if (resendCooldown > 0)
                        {
                            <span>Resend in @resendCooldown seconds</span>
                        }
                        else if (isWorking)
                        {
                            <span>Sending...</span>
                        }
                        else
                        {
                            <span>Resend Email</span>
                        }
                    </button>
                    
                    <button @onclick="GoBackToForm"
                            class="w-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                        Try Different Email
                    </button>
                </div>
            </div>
        }

        <!-- Back to Sign In Link -->
        <div class="text-center">
            <p class="text-sm text-gray-600 dark:text-gray-400">
                Remember your password?
                <a href="/signin" class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200">
                    Back to sign in
                </a>
            </p>
        </div>
    </div>
</div>

@code {
    private ForgotPasswordModel forgotPasswordModel = new();
    private bool isWorking = false;
    private bool emailSent = false;
    private string errorMessage = string.Empty;
    private int resendCooldown = 0;
    private System.Timers.Timer? cooldownTimer;

    public class ForgotPasswordModel
    {
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;
    }

    private async Task HandleSubmit()
    {
        try
        {
            isWorking = true;
            errorMessage = string.Empty;
            StateHasChanged();

            // Simulate API call to send reset email
            await Task.Delay(2000); // Simulate network delay

            // In production, this would call a password reset service
            // For demo purposes, we'll always succeed
            emailSent = true;
            StartResendCooldown();
        }
        catch (Exception ex)
        {
            errorMessage = "Unable to send reset email. Please try again later.";
        }
        finally
        {
            isWorking = false;
            StateHasChanged();
        }
    }

    private async Task ResendEmail()
    {
        if (resendCooldown > 0) return;

        try
        {
            isWorking = true;
            StateHasChanged();

            // Simulate resending email
            await Task.Delay(1000);

            StartResendCooldown();
        }
        catch (Exception ex)
        {
            errorMessage = "Unable to resend email. Please try again.";
        }
        finally
        {
            isWorking = false;
            StateHasChanged();
        }
    }

    private void StartResendCooldown()
    {
        resendCooldown = 60; // 60 seconds cooldown
        cooldownTimer = new System.Timers.Timer(1000);
        cooldownTimer.Elapsed += (sender, e) =>
        {
            resendCooldown--;
            if (resendCooldown <= 0)
            {
                cooldownTimer?.Stop();
                cooldownTimer?.Dispose();
            }
            InvokeAsync(StateHasChanged);
        };
        cooldownTimer.Start();
    }

    private void GoBackToForm()
    {
        emailSent = false;
        errorMessage = string.Empty;
        forgotPasswordModel.Email = string.Empty;
    }

    public void Dispose()
    {
        cooldownTimer?.Stop();
        cooldownTimer?.Dispose();
    }
}
