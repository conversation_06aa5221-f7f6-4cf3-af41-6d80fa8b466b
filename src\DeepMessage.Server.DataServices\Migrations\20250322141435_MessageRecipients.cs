﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DeepMessage.Server.DataServices.Migrations
{
    /// <inheritdoc />
    public partial class MessageRecipients : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "DeliveredAt",
                table: "MessageRecipients",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<byte>(
                name: "DeliveryStatus",
                table: "MessageRecipients",
                type: "tinyint",
                nullable: false,
                defaultValue: (byte)0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DeliveredAt",
                table: "MessageRecipients");

            migrationBuilder.DropColumn(
                name: "DeliveryStatus",
                table: "MessageRecipients");
        }
    }
}
