﻿using DeepMessage.Server.DataServices.Data;
using DeepMessage.Server.DataServices.Helpers;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using FirebaseAdmin.Messaging;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System.Collections.Concurrent;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Channels;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;

namespace DeepMessage.Server.DataServices;

public class DeepChatHub : Hub
{
    private static ConcurrentDictionary<string, string> connectionIdCache = new ConcurrentDictionary<string, string>();
    private readonly IServiceScopeFactory scopeFactory;

    public ILogger<DeepChatHub> Logger { get; }

    public DeepChatHub(ILogger<DeepChatHub> logger, IServiceScopeFactory scopeFactory)
    {
        Logger = logger;
        this.scopeFactory = scopeFactory;
    }

    public override async Task OnConnectedAsync()
    {
        var userId = Context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null)
        {
            Logger.LogWarning("Unauthorized connection attempt from {ConnectionId}", Context.ConnectionId);
            await Clients.Client(Context.ConnectionId).SendAsync("Logout");
            Context.Abort();
            return;
        }

        Logger.LogInformation("User connected - ConnectionId: {ConnectionId}, UserId: {UserId}, UserName: {UserName}",
            Context.ConnectionId, userId, Context.User.Identity?.Name);

        // Handle multiple connections for same user
        if (connectionIdCache.TryGetValue(userId, out var existingConnectionId))
        {
            Logger.LogInformation("User {UserId} already has connection {ExistingConnectionId}, replacing with {NewConnectionId}",
                userId, existingConnectionId, Context.ConnectionId);
        }

        connectionIdCache.AddOrUpdate(userId, Context.ConnectionId, (key, oldValue) => Context.ConnectionId);

        await base.OnConnectedAsync();

        try
        {
            await SendPendingMessages(userId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error sending pending messages to user {UserId}", userId);
        }
    }

    private async Task SendPendingMessages(string userId)
    {
        var scope = scopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var pendingMessages = (from m in context.Messages
                               from recipients in context.MessageRecipients.Where(x => x.MessageId == m.Id)
                               from user in context.Users.Where(x => x.Id == recipients.RecipientId)
                               where recipients.MessageDeliveryStatus <= MessageDeliveryStatus.SentToEndUserViaSignalR
                               && recipients.RecipientId == userId
                               orderby m.Id
                               select new ChatMessagesSyncFormBusinessObject
                               {
                                   Id = m.Id,
                                   MessageRecepientId = recipients.Id,
                                   MessageRecepientUserName = user.UserName,
                                   ConversationId = m.ConversationId,
                                   SenderId = m.SenderId,
                                   CreatedAt = m.CreatedAt,
                                   DeletedAt = m.DeletedAt,
                                   DisappearAfter = m.DisappearAfter,
                                   DisappearAt = m.DisappearAt,
                                   EditedAt = m.EditedAt,
                                   IsDeleted = m.IsDeleted,
                                   IsEdited = m.IsEdited,
                                   IsEphemeral = m.IsEphemeral,
                                   MessageRecipients = m.Recipients.Select(r => new MessageRecipientSyncFormBusinessObject
                                   {
                                       Id = r.Id,
                                       MessageId = r.MessageId,
                                       RecipientId = r.RecipientId,
                                       EncryptedContent = r.EncryptedContent,
                                       DeliveryStatus = r.MessageDeliveryStatus,
                                       DeliveryStatusTime = r.DeliveryStatusTime
                                   }).ToList(),
                               }).ToList();
        pendingMessages = pendingMessages.DistinctBy(x => x.Id).ToList();
        foreach (var message in pendingMessages)
        {
            await SendMessageAsync(userId, message.Id, JsonSerializer.Serialize(message));
        }

        var utcNow = DateTime.UtcNow;

        var messageIds = pendingMessages.Select(x => x.Id).ToList();
        await context.Messages.Where(x => messageIds.Contains(x.Id) && x.DeliveryStatus < MessageDeliveryStatus.SentToEndUserViaSignalR)
                      .ExecuteUpdateAsync(x => x
                      .SetProperty(p => p.DeliveryStatus, MessageDeliveryStatus.SentToEndUserViaSignalR)
                      .SetProperty(p => p.DeliveryStatusTime, utcNow));

        var messageRecipients = pendingMessages.Select(x => x.MessageRecepientId).ToList();
        await context.MessageRecipients.Where(x => messageRecipients.Contains(x.Id))
            .ExecuteUpdateAsync(x => x
                        .SetProperty(p => p.MessageDeliveryStatus, MessageDeliveryStatus.SentToEndUserViaSignalR)
                        .SetProperty(p => p.DeliveryStatusTime, utcNow));
    }

    public async Task AcknowledgeMessage(string messageId, string targetUserId, MessageDeliveryStatus deliveryStatus, DateTime timeStamp)
    {
        var userId = Context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            Logger.LogWarning("Received Ack without valid user context.");
            return;
        }
        Logger.LogDebug("Ack from {0}, messageId {messageId}, targetUserId: {targetUserId}, deliveryStatus: {deliveryStatus}", userId, messageId, targetUserId, deliveryStatus);
        var scope = scopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var messageDispatcher = scope.ServiceProvider.GetRequiredService<MessageDispatcher>();

        // ✅ FIXED: Get the MessageRecipient ID first for proper tracking
        var messageRecipient = await context.MessageRecipients
            .Where(x => x.RecipientId == userId && x.MessageId == messageId)
            .FirstOrDefaultAsync();

        if (messageRecipient != null)
        {
            await context.MessageRecipients.Where(x => x.Id == messageRecipient.Id)
                  .ExecuteUpdateAsync(x => x.SetProperty(y => y.MessageDeliveryStatus, deliveryStatus)
                  .SetProperty(x => x.DeliveryAcknowledgementStatus, AcknowledgementStatus.SentToMessageServer)
                  .SetProperty(y => y.DeliveryStatusTime, timeStamp));

            await context.Messages.Where(x => x.Id == messageId)
                .ExecuteUpdateAsync(x => x.SetProperty(y => y.DeliveryStatus, deliveryStatus)
                .SetProperty(y => y.DeliveryStatusTime, timeStamp));

            messageDispatcher.DispatchMessageUpdate(new ChatMessageUpdate
            {
                MessageId = messageId,
                TargetUserId = targetUserId,
                DeliveryStatusTime = timeStamp,
                SenderId = userId,
                DeliveryStatus = deliveryStatus,
                MessageRecipientId = messageRecipient.Id
            });
        }
    }




    public async Task<bool> SendMessageUpdateAsync(string userId, string messageId, MessageDeliveryStatus deliveryStatus)
    {
        try
        {
            if (string.IsNullOrEmpty(userId))
            {
                Logger.LogWarning("Cannot send message: userId is null or empty");
                return false;
            }

            var connectionId = connectionIdCache.ContainsKey(userId) ? connectionIdCache[userId] : string.Empty;

            if (string.IsNullOrEmpty(connectionId))
            {
                Logger.LogDebug("User   (ID: {UserId}) is offline - message {MessageId} will be queued", userId, messageId);
                return false;
            }

            if (Clients == null)
            {
                Logger.LogError("SignalR Clients is null - cannot send message {MessageId}", messageId);
                return false;
            }

            var client = Clients.Client(connectionId);
            if (client == null)
            {
                Logger.LogWarning("SignalR client not found for user (ID: {UserId}), connection {ConnectionId}", userId, connectionId);
                // Remove stale connection from cache
                connectionIdCache.TryRemove(userId, out _);
                return false;
            }

            await client.SendAsync(SignalRMethod.OnFeedUpdate.ToString(), userId,messageId, deliveryStatus);
            Logger.LogDebug("Message {MessageId} sent to {UserId} via {Method} on connection {ConnectionId} - {deliveryStatus}", messageId, userId, SignalRMethod.OnFeedUpdate,  deliveryStatus, connectionId);
            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error sending message {MessageId} to user (ID: {UserId}) via SignalR", messageId, userId);
            // Remove potentially stale connection
            connectionIdCache.TryRemove(userId, out _);
            return false;
        }
    }

    public async Task<bool> SendMessageAsync(string userId, string messageId, string messageJson)
    {
        try
        {
            if (string.IsNullOrEmpty(userId))
            {
                Logger.LogWarning("Cannot send message: userId is null or empty");
                return false;
            }

            var connectionId = connectionIdCache.ContainsKey(userId) ? connectionIdCache[userId] : string.Empty;

            if (string.IsNullOrEmpty(connectionId))
            {
                Logger.LogDebug("User   (ID: {UserId}) is offline - message {MessageId} will be queued", userId, messageId);
                return false;
            }

            if (Clients == null)
            {
                Logger.LogError("SignalR Clients is null - cannot send message {MessageId}", messageId);
                return false;
            }

            var client = Clients.Client(connectionId);
            if (client == null)
            {
                Logger.LogWarning("SignalR client not found for user (ID: {UserId}), connection {ConnectionId}", userId, connectionId);
                // Remove stale connection from cache
                connectionIdCache.TryRemove(userId, out _);
                return false;
            }

            await client.SendAsync(SignalRMethod.OnNewFeed.ToString(), userId, messageJson);
            Logger.LogDebug("Message {MessageId} sent to (ID: {UserId}) via {Method} on connection {ConnectionId}",
                messageId, userId, SignalRMethod.OnNewFeed, connectionId);
            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error sending message {MessageId} to user (ID: {UserId}) via SignalR", messageId, userId);
            // Remove potentially stale connection
            connectionIdCache.TryRemove(userId, out _);
            return false;
        }
    }

    public override async Task OnDisconnectedAsync(Exception? e)
    {
        var userId = Context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (!string.IsNullOrEmpty(userId))
        {
            // Only remove if this is the current connection for the user
            if (connectionIdCache.TryGetValue(userId, out var cachedConnectionId) &&
                cachedConnectionId == Context.ConnectionId)
            {
                connectionIdCache.TryRemove(userId, out _);
                Logger.LogInformation("User disconnected - ConnectionId: {ConnectionId}, UserId: {UserId}, UserName: {UserName}, Reason: {Reason}",
                    Context.ConnectionId, userId, Context.User.Identity?.Name, e?.Message ?? "Normal disconnect");
            }
            else
            {
                Logger.LogDebug("Connection {ConnectionId} for user {UserId} was already replaced, not removing from cache",
                    Context.ConnectionId, userId);
            }
        }
        else
        {
            Logger.LogWarning("Disconnected connection {ConnectionId} had no valid user ID", Context.ConnectionId);
            //logout user
            await Clients.Client(Context.ConnectionId).SendAsync("Logout");
        }

        await base.OnDisconnectedAsync(e);
    }

}