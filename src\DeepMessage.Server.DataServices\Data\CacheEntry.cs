﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DeepMessage.Server.DataServices.Data
{ 
    public class CacheEntry
    {
        [Key]
        [MaxLength(449)]
        public string Id { get; set; } = string.Empty;

        [Required]
        public byte[] Value { get; set; } = Array.Empty<byte>();

        [Required]
        public DateTimeOffset ExpiresAtTime { get; set; }

        public long? SlidingExpirationInSeconds { get; set; }

        public DateTimeOffset? AbsoluteExpiration { get; set; }
    }
}
