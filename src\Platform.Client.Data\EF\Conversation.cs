﻿using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DeepMessage.Client.Common.Data
{
    public class Conversation
    {
        [Key, StringLength(450)]
        public string Id { get; set; } = null!;

        public string Title { get; set; } = null!;
        public ConversationType Type { get; set; }

        public bool IsDeleted { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public byte SyncStatus { get; set; }

        // Navigation
        public ICollection<ConversationParticipant> Participants { get; set; } = new List<ConversationParticipant>();
        public ICollection<Message> Messages { get; set; } = new List<Message>();
    }

   

    public class ConversationParticipant
    {
        [Key, StringLength(450)]
        public string Id { get; set; } = null!;

        [StringLength(450)]
        public string ConversationId { get; set; } = null!;

        public Conversation Conversation { get; set; } = null!;

        [StringLength(450)]
        public string UserId { get; set; } = null!; 

        public bool IsAdmin { get; set; } = false;

        public DateTime JoinedAt { get; set; } = DateTime.UtcNow;

        public byte SyncStatus { get; set; }
    }
     
    public class Message
    {
        [Key, StringLength(450)] 
        public string Id { get; set; } = null!;

        [StringLength(450)]
        public string ConversationId { get; set; } = null!;
        public Conversation Conversation { get; set; } = null!;

        public string SenderId { get; set; } = null!;  
         

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public byte SyncStatus { get; set; }

        // Soft-deletion
        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedAt { get; set; }

        // If you want to track edits:
        public bool IsEdited { get; set; } = false;
        public DateTime? EditedAt { get; set; }

        // Disappearing (ephemeral) message fields
        public bool IsEphemeral { get; set; } = false;
        public TimeSpan? DisappearAfter { get; set; }
        public DateTime? DisappearAt { get; set; }

        public MessageDeliveryStatus DeliveryStatus { get; set; }

        public DateTime? DeliveryStatusTime { get; set; }

        // Attachments
        public ICollection<MessageAttachment> Attachments { get; set; } = new List<MessageAttachment>();
 
        public ICollection<MessageRecipient> Recipients { get; set; } = new List<MessageRecipient>();
    }
 
    public class MessageRecipient
    {
        [Key, StringLength(450)] 
        public string Id { get; set; } = null!;

        [StringLength(450)]
        public string MessageId { get; set; }  = null!;

        public Message Message { get; set; } = null!;

        public string RecipientId { get; set; } = null!; 
 
        public string EncryptedContent { get; set; } = null!;

        public MessageDeliveryStatus MessageDeliveryStatus { get; set; }

        // Is it read by the user?
        public bool IsRead { get; set; } = false;
        public DateTime? ReadAt { get; set; }

        public AcknowledgementStatus DeliveryAcknowledgementStatus { get; set; }

        public AcknowledgementStatus ReadAcknowledgementStatus { get; set; }

        // If ephemeral messages vanish after the user reads them, 
        // you might store user-specific ephemeral timers here as well.
        // ...
    }

    /// <summary>
    /// Supports pictures, documents, audio, or other file attachments. 
    /// You can store the actual file data in a BLOB column or just keep a URL/path reference.
    /// </summary>
    public class MessageAttachment
    {
        [Key, StringLength(450)]
        public string Id { get; set; } = null!;

        [StringLength(450)] 
        public string MessageId { get; set; } = null!;
        
        public Message Message { get; set; } = null!;

        // E.g. "Image", "Document", "Audio"...
        public string AttachmentType { get; set; } = string.Empty;

        // The filename or caption:
        public string FileName { get; set; } = null!;

        // For referencing the file location (could be local path, cloud storage, etc.).
        public string FileUrl { get; set; } = null!;

        // Optionally store the file size, mime type, metadata, etc.
        public long? FileSizeBytes { get; set; }
        public string? MimeType { get; set; }

        // Thumbnail data for images (smaller compressed version)
        public byte[]? ThumbnailData { get; set; }

        // Soft-deletion or ephemeral logic if needed
        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedAt { get; set; }

        public byte SyncStatus { get; set; }
    }

}
