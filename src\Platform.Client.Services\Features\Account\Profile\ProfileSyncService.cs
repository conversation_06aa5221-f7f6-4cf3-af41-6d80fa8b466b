using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace Platform.Client.Services.Features.Account;

public interface IProfileSyncService
{
    Task SyncUserProfileAsync(string userId, string username);
}

public class ProfileSyncService : IProfileSyncService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<ProfileSyncService> _logger;

    public ProfileSyncService(IServiceScopeFactory scopeFactory, ILogger<ProfileSyncService> logger)
    {
        _scopeFactory = scopeFactory;
        _logger = logger;
    }

    public async Task SyncUserProfileAsync(string userId, string username)
    {
        try
        {
            using var scope = _scopeFactory.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            
            // Check if user exists in local database
            var localUser = await context.ApplicationUsers.FirstOrDefaultAsync(u => u.Id == userId);
            
            // Try to get profile data from online service (with fallback)
            var profileData = await GetProfileDataFromOnlineService(userId);
            
            if (localUser == null)
            {
                // Create new user in local database
                localUser = new ApplicationUser
                {
                    Id = userId,
                    NickName = username,
                    Hash = "dummy", // Required field
                    AvatarData = profileData?.AvatarData,
                    AvatarDescription = profileData?.AvatarDescription
                };
                context.ApplicationUsers.Add(localUser);
                _logger.LogInformation("Created new local user profile for {UserId}", userId);
            }
            else
            {
                // Update existing user with latest profile data
                localUser.NickName = username;
                if (profileData != null)
                {
                    if (!string.IsNullOrEmpty(profileData.AvatarData))
                    {
                        localUser.AvatarData = profileData.AvatarData;
                    }
                    if (!string.IsNullOrEmpty(profileData.AvatarDescription))
                    {
                        localUser.AvatarDescription = profileData.AvatarDescription;
                    }
                }
                context.ApplicationUsers.Update(localUser);
                _logger.LogInformation("Updated local user profile for {UserId}", userId);
            }

            await context.SaveChangesAsync();
            _logger.LogInformation("Profile sync completed successfully for user {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing profile data for user {UserId}", userId);
            // Don't throw - profile sync failure shouldn't prevent sign-in
        }
    }

    private async Task<ProfileFormBusinessObject?> GetProfileDataFromOnlineService(string userId)
    {
        try
        {
            using var scope = _scopeFactory.CreateScope();
            
            // Try to get the online service (keyed service)
            var onlineProfileService = scope.ServiceProvider.GetKeyedService<IProfileFormDataService>("online");
            if (onlineProfileService != null)
            {
                var profileData = await onlineProfileService.GetItemByIdAsync(userId);
                if (profileData != null)
                {
                    _logger.LogInformation("Retrieved profile data from online service for user {UserId}", userId);
                    return profileData;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve profile data from online service for user {UserId}", userId);
        }

        return null;
    }
}
