<?xml version="1.0" encoding="utf-8" ?>
<local:FriendProfileFormViewBase
    x:Class="Platform.Client.Common.Features.Friends.FriendProfileFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Friends"
    Title="Edit Friend"
    x:DataType="local:FriendProfileFormViewBase"
    Background="{StaticResource OverlayColor}"
    IsBusy="True">

    <Grid Padding="16" RowDefinitions="1*, Auto, 2*">

        <ScrollView Grid.Row="1">
            <Border
                x:Name="MainBorder"
                Padding="0"
                Background="{StaticResource CardBackgroundColor}"
                StrokeThickness="0">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="8" />
                </Border.StrokeShape>

                <Grid Margin="16,8" RowDefinitions="Auto, Auto, Auto">


                    <!--  Profile Display Section  -->
                    <Border
                        Margin="0,0,0,0"
                        BackgroundColor="{AppThemeBinding Light=White,
                                                          Dark={StaticResource Gray700}}"
                        StrokeThickness="0">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="0" />
                        </Border.StrokeShape>
                        <Grid
                            Padding="16"
                            ColumnDefinitions="Auto,*"
                            RowDefinitions="Auto">

                            <!--  Avatar Display  -->
                            <Border
                                Grid.Column="0"
                                BackgroundColor="{Binding SelectedItem.AvatarData, Converter={StaticResource StringToBoolConverter}, ConverterParameter='Transparent|#E5E7EB'}"
                                HeightRequest="80"
                                Stroke="{AppThemeBinding Light={StaticResource Gray300},
                                                         Dark={StaticResource Gray500}}"
                                StrokeThickness="2"
                                VerticalOptions="Start"
                                WidthRequest="80">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="50" />
                                </Border.StrokeShape>
                                <Grid>
                                    <Image
                                        Aspect="AspectFill"
                                        IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource StringToBoolConverter}}"
                                        Source="{Binding SelectedItem.AvatarData}" />
                                    <Label
                                        FontAttributes="Bold"
                                        FontSize="24"
                                        HorizontalOptions="Center"
                                        IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource InverseStringToBoolConverter}}"
                                        Text="{Binding SelectedItem.FriendName, Converter={StaticResource InitialsConverter}}"
                                        TextColor="{AppThemeBinding Light={StaticResource Gray600},
                                                                    Dark={StaticResource Gray500}}"
                                        VerticalOptions="Center" />
                                </Grid>
                            </Border>

                            <!--  Name and Info  -->
                            <VerticalStackLayout
                                Grid.Column="1"
                                Margin="16,0,0,0"
                                Spacing="4"
                                VerticalOptions="Center">
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="20"
                                    LineBreakMode="TailTruncation"
                                    Text="{Binding SelectedItem.FriendName, TargetNullValue='No Name'}"
                                    TextColor="{AppThemeBinding Light=#111827,
                                                                Dark={StaticResource Gray300}}" />
                                <Label
                                    FontSize="14"
                                    Text="{Binding SelectedItem.Tagline, TargetNullValue='No tagline'}"
                                    TextColor="{AppThemeBinding Light=#6B7280,
                                                                Dark={StaticResource Gray400}}" />
                            </VerticalStackLayout>
                        </Grid>
                    </Border>

                    <!--  Form Fields Section  -->
                    <VerticalStackLayout
                        Grid.Row="1"
                        Margin="0,0,0,0"
                        Padding="16"
                        BackgroundColor="{AppThemeBinding Light=White,
                                                          Dark={StaticResource Gray700}}"
                        Spacing="24">

                        <!--  Avatar Selection  -->
                        <VerticalStackLayout Spacing="4">
                            <Label
                                FontAttributes="Bold"
                                FontSize="16"
                                Text="Select Avatar"
                                TextColor="{AppThemeBinding Light=#374151,
                                                            Dark={StaticResource Gray300}}" />

                            <Picker
                                Title="Choose an avatar..."
                                FontSize="14"
                                ItemsSource="{Binding AvatarOptions}"
                                SelectedItem="{Binding SelectedItem.AvatarData}"
                                TextColor="{AppThemeBinding Light=#374151,
                                                            Dark={StaticResource Gray300}}"
                                TitleColor="{AppThemeBinding Light=#9CA3AF,
                                                             Dark={StaticResource Gray500}}" />
                            <Label
                                FontSize="12"
                                Text="Choose from predefined avatar images"
                                TextColor="{AppThemeBinding Light=#6B7280,
                                                            Dark={StaticResource Gray400}}" />

                        </VerticalStackLayout>

                        <!--  Friend Name  -->
                        <VerticalStackLayout Spacing="4">
                            <Label
                                FontAttributes="Bold"
                                FontSize="16"
                                Text="Friend Name"
                                TextColor="{AppThemeBinding Light=#374151,
                                                            Dark={StaticResource Gray300}}" />

                            <Entry
                                BackgroundColor="Transparent"
                                FontSize="16"
                                Placeholder="Enter friend's name"
                                PlaceholderColor="{AppThemeBinding Light=#9CA3AF,
                                                                   Dark={StaticResource Gray500}}"
                                Text="{Binding SelectedItem.FriendName}"
                                TextColor="{AppThemeBinding Light=#374151,
                                                            Dark={StaticResource Gray300}}" />
                            <Label
                                Margin="0,0,0,8"
                                FontSize="12"
                                LineBreakMode="WordWrap"
                                Text="This is how your friend will appear in your contact list"
                                TextColor="{AppThemeBinding Light=#6B7280,
                                                            Dark={StaticResource Gray400}}" />
                        </VerticalStackLayout>

                        <!--  Tagline/Description  -->
                        <VerticalStackLayout Spacing="4">
                            <Label
                                FontAttributes="Bold"
                                FontSize="16"
                                Text="Tagline"
                                TextColor="{AppThemeBinding Light=#374151,
                                                            Dark={StaticResource Gray300}}" />

                            <Editor
                                AutoSize="TextChanges"
                                BackgroundColor="Transparent"
                                FontSize="16"
                                MaxLength="500"
                                Placeholder="Enter a tagline or description..."
                                PlaceholderColor="{AppThemeBinding Light=#9CA3AF,
                                                                   Dark={StaticResource Gray500}}"
                                Text="{Binding SelectedItem.Tagline}"
                                TextColor="{AppThemeBinding Light=#374151,
                                                            Dark={StaticResource Gray300}}" />
                            <Label
                                Margin="0,0,0,8"
                                FontSize="12"
                                Text="Add a personal note or description for this friend"
                                TextColor="{AppThemeBinding Light=#6B7280,
                                                            Dark={StaticResource Gray400}}" />
                        </VerticalStackLayout>

                        <!--  Loading Indicator  -->
                        <ActivityIndicator
                            Margin="0,16"
                            IsRunning="{Binding IsWorking}"
                            IsVisible="{Binding IsWorking}"
                            Color="{AppThemeBinding Light=#004f98,
                                                    Dark={StaticResource Gray300}}" />

                    </VerticalStackLayout>

                    <Button
                        Grid.Row="2"
                        Margin="48,16"
                        Command="{Binding SaveCommand}"
                        IsEnabled="{Binding IsWorking, Converter={StaticResource InverseBoolConverter}}"
                        Text="{Binding IsWorking, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Saving...|Save Changes'}" />


                    <!--  Error Message  -->
                    <Border
                        Margin="16,16,16,0"
                        BackgroundColor="#FEF2F2"
                        IsVisible="{Binding HasError}"
                        Stroke="#FECACA"
                        StrokeThickness="1">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="8" />
                        </Border.StrokeShape>
                        <Grid Padding="12" ColumnDefinitions="Auto,*">
                            <Image
                                Grid.Column="0"
                                Margin="0,2,8,0"
                                HeightRequest="20"
                                VerticalOptions="Start"
                                WidthRequest="20">
                                <Image.Source>
                                    <FontImageSource
                                        FontFamily="Jelly"
                                        Glyph="&#xf06a;"
                                        Size="20"
                                        Color="#DC2626" />
                                </Image.Source>
                            </Image>
                            <Label
                                Grid.Column="1"
                                FontSize="14"
                                Text="{Binding Error}"
                                TextColor="#DC2626" />
                        </Grid>
                    </Border>

                    <Button
                        BackgroundColor="Transparent"
                        CornerRadius="0"
                        HorizontalOptions="End" Clicked="Button_Clicked"
                        VerticalOptions="Start"
                        WidthRequest="24">
                        <Button.ImageSource>
                            <FontImageSource
                                FontFamily="Jelly"
                                Glyph="&#xf057;"
                                Size="20"
                                Color="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray300}}" />
                        </Button.ImageSource>
                    </Button>
                </Grid>

            </Border>
        </ScrollView>
    </Grid>
</local:FriendProfileFormViewBase>
