﻿using Platform.Framework.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Client.Services
{
    public class LocalStorage : ILocalStorageService
    {
        public async Task<string> GetValue([CallerMemberName] string memberName = "")
        {
            return await SecureStorage.GetAsync(memberName) ?? string.Empty;
        }

        public async Task RemoveValue([CallerMemberName] string memberName = "")
        {
            await SecureStorage.SetAsync(memberName, string.Empty);
        }

        public async Task SetValue(string value, [CallerMemberName] string memberName = "")
        {
            await SecureStorage.SetAsync(memberName, value);
        }
    }
}
