﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DeepMessage.Client.Common.Migrations
{
    /// <inheritdoc />
    public partial class AvatarFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FriendRequests");

            migrationBuilder.AddColumn<string>(
                name: "AvatarData",
                table: "Friendships",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AvatarDescription",
                table: "Friendships",
                type: "TEXT",
                maxLength: 450,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AvatarData",
                table: "ApplicationUsers",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AvatarDescription",
                table: "ApplicationUsers",
                type: "TEXT",
                maxLength: 450,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AvatarData",
                table: "Friendships");

            migrationBuilder.DropColumn(
                name: "AvatarDescription",
                table: "Friendships");

            migrationBuilder.DropColumn(
                name: "AvatarData",
                table: "ApplicationUsers");

            migrationBuilder.DropColumn(
                name: "AvatarDescription",
                table: "ApplicationUsers");

            migrationBuilder.CreateTable(
                name: "FriendRequests",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 450, nullable: false),
                    ApprovedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    CancelledAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ExpiresAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    PinCode = table.Column<string>(type: "TEXT", maxLength: 450, nullable: true),
                    RejectedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Status = table.Column<int>(type: "INTEGER", nullable: false),
                    SyncStatus = table.Column<byte>(type: "INTEGER", nullable: false),
                    UserId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FriendRequests", x => x.Id);
                });
        }
    }
}
