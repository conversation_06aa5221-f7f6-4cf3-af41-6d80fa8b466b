using DeepMessage.Server.DataServices.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace DeepMessage.Tests.Services
{
    public class SecurityServiceTests
    {
        private readonly SecurityService _securityService;
        private readonly Mock<ILogger<SecurityService>> _mockLogger;

        public SecurityServiceTests()
        {
            _mockLogger = new Mock<ILogger<SecurityService>>();
            _securityService = new SecurityService(_mockLogger.Object);
        }

        [Theory]
        [InlineData("Hello World", "Hello World")]
        [InlineData("<script>alert('xss')</script>", "&lt;script&gt;alert(&#39;xss&#39;)&lt;/script&gt;")]
        [InlineData("  Test  ", "Test")]
        [InlineData("", "")]
        [InlineData(null, "")]
        public void SanitizeInput_ShouldSanitizeCorrectly(string input, string expected)
        {
            // Act
            var result = _securityService.SanitizeInput(input);

            // Assert
            result.Should().Be(expected);
        }

        [Theory]
        [InlineData("Hello World", true)]
        [InlineData("", false)]
        [InlineData(null, false)]
        [InlineData("   ", false)]
        [InlineData("<script>alert('xss')</script>", false)]
        [InlineData("javascript:alert('xss')", false)]
        public void ValidateMessageContent_ShouldValidateCorrectly(string content, bool expected)
        {
            // Act
            var result = _securityService.ValidateMessageContent(content);

            // Assert
            result.Should().Be(expected);
        }

        [Theory]
        [InlineData("<EMAIL>", true)]
        [InlineData("invalid-email", false)]
        [InlineData("", false)]
        [InlineData(null, false)]
        [InlineData("test@", false)]
        [InlineData("@example.com", false)]
        public void ValidateEmail_ShouldValidateCorrectly(string email, bool expected)
        {
            // Act
            var result = _securityService.ValidateEmail(email);

            // Assert
            result.Should().Be(expected);
        }

        [Theory]
        [InlineData("+1234567890", true)]
        [InlineData("1234567890", true)]
        [InlineData("invalid-phone", false)]
        [InlineData("", false)]
        [InlineData(null, false)]
        [InlineData("123", false)]
        public void ValidatePhoneNumber_ShouldValidateCorrectly(string phoneNumber, bool expected)
        {
            // Act
            var result = _securityService.ValidatePhoneNumber(phoneNumber);

            // Assert
            result.Should().Be(expected);
        }

        [Fact]
        public void HashPassword_ShouldCreateValidHash()
        {
            // Arrange
            var password = "TestPassword123!";

            // Act
            var hash = _securityService.HashPassword(password);

            // Assert
            hash.Should().NotBeNullOrEmpty();
            hash.Length.Should().BeGreaterThan(0);
        }

        [Fact]
        public void VerifyPassword_ShouldVerifyCorrectly()
        {
            // Arrange
            var password = "TestPassword123!";
            var hash = _securityService.HashPassword(password);

            // Act & Assert
            _securityService.VerifyPassword(password, hash).Should().BeTrue();
            _securityService.VerifyPassword("WrongPassword", hash).Should().BeFalse();
        }

        [Fact]
        public void GenerateSecureToken_ShouldGenerateUniqueTokens()
        {
            // Act
            var token1 = _securityService.GenerateSecureToken();
            var token2 = _securityService.GenerateSecureToken();

            // Assert
            token1.Should().NotBeNullOrEmpty();
            token2.Should().NotBeNullOrEmpty();
            token1.Should().NotBe(token2);
        }

        [Theory]
        [InlineData("123e4567-e89b-12d3-a456-************", true)]
        [InlineData("invalid-guid", false)]
        [InlineData("", false)]
        [InlineData(null, false)]
        public void IsValidUserId_ShouldValidateCorrectly(string userId, bool expected)
        {
            // Act
            var result = _securityService.IsValidUserId(userId);

            // Assert
            result.Should().Be(expected);
        }
    }
}
