﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup> 
    <PackageReference Include="Microsoft.AspNetCore.Components" Version="9.0.4" /> 
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.4" /> 
    <PackageReference Include="Microsoft.JSInterop" Version="9.0.4" /> 
    <PackageReference Include="PubSub" Version="4.0.2" /> 
    <PackageReference Include="SkiaSharp" Version="3.119.0" />
	  <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
  </ItemGroup>

</Project>
