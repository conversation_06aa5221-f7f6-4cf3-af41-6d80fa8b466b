using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Friends;
using Platform.Client.Services.Features.Friends;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.ObjectModel;
using Platform.Framework.Core;

namespace Platform.Client.Common.Features.Friends;

public class FriendProfileFormViewBase : FormBaseMaui<FriendProfileFormBusinessObject, FriendProfileFormViewModel, string, IFriendProfileFormDataService>
{
    public FriendProfileFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }

    public ObservableCollection<string> AvatarOptions { get; set; }


}

public partial class FriendProfileFormView : FriendProfileFormViewBase
{
    public FriendProfileFormView(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
        InitializeComponent();
        AvatarOptions = new ObservableCollection<string>();
        for (int i = 1; i <= 12; i++)
        {
            AvatarOptions.Add($"a{i}.svg");
        }

        BindingContext = this;

        // Set initial state for entrance animation
        var mainBorder = this.FindByName<Border>("MainBorder");
        if (mainBorder != null)
        {
            this.Opacity = 0;    
            mainBorder.Scale = 0.9; 
        }
    }

    public override async Task OnAfterSaveAsync(string key)
    {
        if (SelectedItem != null)
        {
            // Notify FriendsListing to refresh after friend profile update
            PubSub.Hub.Default.Publish("FriendDataUpdated");

            await MainThread.InvokeOnMainThreadAsync(async () =>
            {
                // Animate exit before closing modal
                if (this is FriendProfileFormView friendProfileFormView)
                {
                    await friendProfileFormView.AnimateExit();
                }
                await Navigation.PopModalAsync(false);
            });
        }

    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        // Animate entrance
        await AnimateEntrance();
    }

    /// <summary>
    /// Animates the entrance of the edit friend modal
    /// </summary>
    private async Task AnimateEntrance()
    {
        try
        {
            var mainBorder = this.FindByName<Border>("MainBorder");
            if (mainBorder != null)
            {
                // Small delay to ensure the page is loaded
                await Task.Delay(100);

                // Animate entrance with fade, scale, and slide up
                var fadeTask = this.FadeTo(1, 500, Easing.CubicOut);
                var scaleTask = mainBorder.ScaleTo(1, 500, Easing.CubicOut);

                await Task.WhenAll(fadeTask, scaleTask);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Entrance animation failed: {ex.Message}");
            // Ensure visibility if animation fails
            var mainBorder = this.FindByName<Border>("MainBorder");
            if (mainBorder != null)
            {
                mainBorder.Opacity = 1;
                mainBorder.Scale = 1;
                mainBorder.TranslationY = 0;
            }
        }
    }

    /// <summary>
    /// Animates the exit of the edit friend modal
    /// </summary>
    public async Task AnimateExit()
    {
        try
        {
            var mainBorder = this.FindByName<Border>("MainBorder");
            if (mainBorder != null)
            {
                // Animate the main content with exit effects
                var fadeTask = mainBorder.FadeTo(0, 400, Easing.CubicOut);
                var scaleTask = mainBorder.ScaleTo(0.9, 400, Easing.CubicOut);
                var translateTask = mainBorder.TranslateTo(0, 50, 400, Easing.CubicOut);

                // Also fade the background
                var backgroundFadeTask = this.FadeTo(0.3, 400, Easing.CubicOut);

                // Wait for all animations to complete
                await Task.WhenAll(fadeTask, scaleTask, translateTask, backgroundFadeTask);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Exit animation failed: {ex.Message}");
        }
    }

    private void Button_Clicked(object sender, EventArgs e)
    {
        PubSub.Hub.Default.Publish("RefreshFriendsList");
        Navigation.PopModalAsync(false);
    }
}
