@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Conversation 
@using Platform.Client.Services.Features.Conversation
@page "/chat"
@inherits ListingBase<ChatThreadsListingViewModel, ChatThreadsListingBusinessObject,
                ChatThreadsFilterViewModel, ChatThreadsFilterBusinessObject, IChatThreadsListingDataService>

<div class="min-h-screen bg-chat animate-fade-in">
    <div class="mobile-header">
        <div class="touch-spacing">
            <!-- Title -->
            <div class="mb-4">
                <h1 class="text-responsive-xl font-semibold text-primary">Messages</h1>
            </div>


            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" aria-hidden="true">
                    <svg class="h-4 w-4 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <input type="text"
                       @bind="FilterViewModel.SearchText"
                       @bind:event="oninput"
                       @onkeyup="OnSearchKeyUp"
                       placeholder="Search conversations..."
                       class="form-control pl-10 pr-10 rounded-full touch-target"
                       aria-label="Search conversations" />
                @if (!string.IsNullOrEmpty(FilterViewModel.SearchText))
                {
                    <button @onclick="ClearSearch"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-primary-500 hover:text-primary transition-theme touch-target-sm focus-ring-primary"
                            aria-label="Clear search">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                }
            </div>
        </div>
    </div>

    <!-- Content Section - WhatsApp Style -->
    <div class="bg-subtle-contrast min-h-screen">
        @if (!string.IsNullOrEmpty(Error))
        {
            <!-- Error State - Minimalistic -->
            <div class="p-4">
                <div class="bg-secondary-50 border border-secondary-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 text-secondary-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-secondary-600">Error loading conversations</h3>
                            <p class="mt-1 text-sm text-secondary">@Error</p>
                        </div>
                    </div>
                    <button @onclick="RefreshItems"
                            class="btn-secondary mt-3">
                        Try Again
                    </button>
                </div>
            </div>
        }
       
         @if (!string.IsNullOrEmpty(FilterViewModel.SearchText))
            {
                <div class="px-4 py-3 bg-muted border-b border-border">
                    <p class="text-sm text-secondary">
                        <span class="font-medium">@TotalRecords</span> conversations found for
                        <span class="font-medium">"@FilterViewModel.SearchText"</span>
                        <button @onclick="ClearSearch" class="ml-2 text-accent hover:underline transition-theme">
                            Clear search
                        </button>
                    </p>
                </div>
            }

            <div class="divide-y divide-border">
                @foreach (var thread in Items)
                {
                    <div @onclick="() => OpenChat(thread)"
                         class="chat-card">
                        <div class="flex items-center space-x-3 w-full">
                            <!-- Avatar -->
                            <div class="relative flex-shrink-0">
                                @if (!string.IsNullOrEmpty(thread.Avatar))
                                {
                                    <img src="@thread.Avatar" alt="@thread.Name"
                                         class="avatar-image avatar-lg h-12 w-12 border border-primary-400" />
                                }
                                else
                                {
                                    <div class="avatar avatar-lg bg-primary-300">
                                        <span class="avatar-initials text-white">@GetInitials(thread.Name)</span>
                                    </div>
                                }
                                <!-- Online Status Indicator -->
                                <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-secondary-600 border-2 border-surface rounded-full"></div>
                            </div>

                            <!-- Chat Info -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-base font-medium text-primary-900 dark:text-white truncate">
                                        @thread.Name
                                    </h3>
                                    <div class="flex items-center space-x-2 flex-shrink-0 ml-2">
                                        @if (HasUnreadMessages(thread))
                                        {
                                            <div class="w-2 h-2 bg-primary-500 rounded-full"></div>
                                        }
                                        <span class="text-xs text-primary-500 dark:text-primary-400">
                                            @GetFormattedTime(thread.LastMessageTime)
                                        </span>
                                    </div>
                                </div>

                                @if (!string.IsNullOrEmpty(thread.LastMessage))
                                {
                                    <p class="text-sm text-primary-500 dark:text-primary-400 truncate mt-0.5">
                                        @thread.LastMessage
                                    </p>
                                }
                                else
                                {
                                    <p class="text-sm text-primary-400 dark:text-primary-500 italic mt-0.5">
                                        No messages yet
                                    </p>
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>
    </div>
</div>
