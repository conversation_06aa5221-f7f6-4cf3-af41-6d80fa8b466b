using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Friends;
using System.ComponentModel.DataAnnotations;

namespace Platform.Client.Services.Features.Friends;

public class FriendProfileFormViewModel : ObservableBase
{
    [Required]
    public string? Id { get; set; }

    [Required]
    [StringLength(450)]
    public string FriendName { get; set; } = string.Empty;

    [StringLength(500)]
    public string? Tagline { get; set; }

    private string? _avatarData;

    public string? AvatarData
    {
        get { return _avatarData; }
        set { SetField(ref _avatarData, value); }
    }

}
