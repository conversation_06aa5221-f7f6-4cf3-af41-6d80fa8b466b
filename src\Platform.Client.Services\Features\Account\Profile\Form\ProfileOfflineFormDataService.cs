using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Account;
using DeepMessage.Client.Common.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Platform.Client.Services.Features.Account;

public class ProfileOfflineFormDataService : IProfileFormDataService
{
    private readonly AppDbContext _context;
    private readonly ILogger<ProfileOfflineFormDataService> _logger;

    public ProfileOfflineFormDataService(AppDbContext context, ILogger<ProfileOfflineFormDataService> logger)
    {
        _context = context;
        _logger = logger;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(ProfileFormBusinessObject formBusinessObject)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(formBusinessObject.Id, "User ID is required");

            var user = await _context.ApplicationUsers.FirstOrDefaultAsync(u => u.Id == formBusinessObject.Id);
            if (user == null)
            {
                // Create new user if not exists
                user = new ApplicationUser
                {
                    Id = formBusinessObject.Id,
                    NickName = formBusinessObject.DisplayName,
                    AvatarData = formBusinessObject.AvatarData,
                    AvatarDescription = formBusinessObject.AvatarDescription
                };
                _context.ApplicationUsers.Add(user);
            }
            else
            {
                // Update existing user
                user.NickName = formBusinessObject.DisplayName;
                if (!string.IsNullOrEmpty(formBusinessObject.AvatarData))
                {
                    user.AvatarData = formBusinessObject.AvatarData;
                }
                if (!string.IsNullOrEmpty(formBusinessObject.AvatarDescription))
                {
                    user.AvatarDescription = formBusinessObject.AvatarDescription;
                }
                _context.ApplicationUsers.Update(user);
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Profile saved to local database for user {UserId}", user.Id);
            return JsonSerializer.Serialize(new {
                success = true,
                message = "Profile saved to local database successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving profile to local database for user {UserId}", formBusinessObject.Id);
            throw;
        }
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<ProfileFormBusinessObject> GetItemByIdAsync(string id)
    {
        try
        {
            var user = await _context.ApplicationUsers.FirstAsync(u => u.Id == id);
            return new ProfileFormBusinessObject
            {
                Id = user.Id,
                DisplayName = user.NickName,
                AvatarData = user.AvatarData,
                AvatarDescription = user.AvatarDescription
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving profile from local database for user {UserId}", id);
            throw;
        }
    }
}
