@page "/reset-password"
@page "/reset-password/{Token}"
@using System.ComponentModel.DataAnnotations

<!-- Main Container -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-6">
                <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-6 6h-2.5a2.5 2.5 0 01-2.5-2.5 2.5 2.5 0 012.5-2.5H16a2 2 0 002-2m-7 4v4m0 0H9m2 0h2m-2-4a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Reset your password</h2>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Enter your new password below
            </p>
        </div>

        @if (!isTokenValid)
        {
            <!-- Invalid Token Message -->
            <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg px-8 py-8 text-center">
                <div class="mx-auto h-16 w-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-6">
                    <svg class="h-8 w-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Invalid or Expired Link
                </h3>
                
                <p class="text-gray-600 dark:text-gray-400 mb-6">
                    This password reset link is invalid or has expired. Please request a new one.
                </p>
                
                <a href="/forgot-password" 
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                    Request New Reset Link
                </a>
            </div>
        }
        else if (!passwordReset)
        {
            <!-- Reset Password Form -->
            <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg px-8 py-8 space-y-6">
                <EditForm Model="resetPasswordModel" OnValidSubmit="HandleSubmit">
                    <DataAnnotationsValidator />
                    
                    <div class="space-y-6">
                        <!-- New Password Field -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                New Password
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                </div>
                                <InputText @bind-Value="resetPasswordModel.Password" 
                                           id="password"
                                           type="@(showPassword ? "text" : "password")"
                                           placeholder="Enter your new password"
                                           class="block w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" />
                                <button type="button" @onclick="TogglePasswordVisibility"
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    @if (showPassword)
                                    {
                                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                        </svg>
                                    }
                                    else
                                    {
                                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    }
                                </button>
                            </div>
                            <ValidationMessage For="@(() => resetPasswordModel.Password)" class="mt-1 text-sm text-red-600 dark:text-red-400" />
                            
                            <!-- Password Strength Indicator -->
                            @if (!string.IsNullOrEmpty(resetPasswordModel.Password))
                            {
                                <div class="mt-2">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600 dark:text-gray-400">Password strength:</span>
                                        <span class="font-medium" style="color: @GetPasswordStrengthColor()">
                                            @GetPasswordStrengthText()
                                        </span>
                                    </div>
                                    <div class="mt-1 w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                        <div class="h-2 rounded-full transition-all duration-300" 
                                             style="width: @(GetPasswordStrength() * 25)%; background-color: @GetPasswordStrengthColor()"></div>
                                    </div>
                                </div>
                            }
                        </div>

                        <!-- Confirm Password Field -->
                        <div>
                            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Confirm New Password
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                </div>
                                <InputText @bind-Value="resetPasswordModel.ConfirmPassword" 
                                           id="confirmPassword"
                                           type="@(showConfirmPassword ? "text" : "password")"
                                           placeholder="Confirm your new password"
                                           class="block w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" />
                                <button type="button" @onclick="ToggleConfirmPasswordVisibility"
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    @if (showConfirmPassword)
                                    {
                                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                        </svg>
                                    }
                                    else
                                    {
                                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    }
                                </button>
                            </div>
                            <ValidationMessage For="@(() => resetPasswordModel.ConfirmPassword)" class="mt-1 text-sm text-red-600 dark:text-red-400" />
                            @if (!string.IsNullOrEmpty(resetPasswordModel.ConfirmPassword))
                            {
                                <div class="mt-1 flex items-center">
                                    @if (PasswordsMatch())
                                    {
                                        <svg class="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm text-green-600 dark:text-green-400">Passwords match</span>
                                    }
                                    else
                                    {
                                        <svg class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm text-red-600 dark:text-red-400">Passwords do not match</span>
                                    }
                                </div>
                            }
                        </div>

                        <!-- Submit Button -->
                        <div>
                            <button type="submit" 
                                    disabled="@(isWorking || !CanSubmit())"
                                    class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                                @if (isWorking)
                                {
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span> Resetting password...</span>
                                }
                                else
                                {
                                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                        <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                        </svg>
                                    </span>
                                    <span> Reset Password</span>
                                }
                            </button>
                        </div>

                        <!-- Error Display -->
                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                                <div class="flex">
                                    <svg class="h-5 w-5 text-red-600 dark:text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div>
                                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                                        <p class="mt-1 text-sm text-red-700 dark:text-red-300">@errorMessage</p>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </EditForm>
            </div>
        }
        else
        {
            <!-- Success Message -->
            <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg px-8 py-8 text-center">
                <div class="mx-auto h-16 w-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-6">
                    <svg class="h-8 w-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Password Reset Successful
                </h3>
                
                <p class="text-gray-600 dark:text-gray-400 mb-6">
                    Your password has been successfully reset. You can now sign in with your new password.
                </p>
                
                <a href="/signin" 
                   class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                    Sign In Now
                </a>
            </div>
        }

        <!-- Back to Sign In Link -->
        <div class="text-center">
            <p class="text-sm text-gray-600 dark:text-gray-400">
                Remember your password?
                <a href="/signin" class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200">
                    Back to sign in
                </a>
            </p>
        </div>
    </div>
</div>

@code {
    [Parameter] public string? Token { get; set; }

    private ResetPasswordModel resetPasswordModel = new();
    private bool isWorking = false;
    private bool passwordReset = false;
    private bool isTokenValid = true;
    private string errorMessage = string.Empty;
    private bool showPassword = false;
    private bool showConfirmPassword = false;

    public class ResetPasswordModel
    {
        [Required(ErrorMessage = "Password is required")]
        [MinLength(8, ErrorMessage = "Password must be at least 8 characters long")]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "Please confirm your password")]
        [Compare(nameof(Password), ErrorMessage = "Passwords do not match")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    protected override async Task OnInitializedAsync()
    {
        // Validate the reset token
        if (string.IsNullOrEmpty(Token))
        {
            isTokenValid = false;
            return;
        }

        // In production, validate the token with your authentication service
        // For demo purposes, we'll accept any non-empty token
        isTokenValid = !string.IsNullOrWhiteSpace(Token);
    }

    private async Task HandleSubmit()
    {
        try
        {
            isWorking = true;
            errorMessage = string.Empty;
            StateHasChanged();

            // Simulate API call to reset password
            await Task.Delay(2000); // Simulate network delay

            // In production, this would call a password reset service with the token
            // For demo purposes, we'll always succeed
            passwordReset = true;
        }
        catch (Exception ex)
        {
            errorMessage = "Unable to reset password. Please try again later.";
        }
        finally
        {
            isWorking = false;
            StateHasChanged();
        }
    }

    private void TogglePasswordVisibility()
    {
        showPassword = !showPassword;
    }

    private void ToggleConfirmPasswordVisibility()
    {
        showConfirmPassword = !showConfirmPassword;
    }

    private bool PasswordsMatch()
    {
        return !string.IsNullOrEmpty(resetPasswordModel.Password) &&
               resetPasswordModel.Password == resetPasswordModel.ConfirmPassword;
    }

    private bool CanSubmit()
    {
        return !string.IsNullOrWhiteSpace(resetPasswordModel.Password) &&
               resetPasswordModel.Password.Length >= 8 &&
               PasswordsMatch() &&
               GetPasswordStrength() >= 2; // Require at least medium strength
    }

    private int GetPasswordStrength()
    {
        if (string.IsNullOrEmpty(resetPasswordModel.Password))
            return 0;

        int score = 0;
        var password = resetPasswordModel.Password;

        // Length check
        if (password.Length >= 8) score++;
        if (password.Length >= 12) score++;

        // Character variety checks
        if (System.Text.RegularExpressions.Regex.IsMatch(password, @"[a-z]")) score++; // lowercase
        if (System.Text.RegularExpressions.Regex.IsMatch(password, @"[A-Z]")) score++; // uppercase
        if (System.Text.RegularExpressions.Regex.IsMatch(password, @"[0-9]")) score++; // numbers
        if (System.Text.RegularExpressions.Regex.IsMatch(password, @"[^a-zA-Z0-9]")) score++; // special characters

        return Math.Min(score, 4); // Cap at 4 for UI purposes
    }

    private string GetPasswordStrengthText()
    {
        return GetPasswordStrength() switch
        {
            0 => "",
            1 => "Weak",
            2 => "Fair",
            3 => "Good",
            4 => "Strong",
            _ => ""
        };
    }

    private string GetPasswordStrengthColor()
    {
        return GetPasswordStrength() switch
        {
            0 => "transparent",
            1 => "#ef4444", // red
            2 => "#f59e0b", // amber
            3 => "#10b981", // emerald
            4 => "#059669", // emerald-600
            _ => "transparent"
        };
    }
}
