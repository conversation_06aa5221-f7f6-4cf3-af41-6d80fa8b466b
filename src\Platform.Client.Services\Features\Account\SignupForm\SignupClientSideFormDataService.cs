﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Account;
using Platform.Client.Services.Services;
using Platform.Framework.Core;
using System.Text.Json;
using System.Security.Claims;

namespace ModelFury.Briefly.MobileApp.Features.Account;
public class SignupClientSideFormDataService : ISignupFormDataService
{
    private readonly BaseHttpClient _httpClient;
    private readonly IClientEncryptionService _encryptionService;
    private readonly ILocalStorageService _localStorageService;
    private readonly ISecureKeyManager _secureKeyManager;

    public SignupClientSideFormDataService(BaseHttpClient context, IClientEncryptionService encryptionService, ILocalStorageService localStorageService, ISecureKeyManager secureKeyManager)
    {
        _httpClient = context;
        _encryptionService = encryptionService;
        _localStorageService = localStorageService;
        _secureKeyManager = secureKeyManager;
    }
    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(SignupFormBusinessObject formBusinessObject)
    {
        return await _httpClient.PostAsJsonAsync<string>($"api/SignupsForm/Save", formBusinessObject);
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<SignupFormBusinessObject> GetItemByIdAsync(string id)
    {
        return await _httpClient.GetFromJsonAsync<SignupFormBusinessObject>($"api/SignupsForm/GetItemById?id=" + id);
    }
}
