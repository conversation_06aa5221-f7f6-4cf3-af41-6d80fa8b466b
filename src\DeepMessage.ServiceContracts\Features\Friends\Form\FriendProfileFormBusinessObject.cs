using DeepMessage.Framework.Core;
using System.ComponentModel.DataAnnotations;

namespace DeepMessage.ServiceContracts.Features.Friends;

public class FriendProfileFormBusinessObject
{
    [Required]
    public string? Id { get; set; }

    [Required]
    [StringLength(450)]
    public string FriendName { get; set; } = string.Empty;

    [StringLength(500)]
    public string? Tagline { get; set; }

    /// <summary>
    /// Stores the generated avatar image data as base64 string
    /// </summary>
    public string? AvatarData { get; set; }

     
}
