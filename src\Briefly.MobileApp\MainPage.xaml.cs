﻿using Microsoft.Maui.Platform;
#if ANDROID
using Android.Content.Res;
using AndroidX.Core.Content;
#endif

namespace ModelFury.Briefly.MobileApp
{
    public partial class MainPage : ContentPage
    {
        int count = 0;

        public MainPage()
        {
            InitializeComponent();
            ConfigurePage();
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            // Refresh status bar when page appears (handles theme changes)
            SetThemeAwareStatusBar();
        }

        private void ConfigurePage()
        {
            // Set theme-aware status bar colors
            SetThemeAwareStatusBar();
        }

        void SetThemeAwareStatusBar()
        {
#if ANDROID
            var window = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.Window;
            var context = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity;

            if (window != null && context != null)
            {
                window.ClearFlags(Android.Views.WindowManagerFlags.TranslucentStatus);
                window.AddFlags(Android.Views.WindowManagerFlags.DrawsSystemBarBackgrounds);

                // Check if dark mode is enabled
                var isDarkMode = (context.Resources?.Configuration?.UiMode & UiMode.NightMask) == UiMode.NightYes;

                if (isDarkMode)
                {
                    // Dark theme: Dark gray status bar with white text/icons
                    var darkColor = Android.Graphics.Color.ParseColor("#2A2A2A");
                    window.SetStatusBarColor(darkColor);
                    window.SetNavigationBarColor(darkColor);
                     
                }
                else
                {
                    // Light theme: White status bar with black text/icons
                    var lightColor = Android.Graphics.Color.ParseColor("#FFFFFF");
                    window.SetStatusBarColor(lightColor);
                    window.SetNavigationBarColor(lightColor); 
                }
            }
#elif IOS
            // iOS status bar configuration is handled in AppDelegate
            // But we can also configure it here if needed
            if (UIKit.UIApplication.SharedApplication != null)
            {
                var isDarkMode = UIKit.UITraitCollection.CurrentTraitCollection.UserInterfaceStyle == UIKit.UIUserInterfaceStyle.Dark;

                if (isDarkMode)
                {
                    UIKit.UIApplication.SharedApplication.SetStatusBarStyle(UIKit.UIStatusBarStyle.LightContent, false);
                }
                else
                {
                    UIKit.UIApplication.SharedApplication.SetStatusBarStyle(UIKit.UIStatusBarStyle.DarkContent, false);
                }
            }
#endif
        }

        private void OnCounterClicked(object sender, EventArgs e)
        {
            count++;

            if (count == 1)
                CounterBtn.Text = $"Clicked {count} time";
            else
                CounterBtn.Text = $"Clicked {count} times";

            // Test status bar theme on button click
            TestStatusBarTheme();

            SemanticScreenReader.Announce(CounterBtn.Text);
        }

        /// <summary>
        /// Test method to verify theme-aware status bar implementation
        /// Call this method to check if the status bar colors are applied correctly
        /// </summary>
        public void TestStatusBarTheme()
        {
#if ANDROID
            var context = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity;
            if (context != null)
            {
                var isDarkMode = (context.Resources?.Configuration?.UiMode & UiMode.NightMask) == UiMode.NightYes;
                System.Diagnostics.Debug.WriteLine($"[StatusBar Test] Dark mode detected: {isDarkMode}");
                System.Diagnostics.Debug.WriteLine($"[StatusBar Test] Expected status bar color: {(isDarkMode ? "#2A2A2A" : "#FFFFFF")}");
                System.Diagnostics.Debug.WriteLine($"[StatusBar Test] Expected icon color: {(isDarkMode ? "White" : "Black")}");

                // Refresh status bar to ensure current theme is applied
                SetThemeAwareStatusBar();
                System.Diagnostics.Debug.WriteLine("[StatusBar Test] Status bar refreshed with current theme");
            }
#endif
        }
    }

}
