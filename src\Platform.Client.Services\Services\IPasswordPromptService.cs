namespace Platform.Client.Services.Services
{
    /// <summary>
    /// Service for prompting user for password when AES operations are needed
    /// </summary>
    public interface IPasswordPromptService
    {
        /// <summary>
        /// Prompts user for password and returns derived AES key
        /// </summary>
        /// <param name="username">Username for salt derivation</param>
        /// <param name="purpose">Purpose of the password request (for UI display)</param>
        /// <returns>Derived AES key or null if user cancels</returns>
        Task<byte[]?> PromptForPasswordAndDeriveKeyAsync(string username, string purpose);

        /// <summary>
        /// Checks if password prompt is available (UI context exists)
        /// </summary>
        bool IsPasswordPromptAvailable();
    }

    /// <summary>
    /// Default implementation that can be overridden by platform-specific implementations
    /// </summary>
    public class PasswordPromptService : IPasswordPromptService
    {
        private readonly ISecureKeyManager _secureKeyManager;

        public PasswordPromptService(ISecureKeyManager secureKeyManager)
        {
            _secureKeyManager = secureKeyManager;
        }

        public virtual async Task<byte[]?> PromptForPasswordAndDeriveKeyAsync(string username, string purpose)
        {
            // This is a base implementation that should be overridden by platform-specific services
            // For now, return null to indicate password prompt is not available
            await Task.CompletedTask;
            return null;
        }

        public virtual bool IsPasswordPromptAvailable()
        {
            // Override in platform-specific implementations
            return false;
        }
    }
}