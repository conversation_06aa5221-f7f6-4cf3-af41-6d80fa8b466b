<svg width="82" height="82" xmlns="http://www.w3.org/2000/svg" fill="none">
 <!-- Blue rounded square icon -->
 <!-- NEWS text -->
 <!-- Lines to represent article content -->
 <!-- Checkmark -->
 <!-- App name text -->
 <!-- Gradient Definition -->
 <defs>
  <linearGradient y2="0.875" x2="1" y1="-0.125" x1="0" id="blueGradient">
   <stop stop-color="#2684FF"/>
   <stop stop-color="#0052CC" offset="1"/>
  </linearGradient>
 </defs>
 <g>
  <title>Layer 1</title>
  <path stroke="null" id="svg_1" d="m10.82013,25.00001c0,-8.02131 6.95804,-14.76191 15.2381,-14.76191l30.4762,0c8.28006,0 15.2381,6.7406 15.2381,14.76191l0,29.52382c0,8.02131 -6.95804,14.76191 -15.2381,14.76191l-30.4762,0c-8.28006,0 -15.2381,-6.7406 -15.2381,-14.76191l0,-29.52382z" opacity="undefined" fill="url(#blueGradient)"/>
  <text style="cursor: move;" id="svg_2" font-weight="bold" font-size="14" font-family="Arial, sans-serif" fill="#0052CC" y="32.09524" x="21.38098"/>
  <rect stroke="null" id="svg_3" fill="white" rx="2" height="3.04762" width="29.04763" y="38.90477" x="24.71431"/>
  <path stroke="null" id="svg_4" d="m25.4286,46.85715c0,-0.82801 0.68027,-1.52381 1.4898,-1.52381l17.87756,0c0.80952,0 1.4898,0.6958 1.4898,1.52381l0,0c0,0.82801 -0.68027,1.52381 -1.4898,1.52381l-17.87756,0c-0.80952,0 -1.4898,-0.6958 -1.4898,-1.52381l0,0z" opacity="undefined" fill="white"/>
  <path stroke="white" id="svg_5" stroke-linejoin="round" stroke-linecap="round" fill="none" stroke-width="3" d="m48.92596,51.38511l2.69461,3.58844l8.55936,-8.69313"/>
  <path stroke="null" id="svg_7" d="m25.79896,53.37831c0,-0.66269 0.43264,-1.21958 0.94747,-1.21958l11.36963,0c0.51484,0 0.94747,0.55688 0.94747,1.21958l0,0c0,0.66269 -0.43263,1.21958 -0.94747,1.21958l-11.36963,0c-0.51483,0 -0.94747,-0.55688 -0.94747,-1.21958l0,0z" opacity="undefined" fill="white"/>
  <text transform="matrix(0.691028 0 0 0.662367 3.8979 3.12466)" stroke="null" id="svg_10" font-weight="bold" font-size="22" font-family="Arial, Helvetica, sans-serif" fill="#0A2240" y="44.83782" x="26.78206">News</text>
 </g>
</svg>