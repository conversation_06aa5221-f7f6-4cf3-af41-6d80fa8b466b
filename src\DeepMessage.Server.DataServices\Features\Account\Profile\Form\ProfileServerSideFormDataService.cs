﻿using DeepMessage.Framework.Core;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace DeepMessage.Server.DataServices.Features.Account;
public class ProfileServerSideFormDataService : IProfileFormDataService
{
    private readonly AppDbContext _context;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<ProfileServerSideFormDataService> _logger;

    public ProfileServerSideFormDataService(AppDbContext context, UserManager<ApplicationUser> userManager, ILogger<ProfileServerSideFormDataService> logger)
    {
        _context = context;
        _userManager = userManager;
        _logger = logger;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(ProfileFormBusinessObject formBusinessObject)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(formBusinessObject.Id, "User ID is required");

            var user = await _userManager.FindByIdAsync(formBusinessObject.Id);
            ArgumentNullException.ThrowIfNull(user, "User not found");

            // Update user profile data
            user.UserName = formBusinessObject.DisplayName;
            if (!string.IsNullOrEmpty(formBusinessObject.AvatarData))
            {
                user.AvatarData = formBusinessObject.AvatarData;
            }
            if (!string.IsNullOrEmpty(formBusinessObject.AvatarDescription))
            {
                user.AvatarDescription = formBusinessObject.AvatarDescription;
            }

            var result = await _userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                throw new InvalidOperationException($"Failed to update user profile: {errors}");
            }

            _logger.LogInformation("Profile updated successfully for user {UserId}", user.Id);
            return JsonSerializer.Serialize(new {
                success = true,
                message = "Profile updated successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating profile for user {UserId}", formBusinessObject.Id);
            throw;
        }
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<ProfileFormBusinessObject?> GetItemByIdAsync(string id)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return null;
            }

            return new ProfileFormBusinessObject
            {
                Id = user.Id,
                DisplayName = user.UserName ?? string.Empty,
                AvatarData = user.AvatarData,
                AvatarDescription = user.AvatarDescription
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving profile for user {UserId}", id);
            throw;
        }
    }
}
