﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Account;
using System.Security.Claims;
using Platform.Framework.Core;
using DeepMessage.Client.Common.Data;
using DeepMessage.Cient.Common.Data;

namespace ModelFury.Briefly.MobileApp.Features.Account;
public class ProfileClientSideListingDataService : ClientSideListingDataService<ProfileListingBusinessObject, ProfileFilterBusinessObject>, IProfileListingDataService
{

    private readonly BaseHttpClient _httpClient;
    private readonly ILocalStorageService localStorageService;
    private readonly AppDbContext context;

    public ProfileClientSideListingDataService(BaseHttpClient http,
        ILocalStorageService localStorageService, AppDbContext context)
    {
        _httpClient = http;
        this.localStorageService = localStorageService;
        this.context = context;
    }

    public override IQueryable<ProfileListingBusinessObject> GetQuery(ProfileFilterBusinessObject filterBusinessObject)
    {
        var userId =  Task.Run(()=> localStorageService.GetValue(ClaimTypes.NameIdentifier)).Result;
        return (from u in context.ApplicationUsers
                where u.Id == userId
                select new ProfileListingBusinessObject
                {
                    Id = u.Id,
                    NickName = u.NickName,
                    AvatarData = u.AvatarData,
                    DisplayName = u.DisplayName
                });
    }
}
