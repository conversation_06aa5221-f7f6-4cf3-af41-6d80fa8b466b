@using System.Security.Claims
@inject IJSRuntime JSRuntime

<div class="relative inline-block">
    @if (AvatarData != null && AvatarData.Length > 0)
    {
        <!-- Generated Avatar -->
        <img src="@AvatarData"
             alt="@(DisplayName ?? "User avatar")" 
             class="@GetAvatarClasses()" 
             @onclick="OnAvatarClick" />
    }
    else if (!string.IsNullOrEmpty(FallbackImageUrl))
    {
        <!-- Fallback Image -->
        <img src="@FallbackImageUrl" 
             alt="@(DisplayName ?? "User avatar")" 
             class="@GetAvatarClasses()" 
             @onclick="OnAvatarClick" />
    }
    else
    {
        <!-- Initials Placeholder -->
        <div class="@GetPlaceholderClasses()" @onclick="OnAvatarClick">
            <span class="@GetInitialsClasses()">
                @GetInitials()
            </span>
        </div>
    }

    @if (ShowEditButton && IsEditable)
    {
        <!-- Edit Button Overlay -->
        <button @onclick="OnEditClick" 
                class="absolute bottom-0 right-0 bg-gray-900 text-white rounded-full p-1.5 hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-900 focus:ring-offset-2 transition-colors">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
            </svg>
        </button>
    }
</div>

@code {
    [Parameter] public string? AvatarData { get; set; }
    [Parameter] public string? FallbackImageUrl { get; set; }
    [Parameter] public string? DisplayName { get; set; }
    [Parameter] public AvatarSize Size { get; set; } = AvatarSize.Medium;
    [Parameter] public bool IsEditable { get; set; } = false;
    [Parameter] public bool ShowEditButton { get; set; } = false;
    [Parameter] public bool IsClickable { get; set; } = false;
    [Parameter] public EventCallback OnEditClicked { get; set; }
    [Parameter] public EventCallback OnAvatarClicked { get; set; }

   

    private string GetAvatarClasses()
    {
        var baseClasses = "object-cover rounded-full";
        var sizeClasses = Size switch
        {
            AvatarSize.Small => "w-8 h-8",
            AvatarSize.Medium => "w-12 h-12",
            AvatarSize.Large => "w-16 h-16",
            AvatarSize.ExtraLarge => "w-24 h-24",
            _ => "w-12 h-12"
        };
        
        var interactionClasses = IsClickable ? "cursor-pointer hover:opacity-80 transition-opacity" : "";
        
        return $"{baseClasses} {sizeClasses} {interactionClasses}".Trim();
    }

    private string GetPlaceholderClasses()
    {
        var baseClasses = "rounded-full bg-gray-300 flex items-center justify-center text-gray-700 font-medium";
        var sizeClasses = Size switch
        {
            AvatarSize.Small => "w-8 h-8",
            AvatarSize.Medium => "w-12 h-12",
            AvatarSize.Large => "w-16 h-16",
            AvatarSize.ExtraLarge => "w-24 h-24",
            _ => "w-12 h-12"
        };
        
        var interactionClasses = IsClickable ? "cursor-pointer hover:bg-gray-400 transition-colors" : "";
        
        return $"{baseClasses} {sizeClasses} {interactionClasses}".Trim();
    }

    private string GetInitialsClasses()
    {
        return Size switch
        {
            AvatarSize.Small => "text-xs",
            AvatarSize.Medium => "text-sm",
            AvatarSize.Large => "text-lg",
            AvatarSize.ExtraLarge => "text-2xl",
            _ => "text-sm"
        };
    }

    private string GetInitials()
    {
        if (string.IsNullOrWhiteSpace(DisplayName))
            return "?";

        var parts = DisplayName.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length == 0)
            return "?";

        if (parts.Length == 1)
            return parts[0].Substring(0, Math.Min(2, parts[0].Length)).ToUpper();

        return $"{parts[0][0]}{parts[^1][0]}".ToUpper();
    }

    private async Task OnAvatarClick()
    {
        if (IsClickable && OnAvatarClicked.HasDelegate)
        {
            await OnAvatarClicked.InvokeAsync();
        }
    }

    private async Task OnEditClick()
    {
        if (OnEditClicked.HasDelegate)
        {
            await OnEditClicked.InvokeAsync();
        }
    }
}

@code {
    public enum AvatarSize
    {
        Small,
        Medium,
        Large,
        ExtraLarge
    }
}
