﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Conversation;
namespace DeepMessage.Server.WebApis.Controller.Conversation;
[ApiController, Route("api/[controller]/[action]")]
public class ChatMessageFormController : ControllerBase, IChatMessageFormDataService
{

	private readonly IChatMessageFormDataService dataService;

	public ChatMessageFormController(IChatMessageFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] ChatMessageFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<ChatMessageFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
