﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Configurations;
namespace Platform.Client.Services.Features.Configurations;
public class DeviceTokenClientSideFormDataService : IDeviceTokenFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public DeviceTokenClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(DeviceTokenFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/DeviceTokensForm/Save", formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<DeviceTokenFormBusinessObject> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<DeviceTokenFormBusinessObject>($"api/DeviceTokensForm/GetItemById?id=" + id);
	}
}
