﻿
using System.ComponentModel.DataAnnotations;

namespace DeepMessage.Client.Common.Data
{
    public class ApplicationUser
    {
        [Key, StringLength(450)]
        public string Id { get; set; } = string.Empty;

        [StringLength(450)]
        public string NickName { get; set; } = string.Empty;

        [StringLength(450)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// Display name for the user
        /// </summary>
        [StringLength(450)]
        public string Hash { get; set; } = string.Empty;

        /// <summary>
        /// Stores the generated avatar image data as byte array
        /// </summary>
        public string? AvatarData { get; set; }

        [StringLength(450)]
        public string? AvatarDescription { get; set; }

        public string Pub1 { get; set; } = null!;

        public string Pub2 { get; set; } = null!;
    }
}
